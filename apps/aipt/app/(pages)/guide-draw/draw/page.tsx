"use client";

import { <PERSON><PERSON> } from "@/app/components/common/aipt-button";
import { Panel } from "@/app/components/panels/panel";
import { SketchCanvasRefProvider } from "@repo/core/guide/context/sketch-canvas-context";

import IcSave from "@/public/ic-save.svg";
import { GuideWidget, RawGuideSet } from "@/types/guide-widget";

import { toast } from "@/app/components/common/toast";
import { LocalDraftService } from "@/app/service/local-draft-service";

import fetcher from "@/app/utils/fetcher";
import { mergePathsWithSvg } from "@/app/utils/pathUtils";
import {
  DrawElement,
  GuideWidgetData,
  Line,
} from "@repo/core/types/data/widget-guide";
import { Modal, Spin } from "antd";

import { GuideDrawWrapper } from "@/app/components/draw-player/guide-draw-wrapper";
import { post } from "@/app/utils/fetcher";
import { toH3Parts } from "@repo/core/guide/utils/h3-parts";
import { ReactSketchCanvasRef } from "@repo/react-sketch-canvas";
import { debounce } from "lodash";
import { useSearchParams } from "next/navigation";
import { Suspense, useCallback, useEffect, useMemo, useRef, useState } from "react";
import useSWR from "swr";
import useSWRMutation from "swr/mutation";

type DrawElementWithWait = DrawElement & { wait?: boolean };

// 荧光笔操作场景下，前边画完线后highlighterWaitTime时间内再有新的荧光笔绘制，前边画的消失时间要和最后一个荧光笔消失时间一致
const highlighterWaitTime = 2000;

/**
 * 荧光笔绘制结束后前边记录的荧光笔endFrame处理逻辑
 * @param drawPaths 绘制路径
 * @returns 处理后的绘制路径
 */
const handleLighlighterEndFrame = (
  drawPaths: (DrawElement & { wait?: boolean })[]
): DrawElement[] => {
  if (drawPaths.length === 0) {
    return drawPaths;
  }

  const targetEndFrame = drawPaths[drawPaths.length - 1]?.endFrame;

  // 处理荧光笔路径
  const processedHighlighterPaths = drawPaths.map((path) => {
    const processedPath = { ...path };
    if (path.wait) {
      processedPath.endFrame = targetEndFrame;
      delete processedPath.wait;
    }

    return processedPath;
  });

  return processedHighlighterPaths;
};

const getCurrentPartByBlockIndex = (
  guideWidgetData: GuideWidgetData,
  blockIndex: string
) => {
  const { content } = guideWidgetData;
  const h3Parts = toH3Parts(content);
  return h3Parts[Number(blockIndex)];
};

function DrawPageContent() {
  const sketchCanvasRef = useRef<ReactSketchCanvasRef | null>(null);
  const [strokeColor, setStrokeColor] = useState("#F9453F");
  const [strokeWidth, setStrokeWidth] = useState(4);
  const searchParams = useSearchParams();
  const id = searchParams.get("id");
  const guideWidgetId = searchParams.get("guideWidgetId");
  const blockIndex = searchParams.get("blockIndex");
  const highlighterTimerRef = useRef<NodeJS.Timeout | null>(null);
  // 添加一个ref来存储最新的路径数据，避免异步操作导致的数据不一致
  const latestPathsRef = useRef<DrawElement[]>([]);
  const localDraft = new LocalDraftService({
    guideWidgetSetId: id || "",
    guideWidgetId: guideWidgetId || "",
  });
  useEffect(() => {
    // 进来先清理下，防止之前操作过还有缓存的数据，保证是一个干净的环境
    localDraft.clear("draw");
  }, []);
  // 按h3分组使用画笔的草稿
  const [draft, setDraft] = useState<GuideWidgetData | undefined>(undefined);
  // 本次绘制的路径数量
  const [curAddPathCount, setCurAddPathCount] = useState(0);
  // 画过之后才可以有撤销操作
  const isUndo = useMemo(() => {
    return curAddPathCount > 0 && !!draft;
  }, [curAddPathCount, draft]);
  // 撤销操作的草稿数据，用来实现恢复
  const [undoGuideData, setUndoGuideData] = useState<GuideWidgetData[]>([]);
  const isRedo = useMemo(() => {
    return !!undoGuideData.length;
  }, [undoGuideData]);
  // 获取part集合数据
  const {
    data: fetchedGuideWidgetSetData,
  } = useSWR<RawGuideSet>(
    id
      ? `/api/v1/guideWidgetSet/info?guideWidgetSetId=${id}`
      : null,
    fetcher,
    { revalidateOnFocus: false }
  );
  const { data } = useSWR<GuideWidget>(
    () =>
      guideWidgetId
        ? `/api/v1/guideWidget/info?guideWidgetId=${guideWidgetId}&guideWidgetSetId=${id}`
        : null,
    fetcher,
    {
      revalidateOnFocus: false,
    }
  );
  const partInfo = useMemo(() => {
    if (!fetchedGuideWidgetSetData) return undefined;
    const partIndex = fetchedGuideWidgetSetData.guideWidgetList.findIndex(
      (item) => Number(item.guideWidgetId) === Number(guideWidgetId)
    );
    if (partIndex < 0) return undefined;
    return {
      partIndex,
      totalPartsCount: fetchedGuideWidgetSetData.guideWidgetList.length,
    };
  }, [fetchedGuideWidgetSetData, guideWidgetId]);

  const [modal, contextHolder] = Modal.useModal();

  const videoJson: GuideWidgetData | undefined = useMemo(() => {
    if (!data) return undefined;
    if (!data.videoJson) return undefined;
    // 如果草稿存在，则使用草稿
    if (draft) return draft;
    return typeof data.videoJson === "string"
      ? (JSON.parse(data.videoJson) as GuideWidgetData)
      : data.videoJson;
  }, [draft, data]);

  const h3Parts = useMemo(() => {
    if (!videoJson) return [];
    const { content } = videoJson;
    return toH3Parts(content);
  }, [videoJson]);

  const currentPart = useMemo(() => {
    if (!h3Parts) return [];
    return h3Parts[Number(blockIndex)] || [];
  }, [h3Parts, blockIndex]);

  // 提取content的inFrame和outFrame
  const partFrames = useMemo(() => {
    if (!currentPart) return { inFrame: 0, outFrame: 0 };

    const firstLine = currentPart[0];
    const lastLine = currentPart[currentPart.length - 1];
    let inFrame = firstLine?.inFrame || 0;
    if (firstLine?.inFrame === undefined) {
      inFrame = currentPart[1]?.inFrame || 0;
    }
    const outFrame = lastLine?.outFrame || 0;

    return {
      inFrame,
      outFrame,
    };
  }, [currentPart]);

  const { trigger: submitList, isMutating } = useSWRMutation(
    "/api/v1/guideWidget/save/draw",
    post
  );
  const generateNewDraftByPaths = (updatedPaths: DrawElement[]) => {
    const newPart = currentPart?.map((item: Line) => {
      if (item.tag === "h3") {
        return { ...item, draw: updatedPaths };
      }
      return item;
    });

    // 修改h3Parts
    h3Parts[Number(blockIndex)] = newPart;
    return {
      ...videoJson,
      content: h3Parts.flat(),
    } as GuideWidgetData;
  };
  const handleSubmit = async (type?: "manual" | "auto") => {
    try {
      let localData = localDraft.load<GuideWidgetData>("draw");
      if (!videoJson) return;
      if (!localData) return;

      // 如果还有荧光笔计时器在运行，需要立即处理荧光笔逻辑
      if (highlighterTimerRef.current) {
        // 清理定时器
        clearTimeout(highlighterTimerRef.current);

        // 使用最新的路径数据而不是从本地存储加载
        const updatedPaths = latestPathsRef.current;

        const newDrawPaths: DrawElement[] =
          handleLighlighterEndFrame(updatedPaths);

        localData = generateNewDraftByPaths(newDrawPaths);

        highlighterTimerRef.current = null;
      }

      await submitList({
        guideWidgetSetId: Number(id) || "",
        guideWidgetId: Number(guideWidgetId),
        videoJson: JSON.stringify(localData),
      });
      if (type === "manual") {
        toast.success("保存成功");
        // 清空草稿
        localDraft.clear("draw");
        return true;
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "保存失败");
      console.error("Error exporting paths:", error);
      return false;
    }
  };

  const startHighlighterTimer = () => {
    // 如果之前有定时器，则清除，重新开始计时
    if (highlighterTimerRef.current) {
      clearTimeout(highlighterTimerRef.current);
    }

    highlighterTimerRef.current = setTimeout(() => {
      try {
        // 使用最新的路径数据而不是从本地存储加载
        const updatedPaths = latestPathsRef.current;

        // 如果到时间了重置之前记录的荧光笔endFrame
        const newDrawPaths = handleLighlighterEndFrame(updatedPaths);

        const newDraft = generateNewDraftByPaths(newDrawPaths);

        if (newDraft) {
          localDraft.save<GuideWidgetData>("draw", newDraft);
          setDraft(newDraft);
          // 更新最新路径引用
          latestPathsRef.current = newDrawPaths;
        }
      } catch (error) {
        console.error("荧光笔定时器处理出错:", error);
      } finally {
        // 重置 timer 为 null
        highlighterTimerRef.current = null;
      }
    }, highlighterWaitTime);
  };

  // 处理路径变化，这里只会出现增加路径的情况
  const handlePathsChange = (updatedPaths: DrawElement[] | null) => {
    // updatedPaths为null，表示开始新的绘制
    if (updatedPaths === null) {
      if (highlighterTimerRef.current) {
        // 开始新的绘制，重置定时器
        clearTimeout(highlighterTimerRef.current);
        highlighterTimerRef.current = null;
      }
      return;
    }
    setCurAddPathCount(curAddPathCount + 1);
    // 有新路径进来清空记录
    setUndoGuideData([]);

    const exportPaths = async () => {
      try {
        if (!videoJson) return;

        let newDrawPaths: DrawElement[] = [...updatedPaths]; // 创建副本避免直接修改
        const lastPath = updatedPaths[updatedPaths.length - 1];

        // 最新一条是荧光笔绘制且绘制完毕后，启动定时器
        if (lastPath?.endFrame) {
          // 启动新的定时器
          startHighlighterTimer();
        } else {
          // 如果最新一条不是荧光笔绘制
          const processedHighlighterPaths = handleLighlighterEndFrame(
            newDrawPaths.slice(0, -1)
          );
          newDrawPaths = [
            ...processedHighlighterPaths,
            ...((lastPath as DrawElement) ? [lastPath as DrawElement] : []),
          ];
        }

        const exportSvg = await sketchCanvasRef.current?.exportSvg();
        const mergedPaths: DrawElement[] = mergePathsWithSvg(
          newDrawPaths,
          exportSvg || ""
        );
        const newDraft = generateNewDraftByPaths(mergedPaths);
        localDraft.save<GuideWidgetData>("draw", newDraft);
        setDraft(newDraft);
        // 更新最新路径引用
        latestPathsRef.current = mergedPaths;
      } catch (error) {
        console.error("Error exporting paths:", error);
      }
    };

    exportPaths();
  };

  // 添加一个清理函数，在组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (highlighterTimerRef.current) {
        clearTimeout(highlighterTimerRef.current);
      }
    };
  }, []);

  // 保存稿件
  const BtnSave = () => {
    return (
      <Button
        type="default"
        size="sm"
        className="ml-auto bg-[#E5E7EB] text-[#4B5563]"
        loading={isMutating}
        icon={<IcSave />}
        onClick={() => handleSubmit("manual")}
      >
        保存稿件
      </Button>
    );
  };

  const backHandler = async () => {
    const localData = localDraft.load<GuideWidgetData>("draw");
    // 有草稿二次确认
    if (localData) {
      const res = await modal.confirm({
        title: "确认返回",
        content: "当前有未保存的草稿，确定要返回上一页吗？",
        okText: "保存",
        cancelText: "不保存",
      });
      if (res) {
        const saveResult = await handleSubmit("manual");
        if (saveResult) {
          window.history.back();
        }
        return;
      }
      localDraft.clear("draw");
      window.history.back();
    } else {
      // 无草稿直接返回
      window.history.back();
    }
  };

  const handleUndo = () => {
    if (!isUndo) return;
    if (!draft) return;
    // 如果还有定时器，则清除
    if (highlighterTimerRef.current) {
      clearTimeout(highlighterTimerRef.current);
      highlighterTimerRef.current = null;
    }
    // 先记录当前的草稿数据
    setUndoGuideData([...undoGuideData, draft]);

    const currentPart =
      getCurrentPartByBlockIndex(draft, blockIndex as string) || [];
    const newDrawPaths: DrawElementWithWait[] =
      currentPart.find((item: Line) => item.tag === "h3")?.draw?.slice(0, -1) ||
      [];
    // const newPart = currentPart?.map((item: Line) => {
    //   if (item.tag === "h3") {
    //     return { ...item, draw: item?.draw?.slice(0, -1) };
    //   }
    //   return item;
    // }) || [];
    // h3Parts[Number(blockIndex)] = newPart;
    const isWait = newDrawPaths[newDrawPaths.length - 1]?.wait;
    if (isWait) {
      startHighlighterTimer();
      latestPathsRef.current = newDrawPaths;
    }
    const newDraft = generateNewDraftByPaths(newDrawPaths);

    // 更新本地存储和状态
    localDraft.save<GuideWidgetData>("draw", newDraft);
    setDraft(newDraft);
    setCurAddPathCount(curAddPathCount - 1);
  };
  const handleRedo = () => {
    if (!isRedo) return;
    const newDraft = undoGuideData[undoGuideData.length - 1] as GuideWidgetData;

    const currentPart =
      getCurrentPartByBlockIndex(newDraft, blockIndex as string) || [];
    const newDrawPaths: DrawElementWithWait[] =
      currentPart?.find((item: Line) => item.tag === "h3")?.draw || [];
    const isWait = newDrawPaths[newDrawPaths.length - 1]?.wait;
    if (isWait) {
      startHighlighterTimer();
      latestPathsRef.current = newDrawPaths;
    }
    // 更新本地存储和状态
    localDraft.save<GuideWidgetData>("draw", newDraft);
    setDraft(newDraft);
    setUndoGuideData(undoGuideData.slice(0, -1));
    setCurAddPathCount(curAddPathCount + 1);
  };
  const errorMessage = useCallback(debounce(() => {
    toast.error("当前数据中没有H3，请检查", {  });
  }, 1000), [])
  if (currentPart.length && currentPart?.[0]?.tag !== "h3") {
    errorMessage();
  }
  return (
    <Spin spinning={!videoJson}>
      <SketchCanvasRefProvider
        canvasRef={sketchCanvasRef}
        strokeColor={strokeColor}
        strokeWidth={strokeWidth}
        setStrokeColor={setStrokeColor}
        setStrokeWidth={setStrokeWidth}
      >
        <Panel
          contentClassName="px-0"
          key={`draw${guideWidgetId}-${blockIndex}`}
          className="select-none"
          // headerClassName="h-20"
          title="课程圈画"
          loading={false}
          controls={<BtnSave />}
        >
          <div className="h-full w-full">
            {videoJson &&
              currentPart.length > 0 &&
              partInfo && (
                <GuideDrawWrapper
                  videoJson={{
                    ...videoJson,
                    content: currentPart,
                  }}
                  width={1000}
                  height={600}
                  onDrawChange={handlePathsChange}
                  inFrame={partFrames.inFrame}
                  outFrame={partFrames.outFrame}
                  backHandler={backHandler}
                  isUndo={isUndo}
                  isRedo={isRedo}
                  handleUndo={handleUndo}
                  handleRedo={handleRedo}
                  partIndex={partInfo?.partIndex || 0}
                  totalPartsCount={partInfo?.totalPartsCount || 0}
                />
              )}
          </div>
        </Panel>
        {contextHolder}
      </SketchCanvasRefProvider>
    </Spin>
  );
}

export default function DrawPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <DrawPageContent />
    </Suspense>
  );
}
