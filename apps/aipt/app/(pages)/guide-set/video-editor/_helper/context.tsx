import { PlayerRef } from "@remotion/player";
import { createContext, RefObject, useContext } from "react";
import {
  ActiveNodeVal,
  activeNodeValInt,
  GuideWidgetDataExt,
  GuideWidgetDataNew,
} from "./guid-helper";
import { cloneDeep } from "lodash";
import { Signal } from "@preact-signals/safe-react";
import { GuideWidget } from "@/types/guide-widget";

// interface TimeTick {
//   time: number;
//   percent: number;
//   label: string;
// }

interface VideoEditorConfig {
  partData: GuideWidget | null;
  guideData: GuideWidgetDataExt | null;
  playerRef: RefObject<PlayerRef | null> | null;
  currentFrameSignal: Signal<number>;
  currentTimeSignal: Signal<number>;
  activeNode: ActiveNodeVal;
  isPlaying: boolean;
  playbackRate: number;
  setPlaybackRate: (rate: number) => void;
  setActiveNode: (node: ActiveNodeVal) => void;
  updateGuideData: (data: GuideWidgetDataExt) => void;
}

const editorConfig: VideoEditorConfig = {
  partData: null,
  guideData: null,
  playerRef: null,
  currentFrameSignal: new Signal(0),
  currentTimeSignal: new Signal(0),
  activeNode: cloneDeep(activeNodeValInt),
  isPlaying: false,
  playbackRate: 1,
  setPlaybackRate: () => {},
  setActiveNode: () => {},
  updateGuideData: () => {},
};
const VideoEditorContext = createContext<VideoEditorConfig>(editorConfig);
export const useVideoEditor = () => useContext(VideoEditorContext);
export const VideoEditorProvider = ({
  children,
  ...rest
}: VideoEditorConfig & { children: React.ReactNode }) => {
  return (
    <VideoEditorContext.Provider value={rest}>
      {children}
    </VideoEditorContext.Provider>
  );
};
