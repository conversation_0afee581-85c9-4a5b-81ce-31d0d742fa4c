import { Button } from "@/app/components/ui/button";
import { Slider } from "@/app/components/ui/slider1";
import { Signal, useComputed, useSignal } from "@preact-signals/safe-react";
import { Maximize, Pause, Play, Volume2, VolumeX } from "lucide-react";
import { useState } from "react";
import { useVideoEditor } from "../_helper/context";
import { timeFormat } from "../_helper/time-segment";

interface TimeBarProps {
  currentTime: Signal<number>; // 当前播放时间（秒）
  duration: number; // 总时长（秒）
  height?: number; // 进度条高度，默认8px
  bgColor?: string; // 背景色
  progressColor?: string; // 进度色
  showText?: boolean; // 是否显示时间文本
}
const playbackRateOptions: Array<number> = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

const TimeBar: React.FC<TimeBarProps> = ({ currentTime, duration }) => {
  const { playerRef, guideData } = useVideoEditor();
  const percent = useComputed(() =>
    duration > 0 ? Math.min(currentTime.value / duration, 1) : 0
  );

  // 处理拖动进度条
  const handleProgressDragStart = (e: React.PointerEvent<HTMLDivElement>) => {
    if (!playerRef?.current || !guideData) return;

    let hasDragged = false;
    const startX = e.clientX;
    const progressBar = e.currentTarget; // 保存进度条元素的引用

    const handleDrag = (moveEvent: PointerEvent) => {
      // 只有在实际移动时才阻止默认行为和设置拖动状态
      if (!hasDragged && Math.abs(moveEvent.clientX - startX) > 3) {
        hasDragged = true;
        moveEvent.preventDefault();
      }

      if (!hasDragged) return;

      const rect = progressBar.getBoundingClientRect();
      // 计算拖拽位置相对于进度条的百分比
      let pos = (moveEvent.clientX - rect.left) / rect.width;
      // 限制在0-1范围内
      pos = Math.max(0, Math.min(1, pos));
      const newTime = pos * duration;

      // 更新播放器位置
      const player = playerRef.current;
      if (player) {
        const fps = guideData.avatar.fps;
        const frame = Math.round(newTime * fps);
        // console.log("拖动进度条:", { pos, newTime, frame });
        player.seekTo(frame);
      }
    };

    const handleUp = (upEvent: PointerEvent) => {
      document.removeEventListener("pointermove", handleDrag);
      document.removeEventListener("pointerup", handleUp);

      // 如果没有拖动，则执行点击逻辑
      if (!hasDragged) {
        const rect = progressBar.getBoundingClientRect();
        const pos = (upEvent.clientX - rect.left) / rect.width;
        const newTime = pos * duration;

        // console.log("点击进度条:", { pos, newTime, duration });

        const player = playerRef.current;
        if (player) {
          const fps = guideData.avatar.fps;
          const frame = Math.round(newTime * fps);
          // console.log("跳转到帧:", frame);
          player.seekTo(frame);
        }
      }
    };

    document.addEventListener("pointermove", handleDrag);
    document.addEventListener("pointerup", handleUp);
  };

  return (
    <div
      className="relative cursor-pointer py-2"
      onPointerDown={handleProgressDragStart}
      style={{ userSelect: "none" }}
    >
      <div className="bg-surface-tertiary relative h-2 overflow-hidden rounded-full">
        <div
          className="bg-accent-coral absolute left-0 top-0 h-full rounded-full transition-all"
          style={{ width: `${percent.value * 100}%` }}
        />
      </div>
    </div>
  );
};

const VolumeBar = () => {
  const { playerRef } = useVideoEditor();
  const [isMuted, setIsMuted] = useState(false);
  // signal 也没能阻止当前组件的re-render，不知道为啥
  const volumeSignal = useSignal<number>(1);
  const syncVolume = (val: number) => {
    const play = playerRef?.current!;
    volumeSignal.value = val;
    play.setVolume(val);
    if (val === 0) {
      setIsMuted(true);
    } else {
      setIsMuted(false);
    }
  };

  // 使用 useComputed 创建一个派生信号，包含数组值
  const volumeArraySignal = useComputed<Array<number>>(() => [
    volumeSignal.value,
  ]);

  const toggleMute = () => {
    if (isMuted) {
      syncVolume(1);
    } else {
      syncVolume(0);
    }
  };

  const onValueChange = ([val]: Array<number>) => {
    syncVolume(val!);
  };
  return (
    <div className="volume-bar flex items-center gap-2">
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleMute}
        className="text-text-secondary h-8 w-8 hover:text-white"
      >
        {isMuted || volumeSignal.value === 0 ? (
          <VolumeX className="h-4 w-4" />
        ) : (
          <Volume2 className="h-4 w-4" />
        )}
      </Button>
      <div className="w-20">
        <Slider
          min={0}
          max={1}
          step={0.01}
          value={volumeArraySignal.value}
          onValueChange={onValueChange}
          className="bg-gradient-surface border-border-subtle shadow-subtle hover:bg-gradient-elevated hover:border-border-accent hover:shadow-soft volume-slider border transition-all duration-200 ease-out"
        />
        {/* <span className="w-8 text-center text-xs">
          {Math.round(value * 100)}%
        </span> */}
      </div>
    </div>
  );
};

const Controls = () => {
  const {
    guideData,
    playerRef,
    isPlaying,
    playbackRate,
    setPlaybackRate,
    currentFrameSignal,
    currentTimeSignal,
  } = useVideoEditor();

  const {
    avatar: { fps, durationInFrames },
  } = guideData!;

  // 根据总帧数、帧率来计算总时长
  const duration = durationInFrames / fps;

  const currentTimeStr = useComputed(() =>
    timeFormat(Math.floor(currentTimeSignal.value))
  );
  const durationStr = timeFormat(Math.floor(duration));

  const doPlaying = (status: boolean) => {
    const player = playerRef?.current!;
    status ? player.pause() : player.play();
  };

  const togglePlaybackSpeed = () => {
    const currentIndex = playbackRateOptions.indexOf(playbackRate);
    const nextIndex = (currentIndex + 1) % playbackRateOptions.length;
    const val = playbackRateOptions[nextIndex]!;
    setPlaybackRate(val);
  };

  return (
    <div className="player-controls flex-shrink-0 px-6 py-4">
      {/* <div className="text-text-muted mb-4 flex items-center justify-between text-xs">
        <span>{timeFormat(0)}</span>
        <span>{durationStr}</span>
      </div> */}

      <div className="time-bar">
        <TimeBar currentTime={currentTimeSignal} duration={duration} />
      </div>
      <div className="actions text-text-muted mt-3 flex items-center justify-between text-sm">
        <div className="flex items-center gap-6">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => doPlaying(isPlaying)}
              className="bg-accent-coral hover:bg-accent-coral-hover h-9 w-9 rounded-full text-white"
            >
              {isPlaying ? (
                <Pause className="h-4 w-4 fill-current" />
              ) : (
                <Play className="h-4 w-4 fill-current" />
              )}
            </Button>
          </div>

          <div className="flex items-center gap-2 text-sm">
            <span className="font-medium text-white">{currentTimeStr}</span>
            <span className="text-text-muted">/</span>
            <span className="text-text-muted">{durationStr}</span>
            <span className="text-text-muted">
              ({currentFrameSignal}帧 / {durationInFrames}帧)
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="volume-bar">
            <VolumeBar />
          </div>
          <div className="bg-border-subtle h-6 w-px" />

          <Button
            variant="ghost"
            size="sm"
            onClick={togglePlaybackSpeed}
            className="text-text-secondary h-8 w-16 hover:text-white"
          >
            {playbackRate}x
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="text-text-secondary h-8 w-8 hover:text-white"
          >
            <Maximize className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Controls;
