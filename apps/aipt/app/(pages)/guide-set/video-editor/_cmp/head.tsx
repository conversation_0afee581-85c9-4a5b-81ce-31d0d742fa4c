import { Button } from "@/app/components/common/button";
import { Keyboard, Send } from "lucide-react";
import { useVideoEditor } from "../_helper/context";

import { post } from "@/lib/fetcher";
import { getStorageItem, setStorageItem } from "@repo/lib/utils/local-storage";
import { cloneDeep, isEqual } from "lodash";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const Head = () => {
  const { guideData, partData } = useVideoEditor();

  // 提交时间状态管理
  const [submitTime, setSubmitTime] = useState<string>("");
  // 跟踪是否有未保存的更改
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);

  // 格式化时间函数
  const formatSubmitTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${year}/${month}/${day} ${hours}:${minutes}`;
  };

  // 生成存储键
  const getStorageKey = (): string => {
    if (!partData) return "video-editor-submit-time";
    return `video-editor-submit-time-${partData.guideWidgetSetId}-${partData.guideWidgetId}`;
  };

  // 组件初始化时从本地存储读取提交时间
  useEffect(() => {
    const storageKey = getStorageKey();
    const savedTimestamp = getStorageItem<number>(storageKey, 0);
    if (savedTimestamp > 0) {
      setSubmitTime(formatSubmitTime(savedTimestamp));
    }
  }, [partData]);
  const [showLeaveConfirm, setShowLeaveConfirm] = useState(false);

  // 监听数据变化，标记为有未保存的更改
  useEffect(() => {
    // 只有在有数据且不是初始加载时才标记为有更改
    if (guideData && submitTime) {
      setHasUnsavedChanges(true);
    }
  }, [guideData, submitTime]);

  // 监听页面刷新/关闭事件，提示用户保存数据
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (partData && guideData) {
        const oldData = JSON.parse(partData?.videoJson);
        const newData = cloneDeep(guideData);
        newData.content.forEach((item) => {
          delete item.id;
          // @ts-expect-error
          delete item.contentText;
          item.content.forEach((item2: any) => {
            delete item2.id;
            delete item2.contentText;
          });
        });

        newData.subtitles.forEach((item) => {
          // @ts-expect-error
          delete item.contentText;
        });
        const contentIsEqual = isEqual(newData.content, oldData.content);
        const subtitlesIsEqual = isEqual(newData.subtitles, oldData.subtitles);
        if (!contentIsEqual || !subtitlesIsEqual) {
          const message = "您有未保存的更改，确定要离开吗？";
          event.preventDefault();
          event.returnValue = message;
          return message;
        }
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [hasUnsavedChanges, guideData]);
  const onSubmit = async () => {
    if (!partData) return;

    const newGuideData = cloneDeep(guideData);
    if (newGuideData) {
      // @ts-expect-error
      delete newGuideData.remark;
      newGuideData.content.forEach((item) => {
        delete item.id;
        // @ts-expect-error
        delete item.contentText;
        item.content.forEach((item2: any) => {
          delete item2.id;
          // @ts-expect-error
          delete item.contentText;
        });
      });
      newGuideData.subtitles.forEach((item) => {
        // @ts-expect-error
        delete item.contentText;
      });
    }
    const newVideoJson = JSON.stringify(newGuideData);
    partData.videoJson = newVideoJson;

    // 提交接口，保存字幕内容
    const url = "/api/v1/guideWidget/save/subtitles";
    const params = {
      guideWidgetId: partData.guideWidgetId,
      guideWidgetSetId: partData.guideWidgetSetId,
      videoJson: newVideoJson,
    };
    await post(url, { arg: params });

    // 提交成功后保存当前时间到本地存储
    const currentTimestamp = Date.now();
    const storageKey = getStorageKey();
    setStorageItem(storageKey, currentTimestamp);

    // 更新页面显示的提交时间
    setSubmitTime(formatSubmitTime(currentTimestamp));

    // 清除未保存更改标记
    setHasUnsavedChanges(false);

    toast.success("更新成功");
  };

  return (
    <div className="glass-effect flex w-full items-center justify-between px-6 py-4">
      <div className="flex items-center gap-6">
        <h1 className="text-xl font-medium text-white">课程编辑</h1>
        {submitTime && (
          <span className="text-text-muted text-sm">提交于 {submitTime}</span>
        )}
      </div>

      <div className="flex items-center gap-6">
        <Button
          type={"ghost"}
          size="sm"
          className="text-text-muted border-0 text-xs hover:text-white"
        >
          <Keyboard className="mr-1 h-3 w-3" />
          快捷键
        </Button>
        <Button
          size="sm"
          className="bg-accent-coral hover:bg-accent-coral-hover border-0 text-white shadow-lg"
          onClick={onSubmit}
        >
          <Send className="mr-1.5 h-4 w-4 fill-current" />
          提交
        </Button>
      </div>
    </div>
  );
};

export default Head;
