"use client";
import RoughNotion from "@repo/core/components/notion/rough-notion";
import {
  DefaultNotionColorMap,
  NotionTypes,
} from "@repo/core/components/notion/types";
import { cn } from "@repo/ui/lib/utils";
import { FC, useMemo } from "react";
import Markdown from "react-markdown";
import rehypeKatex from "rehype-katex";
import rehypeRaw from "rehype-raw";
import remarkMath from "remark-math";
import { Pluggable } from "unified";
import { useMdEditorContext } from "./md-editor-context";
import { MdImage } from "./md-image";
import "./md-preview.css";

export type PreviewOptions = {
  supportsHTML?: boolean;
};

interface PreviewProps extends React.ComponentProps<"div"> {
  options?: PreviewOptions;
}

export const Preview: FC<PreviewProps> = ({ className, options, ...props }) => {
  const { content } = useMdEditorContext();

  const rehypePlugins = useMemo(() => {
    const katexOptions = {
      delimiters: [
        { left: "$$", right: "$$", display: true },
        { left: "$", right: "$", display: false },
        { left: "\\[", right: "\\]", display: true },
        { left: "\\(", right: "\\)", display: false },
      ],
      strict: false,
      trust: true,
      // macros: {
      //   "\\text": "\\text{#1}",
      // },
    };
    if (options?.supportsHTML) {
      return [[rehypeKatex, katexOptions], rehypeRaw] as Pluggable[];
    }
    return [[rehypeKatex, katexOptions]] as Pluggable[];
  }, [options]);

  return (
    <div
      className={cn(
        "relative rounded-sm bg-gray-50 px-4 py-2 outline-1 outline-zinc-300",
        className
      )}
      {...props}
    >
      <div className="preview">
        <Markdown
          remarkPlugins={[[remarkMath, { singleDollarTextMath: false }]]}
          rehypePlugins={rehypePlugins}
          components={{
            mark: ({ children, ...props }) => {
              const { dataType } = props.node?.properties || {};
              delete props.node;
              return (
                <RoughNotion
                  type={dataType as NotionTypes}
                  // {...props}
                  show={true}
                  color={DefaultNotionColorMap[dataType as NotionTypes]}
                  animate={false}
                  strokeWidth={2}
                  padding={[-4, 4]}
                >
                  {children}
                </RoughNotion>
              );
            },
            em: ({ children, node }) => {
              const { dataSrc } = node?.properties || {};
              return <MdImage dataSrc={dataSrc as string}>{children}</MdImage>;
            },
          }}
        >
          {content.value.replace(/^---?/gm, "- ")}
        </Markdown>
      </div>
    </div>
  );
};
