"use client";

import { useSketchCanvasRef } from "@repo/core/guide/context/sketch-canvas-context";
import {
  DrawElement,
  GuideWidgetData,
} from "@repo/core/types/data/widget-guide";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@repo/ui/components/tooltip";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Eraser,
  Highlighter,
  <PERSON>cil,
  Redo,
  Trash,
  Undo,
} from "lucide-react";
import { FC, useEffect, useState } from "react";
import { GuideDrawPlayer } from "./guide-player";

// 画笔和橡皮擦粗细配置常量
const PEN_STROKE_WIDTHS = [1, 2, 4] as const;
const ERASER_STROKE_WIDTHS = [4, 8, 16] as const;

interface GuideDrawWrapperProps {
  videoJson: GuideWidgetData;
  width: number;
  height: number;
  inFrame?: number;
  outFrame?: number;
  onDrawChange?: (paths: DrawElement[] | null) => void;
  backHandler?: () => void;
  handleUndo?: () => void;
  handleRedo?: () => void;
  isUndo: boolean;
  isRedo: boolean;
  partIndex: number;
  totalPartsCount: number;
}

export const GuideDrawWrapper: FC<GuideDrawWrapperProps> = ({
  videoJson,
  inFrame = 0,
  outFrame,
  onDrawChange,
  backHandler,
  isUndo,
  isRedo,
  handleUndo,
  handleRedo,
  partIndex,
  totalPartsCount,
}) => {
  // const PLAYER_BAR_HEIGHT = 80;
  // const MAX_VIDEO_WIDTH = 1000;
  // const [containerWidth, setContainerWidth] = useState<number>(1200);
  // const [containerHeight, setContainerHeight] = useState<number>(1000);
  const {
    canvasRef,
    strokeColor,
    strokeWidth,
    setStrokeColor,
    setStrokeWidth,
  } = useSketchCanvasRef();
  const [eraserMode, setEraserMode] = useState(false);
  const [lastStrokeColor, setLastStrokeColor] = useState("#F9453F");
  const [isHighlighter, setIsHighlighter] = useState(false);
  const PEN_COLORS: string[] = ["#4C55F5", "#F9453F", "#64D769"];
  const HIGHLIGHTER_COLORS: string[] = ["#4C55F5", "#F9453F", "#64D769"];
  const [penColor, setPenColor] = useState<string>("#4C55F5");
  const [highlighterColor, setHighlighterColor] = useState<string>("#4C55F5");

  // 画笔和橡皮擦各自的粗细状态
  const [penStrokeWidth, setPenStrokeWidth] = useState<number>(
    PEN_STROKE_WIDTHS[1]
  ); // 默认选择中间值
  const [eraserStrokeWidth, ********************] = useState<number>(
    ERASER_STROKE_WIDTHS[1]
  ); // 默认选择中间值

  // 组件初始化时设置默认的画笔粗细
  useEffect(() => {
    setStrokeWidth(penStrokeWidth);
  }, []); // 只在组件挂载时执行一次

  // useEffect(() => {
  //   if (typeof window !== "undefined") {
  //     setContainerWidth(window.innerWidth);
  //     setContainerHeight(window.innerHeight - PLAYER_BAR_HEIGHT);
  //     const handleResize = () => {
  //       setContainerWidth(window.innerWidth);
  //       setContainerHeight(window.innerHeight - PLAYER_BAR_HEIGHT);
  //     };
  //     window.addEventListener("resize", handleResize);
  //     return () => window.removeEventListener("resize", handleResize);
  //   }
  // }, []);

  // const videoWidth = Math.min(containerWidth * 0.7, MAX_VIDEO_WIDTH);
  useEffect(() => {
    setStrokeColor(PEN_COLORS[0] as string);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePenHighlighterToggle = () => {
    if (eraserMode) {
      // 切换回画笔/荧光笔
      canvasRef?.current?.eraseMode(false);
      setEraserMode(false);
      setStrokeColor(isHighlighter ? highlighterColor : penColor);
      setStrokeWidth(penStrokeWidth);
    } else {
      const next = !isHighlighter;
      setIsHighlighter(next);
      setStrokeColor(next ? highlighterColor : penColor);
    }
  };

  const handleColorClick = (color: string) => {
    if (eraserMode) return;
    if (isHighlighter) {
      setHighlighterColor(color);
      setStrokeColor(color);
    } else {
      setPenColor(color);
      setStrokeColor(color);
    }
    setLastStrokeColor(color);
  };

  const handleEraserClick = () => {
    if (eraserMode || isHighlighter) return;
    canvasRef?.current?.eraseMode(true);
    setEraserMode(true);
    setLastStrokeColor(strokeColor);
    setStrokeColor("#fff"); // 橡皮擦颜色可自定义
    setStrokeWidth(eraserStrokeWidth); // 使用橡皮擦的粗细设置
  };

  const handleClearClick = () => {
    canvasRef?.current?.clearCanvas();
    onDrawChange?.([]);
  };

  const renderToolbar = () => (
    <div className="flex h-full w-16 flex-col items-center justify-between bg-white/90">
      <button
        title="返回上一页"
        className="mb-4 flex h-10 w-10 items-center justify-center rounded-full bg-gray-900 text-white shadow-lg transition hover:bg-gray-700"
        onClick={backHandler}
      >
        <ArrowLeft className="size-6" />
      </button>

      <div className="flex w-full flex-col items-center gap-6">
        <div className="mb-2 flex flex-col items-center gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                title={isHighlighter ? "荧光笔" : "画笔"}
                className={`mb-1 flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                  !eraserMode ? "border-blue-500 bg-blue-50" : "border-gray-200"
                }`}
                onClick={handlePenHighlighterToggle}
                type="button"
              >
                {isHighlighter ? (
                  <Highlighter className="size-4 text-yellow-500" />
                ) : (
                  <Pencil className="size-4 text-gray-700" />
                )}
              </button>
            </TooltipTrigger>
            <TooltipContent
              side="right"
              sideOffset={8}
              align="start"
              className="border border-gray-200 bg-white p-0 shadow-lg"
              hideArrow
            >
              <div className="flex flex-row items-center gap-2 p-2">
                <div className="flex flex-col items-center">
                  {isHighlighter ? (
                    <Highlighter className="mb-1 size-4 text-yellow-500" />
                  ) : (
                    <Pencil className="mb-1 size-4 text-gray-700" />
                  )}
                  {(isHighlighter ? HIGHLIGHTER_COLORS : PEN_COLORS).map(
                    (color) => (
                      <div
                        key={color}
                        className="mb-1 h-4 w-4 rounded-full border border-gray-200 last:mb-0"
                        style={{ background: color }}
                      />
                    )
                  )}
                </div>
              </div>
            </TooltipContent>
          </Tooltip>

          <button
            title="橡皮擦"
            className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
              eraserMode ? "border-blue-500 bg-blue-50" : "border-gray-200"
            } ${isHighlighter ? "cursor-not-allowed opacity-50" : ""}`}
            onClick={handleEraserClick}
            type="button"
            disabled={isHighlighter}
          >
            <Eraser className="size-4 text-gray-700" />
          </button>
        </div>
        {/* 颜色区：根据模式切换 */}
        <div className="flex flex-col items-center gap-2">
          <div className="mb-1 flex flex-col items-center gap-2 text-xs font-bold text-gray-700">
            <span>颜色</span>
          </div>
          <div className="flex flex-col items-center gap-2">
            {(isHighlighter ? HIGHLIGHTER_COLORS : PEN_COLORS).map((color) => (
              <button
                key={color}
                className={`h-6 w-6 rounded-full border-2 ${
                  strokeColor === color && !eraserMode
                    ? "border-blue-500"
                    : "border-white"
                } shadow ${eraserMode ? "cursor-not-allowed opacity-50" : ""}`}
                style={{ background: color }}
                onClick={() => handleColorClick(color)}
                type="button"
                disabled={eraserMode}
              />
            ))}
          </div>
        </div>
        {/* 粗细 */}
        <div className="flex flex-col items-center gap-2">
          <div className="mb-1 text-xs font-bold text-gray-700">粗细</div>
          <div className="flex flex-col items-center gap-2">
            {(eraserMode ? ERASER_STROKE_WIDTHS : PEN_STROKE_WIDTHS).map(
              (w) => (
                <button
                  key={w}
                  className={`flex h-8 w-8 items-center justify-center rounded-lg border-2 ${
                    (eraserMode ? eraserStrokeWidth : penStrokeWidth) === w
                      ? "border-blue-400 bg-blue-50"
                      : "border-white bg-gray-50"
                  }`}
                  onClick={() => {
                    if (eraserMode) {
                      ********************(w);
                      setStrokeWidth(w);
                    } else {
                      setPenStrokeWidth(w);
                      setStrokeWidth(w);
                    }
                  }}
                  type="button"
                >
                  <div
                    style={{
                      width: 20,
                      height: w,
                      background: "#888",
                      borderRadius: 2,
                    }}
                  />
                </button>
              )
            )}
          </div>
        </div>
        {/* 操作按钮 */}
        <div className="mt-4 flex flex-col items-center gap-4">
          <button onClick={handleClearClick} title="清空" aria-label="清空">
            <Trash className="size-5" />
          </button>
          <button
            title="撤销"
            type="button"
            className="btn btn-sm btn-outline-primary disabled:cursor-not-allowed disabled:opacity-50"
            onClick={handleUndo}
            disabled={!isUndo}
          >
            <Undo className="size-5" />
          </button>
          <button
            title="重做"
            type="button"
            className="btn btn-sm btn-outline-primary disabled:cursor-not-allowed disabled:opacity-50"
            onClick={handleRedo}
            disabled={!isRedo}
          >
            <Redo className="size-5" />
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="relative flex h-full w-full flex-row gap-y-64">
      {/* 工具栏 */}
      {renderToolbar()}

      {/* GuidePlayer */}

      <div className="flex aspect-[1000/600] h-full items-center justify-center">
        <GuideDrawPlayer
          className="h-[600px]"
          onDrawChange={onDrawChange}
          data={videoJson}
          width={1000}
          height={600}
          inFrame={inFrame}
          outFrame={outFrame}
          eraserMode={eraserMode}
          highlighter={isHighlighter}
          partIndex={partIndex}
          totalPartsCount={totalPartsCount}
        />
      </div>
    </div>
  );
};
