import { useGuideContext } from "@/app/context/guide-context";
// import { useGuideSetContext } from "@/app/context/guide-set-context";
import { MathJaxConfig } from "@repo/core/components/math-jax-config";
import { toH3Parts } from "@repo/core/guide/utils/h3-parts";
import { useGuideTreeViewmodel } from "@repo/core/guide/viewmodels/guide-tree-viewmodel";
import { GuideWidgetData, Line } from "@repo/core/types/data/widget-guide";
import { FC, useMemo } from "react";
import { Button } from "../common/button";
import { GuideEditorLine } from "../editor-guide/guide-editor-line";

const GuideH3: FC<{ data: Line[] }> = ({ data }) => {
  const root = useGuideTreeViewmodel(data);
  return (
    <div className="flex w-[1000px] flex-col rounded-md bg-[#FFFDFA] px-4 outline-1">
      <div className="w-section-h3">
        <GuideEditorLine data={root} root={true} />
      </div>
    </div>
  );
};

export const GuideH3List: FC = () => {
  const { guide } = useGuideContext();
  // const { guideWidgetIndex, guideWidgetSetData } = useGuideSetContext();
  const { guideWidgetId, guideWidgetSetId } = guide;
  const videoData = useMemo(() => {
    if (typeof guide.videoJson === "string") {
      return JSON.parse(guide.videoJson) as GuideWidgetData;
    }
    return guide.videoJson || ({} as GuideWidgetData);
  }, [guide.videoJson]);
  const parts = useMemo(
    () => toH3Parts(videoData.content),
    [videoData.content]
  );

  // 检查某个部分是否已完成圈画
  const isPartCompleted = (part: Line[]) => {
    // 查找该部分中的h3标签，检查是否有draw数据
    const h3Line = part.find((line) => line.tag === "h3");
    return h3Line && h3Line.draw && h3Line.draw.length > 0;
  };

  return (
    <MathJaxConfig>
      <div className="flex flex-col gap-4">
        {parts.map((part, index) => {
          const isCompleted = isPartCompleted(part);
          return (
            <div key={index} className="flex w-full flex-row justify-between">
              <GuideH3 data={part} />
              <div className="flex flex-col items-center gap-2">
                <Button
                  className="rounded border border-gray-200 bg-white px-4 py-1 text-sm text-gray-600 hover:bg-gray-50"
                  onClick={() => {
                    // 在当前页打开
                    location.href = `/guide-draw/draw?id=${guideWidgetSetId}&guideWidgetId=${guideWidgetId}&blockIndex=${index}`;
                  }}
                >
                  进入圈画录制
                </Button>
                {/* 完成状态标记 */}
                <div className="flex items-center gap-1 text-xs">
                  {isCompleted ? (
                    <>
                      <div className="h-2 w-2 rounded-full bg-green-500"></div>
                      <span className="text-green-600">已完成</span>
                    </>
                  ) : (
                    <>
                      <div className="h-2 w-2 rounded-full bg-gray-300"></div>
                      <span className="text-gray-500">未完成</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </MathJaxConfig>
  );
};
