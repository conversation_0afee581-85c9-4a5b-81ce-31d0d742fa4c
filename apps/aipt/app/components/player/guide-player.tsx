import { Player } from "@remotion/player";
import { GuideView } from "@repo/core/guide/guide-view";
import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import { FC, useEffect, useMemo, useState } from "react";
// import localData from "../../../mock/guide-video.json";
import { useGuideSetContext } from "@/app/context/guide-set-context";
import { Spin } from "antd";
import { EmptyTip } from "../panels/empty-tip";
import { playbackRateOptions } from "./player-controls";

interface GuidePlayerProps {
  width?: number;
  height?: number;
  className?: string;
  data: GuideWidgetData | string;
  controls?: boolean;
  partIndex?: number;
  totalPartsCount?: number;
}

const GuidePlayer: FC<GuidePlayerProps> = ({
  width = 1000,
  height = 600,
  className,
  data,
  controls = true,
  partIndex,
  totalPartsCount,
}) => {
  const { guideWidgetIndex, guideWidgetSetData } = useGuideSetContext();
  const guideData = useMemo(() => {
    if (typeof data === "string") {
      return data ? (JSON.parse(data) as GuideWidgetData) : null;
    }
    return data;
  }, [data]);

  if (!guideData) {
    return <EmptyTip texture="视频未生成" />;
  }
  // const guideData = localData as unknown as GuideWidgetData;

  const { avatar } = guideData;

  const [blobUrl, setBlobUrl] = useState<string>();
  useEffect(() => {
    // 模拟预加载：通过 Video 对象加载视频（触发浏览器缓存）
    fetch(avatar.url)
      .then((res) => res.blob())
      .then((blob) => {
        const blobUrl = URL.createObjectURL(blob);
        setBlobUrl(blobUrl);
      })
      .catch((err) => {
        console.error("预加载视频失败:", err);
        setTimeout(() => {
          setBlobUrl(avatar.url);
        }, 1000);
      });
  }, [avatar.url]);

  return (
    <Spin spinning={!blobUrl}>
      <Player
        className={cn("h-[600px] w-[1000px]", className)}
        style={{ width: "100%" }}
        component={GuideView}
        inputProps={{
          data: { ...guideData, blobUrl },
          selectable: false,
          index: partIndex ?? guideWidgetIndex.value,
          totalGuideCount:
            totalPartsCount ??
            guideWidgetSetData.value?.guideWidgetList?.length,
        }}
        durationInFrames={avatar?.durationInFrames + avatar?.fps} //增加1秒, bugfix: 最后一点语音未播完就结束
        fps={avatar?.fps}
        showPlaybackRateControl={playbackRateOptions}
        playbackRate={1}
        controls={controls} // 这将隐藏整个控制条
        alwaysShowControls
        allowFullscreen={false}
        compositionWidth={width}
        compositionHeight={height}
        acknowledgeRemotionLicense
        errorFallback={(e: { error: { message: string } }) => (
          <span className="text-sm text-red-500">错误: {e.error.message}</span>
        )}
      />
    </Spin>
  );
};

export { GuidePlayer };
