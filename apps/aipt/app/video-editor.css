@custom-variant dark (&:is(.video-dark *));

:root {
  --font-size: 14px;
  --background: #ffffff;
  --foreground: oklch(0.145 0 0);
  --card: #ffffff;
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: #030213;
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.95 0.0058 264.53);
  --secondary-foreground: #030213;
  --muted: #ececf0;
  --muted-foreground: #717182;
  --accent: #e9ebef;
  --accent-foreground: #030213;
  --destructive: #d4183d;
  --destructive-foreground: #ffffff;
  --border: rgba(0, 0, 0, 0.1);
  --input: transparent;
  --input-background: #f3f3f5;
  --switch-background: #cbced4;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: #030213;
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  
  /* Enhanced dark theme tokens with better color harmony */
  --surface-primary: #0a0a0a;
  --surface-secondary: #141418;
  --surface-tertiary: #1e1e24;
  --surface-elevated: #252530;
  --surface-hover: #2a2a36;
  --accent-coral: #ff5722;
  --accent-coral-hover: #e64a19;
  --accent-coral-muted: #ff572250;
  --accent-orange: #f97316;
  --accent-green: #10b981;
  --accent-dark-green: #1f4f47;
  --accent-blue-purple: #3a3856;
  --text-primary: #ffffff;
  --text-secondary: #a8a8b3;
  --text-muted: #71717a;
  --text-dim: #52525b;
  --border-subtle: rgba(255, 255, 255, 0.08);
  --border-muted: rgba(255, 255, 255, 0.04);
  --border-accent: rgba(255, 87, 34, 0.2);
  
  /* Enhanced shadow system */
  --shadow-subtle: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 2px 0 rgba(0, 0, 0, 0.12);
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.15), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  --shadow-strong: 0 20px 25px -5px rgba(0, 0, 0, 0.25), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(255, 87, 34, 0.15);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  
  /* Enhanced gradient system */
  --gradient-surface: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
  --gradient-elevated: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.04) 100%);
  --gradient-glow: linear-gradient(135deg, rgba(255, 87, 34, 0.2) 0%, rgba(249, 115, 22, 0.2) 100%);
  --gradient-accent: linear-gradient(135deg, rgba(255, 87, 34, 0.15) 0%, rgba(249, 115, 22, 0.08) 100%);
  --gradient-tab-active: linear-gradient(180deg, var(--surface-secondary) 0%, rgba(255, 87, 34, 0.12) 100%);
  
  /* Glass effect variables */
  --glass-bg: rgba(30, 30, 36, 0.7);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-backdrop: blur(12px) saturate(180%);
}

.video-dark {
  --background: var(--surface-primary);
  --foreground: var(--text-primary);
  --card: var(--surface-secondary);
  --card-foreground: var(--text-primary);
  --popover: var(--surface-elevated);
  --popover-foreground: var(--text-primary);
  --primary: var(--accent-coral);
  --primary-foreground: var(--text-primary);
  --secondary: var(--surface-tertiary);
  --secondary-foreground: var(--text-primary);
  --muted: var(--surface-tertiary);
  --muted-foreground: var(--text-muted);
  --accent: var(--surface-tertiary);
  --accent-foreground: var(--text-primary);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: var(--border-subtle);
  --input: var(--surface-tertiary);
  --ring: var(--accent-coral);
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: var(--surface-secondary);
  --sidebar-foreground: var(--text-primary);
  --sidebar-primary: var(--accent-coral);
  --sidebar-primary-foreground: var(--text-primary);
  --sidebar-accent: var(--surface-tertiary);
  --sidebar-accent-foreground: var(--text-primary);
  --sidebar-border: var(--border-subtle);
  --sidebar-ring: var(--accent-coral);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  
  /* Enhanced surface colors */
  --color-surface-primary: var(--surface-primary);
  --color-surface-secondary: var(--surface-secondary);
  --color-surface-tertiary: var(--surface-tertiary);
  --color-surface-elevated: var(--surface-elevated);
  --color-surface-hover: var(--surface-hover);
  --color-accent-coral: var(--accent-coral);
  --color-accent-coral-hover: var(--accent-coral-hover);
  --color-accent-coral-muted: var(--accent-coral-muted);
  --color-accent-orange: var(--accent-orange);
  --color-accent-green: var(--accent-green);
  --color-accent-dark-green: var(--accent-dark-green);
  --color-accent-blue-purple: var(--accent-blue-purple);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-muted: var(--text-muted);
  --color-text-dim: var(--text-dim);
  --color-border-subtle: var(--border-subtle);
  --color-border-muted: var(--border-muted);
  --color-border-accent: var(--border-accent);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/**
 * Base typography. This is not applied to elements which have an ancestor with a Tailwind text class.
 */
@layer base {
  :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) {
    h1 {
      font-size: var(--text-2xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h2 {
      font-size: var(--text-xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h3 {
      font-size: var(--text-lg);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h4 {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    p {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }

    label {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    button {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    input {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }
  }
}

/* html {
  font-size: var(--font-size);
} */

/* Enhanced utility classes for the course editor */
@layer utilities {
  /* Glass effect system */
  .glass-effect {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
  }
  
  
  /* Surface treatments */
  .surface-elevated {
    background: var(--gradient-elevated);
    box-shadow: var(--shadow-soft);
    border: 1px solid var(--border-subtle);
  }
  
  
  
  /* Enhanced shadow system */
  .shadow-refined {
    box-shadow: var(--shadow-medium);
  }
  
  .shadow-inner-subtle {
    box-shadow: var(--shadow-inner);
  }
  
  
  /* Custom volume slider styles */
  .volume-slider {
    --slider-track-color: var(--color-surface-tertiary);
    --slider-range-color: var(--color-accent-coral);
    --slider-thumb-color: white;
  }
  
  .volume-slider [data-slot="slider-track"] {
    background-color: var(--slider-track-color) !important;
    border-radius: 9999px;
  }
  
  .volume-slider [data-slot="slider-range"] {
    background-color: var(--slider-range-color) !important;
    border-radius: 9999px;
  }
  
  .volume-slider [data-slot="slider-thumb"] {
    background-color: var(--slider-thumb-color) !important;
    border: 2px solid var(--slider-thumb-color) !important;
    box-shadow: var(--shadow-soft);
  }
  
  /* Enhanced tab gradient with proper alignment */
  .tab-gradient-active {
    background: var(--gradient-tab-active);
    box-shadow: var(--shadow-subtle);
    position: relative;
  }
  
  /* Ensure proper bottom border for active tabs */
  .border-b-accent-coral {
    border-bottom-color: var(--color-accent-coral) !important;
  }

  
  .bg-accent-blue-purple {
    background-color: var(--color-accent-blue-purple);
  }
  
  /* Text utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
  }
  
  .line-clamp-4 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
    overflow: hidden;
  }
  
  /* Content card optimizations */
  .content-card {
    background: var(--gradient-surface);
    border: 1px solid var(--border-subtle);
    box-shadow: var(--shadow-subtle);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .content-card:hover {
    background: var(--gradient-elevated);
    border-color: var(--border-accent);
    box-shadow: var(--shadow-soft);
  }
  
  .content-card.active {
    background: var(--gradient-accent);
    border-color: var(--color-accent-coral);
    box-shadow: var(--shadow-soft), var(--shadow-glow);
  }
  
}


@media (min-height: 1000px) {
  .playerWrap {
    margin-top: 10%;
  }
}