"use client";

import { RawGuideSet } from "@/types/guide-widget";
import { Signal, signal, useSignal } from "@preact-signals/safe-react";
import { useSearchParams } from "next/navigation";
import { createContext, FC, ReactNode, useContext, useEffect } from "react";

/**
 * GuideSet 上下文类型定义
 * 用于管理part集合的状态和操作
 */
interface GuideSetContextType {
  /** 当前选中的part组件ID */
  guideWidgetId: Signal<string>;
  /** 设置当前选中的part组件ID */
  setGuideWidgetId: (id: string) => void;
  /** 当前选中的part组件索引 */
  guideWidgetIndex: Signal<number>;
  /** 设置当前选中的part组件索引 */
  setGuideWidgetIndex: (index: number) => void;
  /** part集合ID */
  guideWidgetSetId: string;
  /** part集合数据 */
  guideWidgetSetData: Signal<RawGuideSet | null>;
  /** 更新part集合数据 */
  setGuideWidgetSetData: (data: RawGuideSet | null) => void;
}

// 创建上下文，提供默认值
const GuideSetContext = createContext<GuideSetContextType>({
  guideWidgetId: signal(""),
  setGuideWidgetId: () => {},
  guideWidgetIndex: signal(0),
  setGuideWidgetIndex: () => {},
  guideWidgetSetId: "",
  guideWidgetSetData: signal(null),
  setGuideWidgetSetData: () => {},
});

/**
 * 自定义 Hook，用于访问 GuideSet 上下文
 */
export const useGuideSetContext = () => useContext(GuideSetContext);

interface GuideSetProviderProps {
  children: ReactNode;
}

/**
 * GuideSet 上下文提供者组件
 * 负责初始化和管理part集合的状态
 */
export const GuideSetProvider: FC<GuideSetProviderProps> = ({ children }) => {
  const searchParams = useSearchParams();
  const guideWidgetSetId = searchParams.get("id") || "";

  // 从 localStorage 中读取缓存的 guideWidgetId
  const initialGuideWidgetId =
    typeof window !== "undefined"
      ? localStorage.getItem(`guideWidgetId-${guideWidgetSetId}`) || ""
      : "";

  // 初始化状态
  const guideWidgetId = useSignal(initialGuideWidgetId);
  const guideWidgetSetData = useSignal<RawGuideSet | null>(null);
  const guideWidgetIndex = useSignal<number>(0); // 当前选中的part组件索引

  /**
   * 设置当前选中的part组件ID
   * 同时更新 localStorage 以保持状态持久化
   */
  const handleSetGuideWidgetId = (id: string) => {
    guideWidgetId.value = id;
    localStorage.setItem(`guideWidgetId-${guideWidgetSetId}`, id);
  };

  // 设置当前选中的part组件索引
  const handleSetGuideWidgetIndex = (index: number) => {
    guideWidgetIndex.value = index;
  };

  /**
   * 更新part集合数据
   */
  const handleSetGuideWidgetSetData = (data: RawGuideSet | null) => {
    guideWidgetSetData.value = data;
  };

  useEffect(() => {
    if (guideWidgetSetData.value?.guideWidgetList?.length) {
      const targetIndex = guideWidgetSetData.value.guideWidgetList.findIndex(
        (c) => Number(c.guideWidgetId) === Number(guideWidgetId.value)
      );
      if (targetIndex !== guideWidgetIndex.value) {
        guideWidgetIndex.value = targetIndex;
      }
    }
  }, [guideWidgetSetData.value, guideWidgetId.value, guideWidgetIndex.value]);

  // 组件初始化时清理其他 id 的 guideWidgetId
  useEffect(() => {
    if (typeof window !== "undefined") {
      // 遍历 localStorage 中的所有项
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        // 如果是以 guideWidgetId- 开头的键
        if (key?.startsWith("guideWidgetId-")) {
          // 提取 id
          const id = key.replace("guideWidgetId-", "");
          // 如果不是当前页面的 id，则删除
          if (id !== guideWidgetSetId) {
            console.log("Cleaning up localStorage for other id:", id);
            localStorage.removeItem(key);
          }
        }
      }
    }
  }, [guideWidgetSetId]);

  // 监听组件卸载
  useEffect(() => {
    return () => {
      localStorage.removeItem(`guideWidgetId-${guideWidgetSetId}`);
    };
  }, [guideWidgetSetId]);

  // 构建上下文值
  const value = {
    guideWidgetId,
    setGuideWidgetId: handleSetGuideWidgetId,
    guideWidgetIndex,
    setGuideWidgetIndex: handleSetGuideWidgetIndex,
    guideWidgetSetId,
    guideWidgetSetData,
    setGuideWidgetSetData: handleSetGuideWidgetSetData,
  };

  return (
    <GuideSetContext.Provider value={value}>
      {children}
    </GuideSetContext.Provider>
  );
};
