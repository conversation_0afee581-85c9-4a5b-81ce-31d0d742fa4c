@import "@repo/ui/globals.css";

@import "./video-editor.css";

@theme {
  --color-primary: #4F46E5;

    /* 文字颜色 */
  --color-text-1: rgba(51, 48, 45, 0.95); /* Text 1 */
  --color-text-2: rgba(51, 48, 45, 0.85); /* Text 2 */
  --color-text-3: rgba(51, 48, 45, 0.7); /* Text 3 */
  --color-text-4: rgba(51, 48, 45, 0.55); /* Text 4 */
  --color-text-5: rgba(51, 48, 45, 0.4); /* Text 5 */

  /* 文稿组件内使用的颜色 */
  --color-stone-600:#5C5757;
  --color-stone-700: #4A292A;
  --color-stone-900: #221407;
  --color-amber-200: #FFD080;
  --color-yellow-950: #4D2F13;
  --color-dim-orange: #CC6204;
  --color-main-orange: #FFA666;

  --aspect-guide: 1000 / 600;
}

.MathJax {
  font-size: 18px !important;
}

html, body {
  text-spacing-trim: space-all;
}

/* 圈画页面特别针对控制栏区域 */
.remotion-player-draw > div:last-child {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  background-image: linear-gradient(rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.01) 5.5714%, rgba(0, 0, 0, 0.047) 10.1429%, rgba(0, 0, 0, 0.106) 16.1429%, rgba(0, 0, 0, 0.176) 20.4286%, rgba(0, 0, 0, 0.26) 25.4286%, rgba(0, 0, 0, 0.353) 29.8571%, rgba(0, 0, 0, 0.45) 34.2857%, rgba(0, 0, 0, 0.55) 38.5714%, rgba(0, 0, 0, 0.647) 42%, rgba(0, 0, 0, 0.74) 92.4286%, rgba(0, 0, 0, 0.824) 101.429%, rgba(0, 0, 0, 0.894) 110.714%, rgba(0, 0, 0, 0.953) 120.714%, rgba(0, 0, 0, 0.99) 131.286%, rgb(0, 0, 0) 100%) !important;
}
