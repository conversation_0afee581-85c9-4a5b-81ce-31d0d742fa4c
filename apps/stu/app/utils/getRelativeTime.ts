export const getRelativeTime = (timestamp: number | string) => {
  const now = Date.now();
  const targetDate = new Date(timestamp);
  const diff = now - targetDate.getTime();

  // 转换为各种时间单位的毫秒数
  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const year = 365 * day;

  // 1. 一小时内的评论，展示 **分钟前
  if (diff < hour) {
    const minutes = Math.floor(diff / minute);
    return minutes <= 0 ? "刚刚" : `${minutes}分钟前`;
  }

  // 2. 24小时内的评论，展示 **小时前
  if (diff < day) {
    const hours = Math.floor(diff / hour);
    return `${hours}小时前`;
  }

  // 3. 一周内的评论，展示 **天前
  if (diff < week) {
    const days = Math.floor(diff / day);
    return `${days}天前`;
  }

  // 4. 一年内的评论，展示 MM-DD
  if (diff < year) {
    const month = String(targetDate.getMonth() + 1).padStart(2, "0");
    const date = String(targetDate.getDate()).padStart(2, "0");
    return `${month}-${date}`;
  }

  // 5. 一年以上的评论，展示 YY-MM-DD
  const year2Digit = String(targetDate.getFullYear()).slice(-2);
  const month = String(targetDate.getMonth() + 1).padStart(2, "0");
  const date = String(targetDate.getDate()).padStart(2, "0");
  return `${year2Digit}-${month}-${date}`;
};
