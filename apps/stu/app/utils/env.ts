/**
 * 环境工具函数
 */

/**
 * 根据当前域名判断是否是开发环境
 * 如果是 localhost 或者 192.168.x.x 的 IP 地址，则认为是开发环境
 * @returns {boolean} 是否是开发环境
 */
export const isDevelopment = (): boolean => {
  // 如果不在浏览器环境中，无法获取 window.location
  if (typeof window === "undefined") {
    // 在服务器端环境，可以通过 NODE_ENV 来判断
    return process.env.NODE_ENV === "development";
  }

  // 获取当前主机名
  const hostname = window.location.hostname;

  // 判断是否为 localhost
  if (hostname === "localhost" || hostname === "127.0.0.1") {
    return true;
  }

  // 判断是否为 192.168.x.x 或 172.16.x.x 的 IP 地址
  if (/^((192\.168)|(172\.16))\.\d{1,3}\.\d{1,3}$/.test(hostname)) {
    return true;
  }

  // 非开发环境
  return false;
};

/**
 * 获取当前环境名称
 * @returns {string} 环境名称：'development' 或 'production'
 */
export const getEnvironment = (): string => {
  return isDevelopment() ? "development" : "production";
};
