// apps/stu/app/ui-kit/progress-bar-demo2/page.tsx
"use client";

import {
  ProgressBar,
  useProgressBar,
} from "@repo/core/exercise/components/ProgressBar";
import { useCallback, useState } from "react";

export default function ProgressBarDemoPage() {
  // This state simulates the progress value you would receive from your backend.
  const [backendProgress, setBackendProgress] = useState(10);

  // 模拟客观题的答题状态：第一次答错后可以重试
  const [isFirstAttempt, setIsFirstAttempt] = useState(true);

  // Pass the backend-driven progress into the hook.
  const { progressBarProps, handleProgress, reset, isAnimating } =
    useProgressBar();

  // 回答正确的处理
  const handleCorrectAnswer = () => {
    const correctComboCount = progressBarProps.correctComboCount + 1;
    // 1. Trigger the "correct" animation on the frontend immediately.
    handleProgress({
      type: "correct",
      text: "太棒了!",
      progress: backendProgress,
      correctComboCount: correctComboCount,
      isAllCorrect: false,
    });

    // 2. Simulate updating the progress after a "backend" response.
    setBackendProgress((p) => Math.min(100, p + 8));
    setIsFirstAttempt(true); // 重置为第一次尝试
  };

  // 客观题第一次回答错误：显示反馈但进度条不变
  const handleFirstIncorrectAnswer = useCallback(() => {
    console.log("handleFirstIncorrectAnswer", backendProgress);

    // 1. 显示错误反馈，但进度条不前进
    handleProgress({
      type: "incorrect",
      text: "再想想，你还有一次机会!",
      progress: progressBarProps.progress, // 进度条保持不变
      correctComboCount: 0, // 重置连击
      isAllCorrect: false,
    });

    // 2. 标记为非第一次尝试，但进度不变
    setIsFirstAttempt(false);
  }, [isAnimating, backendProgress, handleProgress]);

  // 客观题第二次回答错误：显示反馈且进度条前进
  const handleSecondIncorrectAnswer = useCallback(() => {
    // 1. 显示错误反馈，进度条前进
    handleProgress({
      type: "incorrect",
      text: "答案不对，但我们继续下一题!",
      progress: backendProgress,
      correctComboCount: 0,
      isAllCorrect: false,
    });

    // 2. 进度前进（模拟跳到下一题）
    setBackendProgress((p) => Math.min(100, p + 6));
    setIsFirstAttempt(true); // 重置为第一次尝试
  }, [isAnimating, backendProgress, handleProgress]);

  const handleReset = () => {
    // Reset frontend animation state via the hook's controller
    reset();
    // Reset the "backend" progress to its initial value
    setBackendProgress(10);
    // Reset attempt state
    setIsFirstAttempt(true);
  };

  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center gap-16 bg-gray-200 font-sans">
      <h1 className="text-3xl font-bold text-gray-800">
        进度条演示 (后端驱动进度)
      </h1>

      {/* The component receives all its props from the hook */}
      <ProgressBar {...progressBarProps} />

      {/* --- Control Buttons --- */}
      <div className="grid grid-cols-3 gap-4">
        <button
          onClick={handleCorrectAnswer}
          disabled={isAnimating}
          className="progress-demo-button bg-green-500 hover:bg-green-600"
        >
          回答正确
        </button>
        <button
          onClick={handleFirstIncorrectAnswer}
          disabled={isAnimating || !isFirstAttempt}
          className="progress-demo-button bg-yellow-500 hover:bg-yellow-600"
        >
          第一次答错
          <br />
          <span className="text-xs">(进度条不变)</span>
        </button>
        <button
          onClick={handleSecondIncorrectAnswer}
          disabled={isAnimating || isFirstAttempt}
          className="progress-demo-button bg-orange-500 hover:bg-orange-600"
        >
          第二次答错
          <br />
          <span className="text-xs">(进度条前进)</span>
        </button>
        <button
          onClick={handleReset}
          disabled={isAnimating}
          className="progress-demo-button col-span-3 bg-red-500 hover:bg-red-600"
        >
          重置
        </button>
      </div>

      {/* --- 当前状态提示 --- */}
      <div className="text-center">
        <p className="text-lg font-semibold text-gray-700">
          当前状态: {isFirstAttempt ? "第一次尝试" : "第二次尝试"}
        </p>
        <p className="mt-1 text-sm text-gray-500">
          {isFirstAttempt
            ? "可以点击「第一次答错」按钮测试进度条不变的效果"
            : "可以点击「第二次答错」按钮测试进度条前进的效果"}
        </p>
      </div>

      {/* --- State Display for Debugging --- */}
      <div className="mt-4 w-80 rounded-lg bg-white p-4 shadow">
        <h3 className="mb-2 font-bold">调试状态:</h3>
        <pre className="text-sm text-gray-600">
          {JSON.stringify(
            {
              backendProgress,
              isAnimating,
              isFirstAttempt,
              comboCount: progressBarProps.correctComboCount,
              currentProgress: progressBarProps.progress,
            },
            null,
            2
          )}
        </pre>
      </div>

      {/* --- 内联样式用于按钮 --- */}
      <style>{`
        .progress-demo-button {
          padding: 10px 20px;
          border: none;
          border-radius: 8px;
          color: white;
          font-weight: bold;
          cursor: pointer;
          transition: background-color 0.2s, opacity 0.2s;
          text-align: center;
          line-height: 1.2;
        }
        .progress-demo-button:disabled {
          background-color: #a0a0a0 !important;
          cursor: not-allowed;
          opacity: 0.6;
        }
      `}</style>
    </div>
  );
}
