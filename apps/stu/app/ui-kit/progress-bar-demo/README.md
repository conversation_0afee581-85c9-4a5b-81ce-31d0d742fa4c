# 进度条演示组件 (Progress Bar Demo)

## 概述

这个演示组件展示了如何使用 `useProgressBar` hook 来管理练习进度条的状态和动画，特别是客观题的不同答题场景。

## 功能特性

### 🎯 **核心场景演示**

1. **回答正确** - 显示正确反馈，进度条前进，连击计数增加
2. **第一次答错** - 显示错误反馈，**进度条不变**，重置连击计数
3. **第二次答错** - 显示错误反馈，进度条前进，跳到下一题

### 📊 **状态管理**

- `backendProgress`: 模拟后端返回的进度值
- `isFirstAttempt`: 跟踪是否为第一次尝试
- `isAnimating`: 防止动画期间的重复操作
- `correctComboCount`: 连续答对题数

## 使用方式

### 访问 Demo

启动开发服务器后，访问：
```
http://localhost:3000/ui-kit/progress-bar-demo
```

### 操作说明

1. **回答正确** - 点击绿色按钮，观察进度条前进和连击效果
2. **第一次答错** - 点击黄色按钮，观察进度条保持不变但显示错误反馈
3. **第二次答错** - 只有在第一次答错后才能点击，观察进度条前进
4. **重置** - 重置所有状态到初始值

## 技术实现

### Hook 使用

```typescript
const { progressBarProps, handleProgress, reset, isAnimating } = useProgressBar();
```

### 关键逻辑

**第一次答错（进度条不变）：**
```typescript
const handleFirstIncorrectAnswer = () => {
  handleProgress({
    type: "incorrect",
    text: "再想想，你还有一次机会!",
    progress: backendProgress, // 保持不变
    correctComboCount: 0,
    isAllCorrect: false,
  });
  setIsFirstAttempt(false);
};
```

**第二次答错（进度条前进）：**
```typescript
const handleSecondIncorrectAnswer = () => {
  handleProgress({
    type: "incorrect", 
    text: "答案不对，但我们继续下一题!",
    progress: backendProgress,
    correctComboCount: 0,
    isAllCorrect: false,
  });
  setBackendProgress(p => Math.min(100, p + 10)); // 进度前进
  setIsFirstAttempt(true);
};
```

## 设计原则

1. **状态分离** - 基础进度数据与动画状态分离管理
2. **防重复操作** - 使用 `isAnimating` 状态防止动画期间的重复点击
3. **真实场景模拟** - 完全模拟真实练习中的客观题答题逻辑
4. **可视化调试** - 提供状态显示面板，便于理解内部状态变化

## 适用场景

- 客观题（选择题、判断题）的答题反馈
- 需要区分第一次和第二次答错的场景
- 进度条动画效果的测试和调试
- 新开发者学习 useProgressBar hook 的使用方式

## 注意事项

- 第一次答错时进度条不前进，符合给用户第二次机会的设计
- 第二次答错时进度条前进，表示跳到下一题
- 所有动画状态由 `useProgressBar` hook 自动管理
- 按钮的禁用状态确保用户按正确顺序操作
