"use client";
import { useCourseModel } from "@/app/models/course-model";
import { useCourseWidgetModel } from "@/app/models/course-widget-model";
import { LocalCourseProgress } from "@/app/models/local-progress";
import { finishLesson } from "@/app/utils/device";
import { post } from "@/app/utils/fetcher";
import { CourseWidget, WidgetStatus } from "@/types/app/course";
import { useSignal } from "@preact-signals/safe-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import useSWRMutation from "swr/mutation";
import { v4 as uuidv4 } from "uuid";

interface WidgetLoaderItem {
  key: string;
  data: CourseWidget;
}

type CourseSequenceViewmodel = ReturnType<typeof useCourseSequenceViewmodel>;

const useCourseSequenceViewmodel = (
  knowledgeId: number,
  redirectWidgetIndex?: string
) => {
  // 获取loader配置, 从model中获取
  const {
    data,
    error,
    mutate: refreshCourseSummary,
  } = useCourseModel(knowledgeId);
  const {
    lessonId,
    total,
    widgets: widgetSummaries,
    nextQuestionParams,
    currentWidgetIndex,
  } = data;

  const [activeIndex, setActiveIndex] = useState<number>(0);

  const [cursorIndex, setCursorIndex] = useState<number>(0);

  const { trigger: doReportProgress, isMutating } = useSWRMutation(
    "/api/v1/lesson/progress/report",
    post
  );

  const widgetCostTime = useSignal(new Map<number, number>());
  const localProgressRecorder = useMemo(
    () => new LocalCourseProgress(knowledgeId, lessonId),
    [knowledgeId, lessonId]
  );

  const showAnimation = useSignal(false);
  // const widgetSummarySequence = useMemo(() => {
  //   console.log("useMemo", widgets);
  //   return signal(widgets);
  // }, [widgets]);
  // const activeWidgetSummary = useComputed(
  //   () => widgetSummaries[activeIndex.value]
  // );
  const hasNextAnimation = useMemo(() => {
    const currentWidget = widgetSummaries[activeIndex];
    const nextWidget = widgetSummaries[activeIndex + 1];
    return currentWidget?.type !== nextWidget?.type;
  }, [activeIndex, widgetSummaries]);

  // const activeWidgetSummary = useMemo(() => {
  //   return widgetSummaries[activeIndex];
  // }, [activeIndex, widgetSummaries]);

  const [widgetSequence, setWidgetSequence] = useState<WidgetLoaderItem[]>([]);

  const { data: cursorWidget } = useCourseWidgetModel({
    knowledgeId,
    lessonId,
    summary: widgetSummaries[cursorIndex],
    nextQuestionParams,
  });

  // const getActiveWidgetLoader = useCallback(() => {
  //   return widgetSequence[activeIndex];
  // }, [activeIndex, widgetSequence]);

  const activeWidgetLoader = useMemo(() => {
    return widgetSequence[activeIndex];
  }, [activeIndex, widgetSequence]);

  const reportCostTime = useCallback(
    (n: number) => {
      widgetCostTime.value.set(activeIndex, n);
    },
    [activeIndex, widgetCostTime]
  );
  const reportProgress = useCallback(
    async ({
      widgetIndex,
      costSeconds,
      status,
    }: {
      widgetIndex: number;
      costSeconds: number;
      status: WidgetStatus;
    }) => {
      await doReportProgress({
        knowledgeId,
        lessonId,
        widgetIndex,
        costSecond: costSeconds,
        status,
      });
    },
    [doReportProgress, knowledgeId, lessonId]
  );

  const goto = useCallback(
    (index: number) => {
      if (index < 0 || index > total - 1) {
        return;
      }
      setActiveIndex(index);
    },
    [total]
  );

  const next = useCallback(() => {
    if (isMutating) return;

    // if (activeIndex.value >= total) {
    reportProgress({
      widgetIndex: activeIndex,
      costSeconds: widgetCostTime.value.get(activeIndex) ?? 1,
      status: "completed",
    });

    if (hasNextAnimation) {
      showAnimation.value = true;
      return;
    }
    if (activeIndex >= total - 1) {
      console.log("next::----THE END----");
      finishLesson();
      return;
    }
    // 更新widgetSummarySequence
    setActiveIndex(activeIndex + 1);
    refreshCourseSummary();
  }, [
    isMutating,
    activeIndex,
    total,
    hasNextAnimation,
    showAnimation,
    widgetCostTime,
    reportProgress,
    refreshCourseSummary,
  ]);

  const onAnimationComplete = useCallback(() => {
    if (!showAnimation.value) {
      return;
    }
    showAnimation.value = false;
    if (activeIndex >= total - 1) {
      console.log("next::----THE END----");
      finishLesson();
      return;
    }
    // 更新widgetSummarySequence
    setActiveIndex(activeIndex + 1);
    refreshCourseSummary();
  }, [showAnimation, activeIndex, total, refreshCourseSummary]);

  const prev = () => {
    if (activeIndex <= 0) {
      console.log("----THE START----");
      return;
    }
    if (hasNextAnimation) {
      showAnimation.value = true;
      return;
    }
    setActiveIndex(activeIndex - 1);
  };

  useEffect(() => {
    if (redirectWidgetIndex) setActiveIndex(Number(redirectWidgetIndex));
    else setActiveIndex(currentWidgetIndex);
    // setCursorIndex(currentWidgetIndex);
  }, [currentWidgetIndex, redirectWidgetIndex]);

  useEffect(() => {
    if (cursorWidget) {
      const newSequence = [...widgetSequence];
      newSequence[cursorIndex] = {
        key: uuidv4(),
        data: cursorWidget,
      };
      setWidgetSequence(newSequence);
      if (cursorIndex < total) {
        setCursorIndex(cursorIndex + 1);
      }
    }
  }, [cursorWidget, widgetSequence, cursorIndex, total]);

  return {
    knowledgeId,
    lessonId,
    total,
    activeIndex,
    widgetSummarySequence: widgetSummaries,
    activeWidgetLoader,
    widgetSequence,
    next,
    prev,
    goto,
    error: error as Error,
    reportProgress,
    reportCostTime,
    localProgressRecorder,
    showAnimation: showAnimation.value,
    onAnimationComplete,
  };
};

export {
  useCourseSequenceViewmodel,
  type CourseSequenceViewmodel,
  type WidgetLoaderItem,
};
