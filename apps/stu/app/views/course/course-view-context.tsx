"use client";
import {
  createContext,
  FC,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";

import useScreen from "@/app/hooks/use-screen";
import { exitLesson, trackEvent } from "@/app/utils/device";
import {
  CourseSequenceViewmodel,
  useCourseSequenceViewmodel,
} from "@/app/viewmodels/course/course-sequence-vm";
import { Signal, useSignal } from "@preact-signals/safe-react";
import { GuideMode } from "@repo/core/types/data/widget-guide";

type CourseViewContextType = CourseSequenceViewmodel & {
  isProgressBarOpen: boolean;
  setIsProgressBarOpen: (isProgressBarOpen: boolean) => void;
  guideMode: GuideMode;
  setGuideMode: (guideMode: GuideMode) => void;
  showSubtitle: boolean;
  setShowSubtitle: (showSubtitle: boolean) => void;
  playRate: number;
  setPlayRate: (playRate: number) => void;
  exit: () => void;
  scrollable: Signal<boolean>;
  screen: { width: number; height: number };
  trackEventWithLessonId: (eventID: string, needWidgetInfo?: boolean) => void;
  subjectId: number;
  knowledgeName: string;
  lessonName: string;
  redirectCommentId?: string;
  redirectCommentRootId?: string;
  redirectReferenceId?: string;
  hadShownRedirectComment: Signal<boolean>;
};

const CourseViewContext = createContext<CourseViewContextType>(
  {} as CourseViewContextType
);

const useCourseViewContext = () => useContext(CourseViewContext);

interface CourseViewProviderProps {
  knowledgeId: number;
  subjectId: number;
  knowledgeName: string;
  lessonName: string;
  children: ReactNode;
  redirectWidgetIndex?: string;
  redirectCommentId?: string;
  redirectCommentRootId?: string;
  redirectReferenceId?: string;
}

const CourseViewProvider: FC<CourseViewProviderProps> = ({
  knowledgeId,
  subjectId,
  knowledgeName,
  lessonName,
  children,
  redirectCommentId,
  redirectCommentRootId,
  redirectReferenceId,
  redirectWidgetIndex,
}) => {
  const screen = useScreen();
  // 获取数据及操作数据的方法
  const sequenceVm = useCourseSequenceViewmodel(
    knowledgeId,
    redirectWidgetIndex
  );
  const { lessonId, activeWidgetLoader } = sequenceVm;
  const [isProgressBarOpen, setIsProgressBarOpen] = useState(false);
  const [guideMode, setGuideMode] = useState(GuideMode.follow);
  const [showSubtitle, setShowSubtitle] = useState(true);
  const [playRate, setPlayRate] = useState(1);
  const scrollable = useSignal(true);
  const hadShownRedirectComment = useSignal(false);

  const trackEventWithLessonId = useCallback(
    (eventID: string, needWidgetInfo?: boolean) => {
      const map = needWidgetInfo
        ? {
            widgetIndex: activeWidgetLoader?.data.index,
            widgetType: activeWidgetLoader?.data.type,
          }
        : {};
      trackEvent(eventID, {
        lesson_id: lessonId,
        ...map,
      });
    },
    [lessonId, activeWidgetLoader]
  );

  const exit = useCallback(() => {
    trackEventWithLessonId("lesson_exit_click", true);
    exitLesson();
  }, [trackEventWithLessonId]);

  useEffect(() => {
    const handler = () => {
      if (isProgressBarOpen) {
        setIsProgressBarOpen(false);
      }
    };
    document.body.addEventListener("click", handler);

    return () => {
      document.body.removeEventListener("click", handler);
    };
  }, [isProgressBarOpen]);

  const value = {
    ...sequenceVm,
    isProgressBarOpen,
    setIsProgressBarOpen,
    guideMode,
    setGuideMode,
    showSubtitle,
    setShowSubtitle,
    playRate,
    setPlayRate,
    exit,
    scrollable,
    screen,
    trackEventWithLessonId,
    subjectId,
    knowledgeName,
    lessonName,
    redirectCommentId,
    redirectCommentRootId,
    redirectReferenceId,
    hadShownRedirectComment,
  };
  return (
    <CourseViewContext.Provider value={value}>
      {children}
    </CourseViewContext.Provider>
  );
};

export {
  CourseViewContext,
  CourseViewProvider,
  GuideMode,
  useCourseViewContext,
  type CourseViewContextType,
};
