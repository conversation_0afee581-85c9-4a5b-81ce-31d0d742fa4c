"use client";
import { cn } from "@repo/ui/lib/utils";
import { ComponentProps, FC } from "react";
import { CourseProgressBarView } from "./course-progress-bar-view";
import { CourseViewProvider } from "./course-view-context";
import { CourseWidgetsLoaderView } from "./course-widgets-loader-view";

export const CourseView: FC<
  {
    knowledgeId: number;
    subjectId: number;
    knowledgeName: string;
    lessonName: string;
    widgetIndex?: string;
    commentId?: string;
    commentRootId?: string;
    referenceId?: string;
  } & ComponentProps<"div">
> = ({
  className,
  knowledgeId,
  subjectId,
  knowledgeName,
  lessonName,
  widgetIndex,
  commentId,
  commentRootId,
  referenceId,
}) => {
  return (
    <CourseViewProvider
      knowledgeId={knowledgeId}
      subjectId={subjectId}
      knowledgeName={knowledgeName}
      lessonName={lessonName}
      redirectWidgetIndex={widgetIndex}
      redirectCommentId={commentId}
      redirectCommentRootId={commentRootId}
      redirectReferenceId={referenceId}
    >
      <div className={cn("relative h-screen w-full", className)}>
        <CourseProgressBarView />
        <CourseWidgetsLoaderView />
      </div>
    </CourseViewProvider>
  );
};
