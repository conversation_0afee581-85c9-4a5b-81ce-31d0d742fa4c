# 无限滚动组件 (InfinityScroll)

一个功能完整的无限滚动列表组件，支持下拉刷新和上拉加载功能。

## 特性

- ✅ **下拉刷新** - 支持下拉刷新数据
- ✅ **上拉加载** - 支持滚动到底部自动加载更多
- ✅ **自定义样式** - 支持外部传入 className
- ✅ **自定义指示器** - 支持自定义刷新和加载指示器
- ✅ **触摸优化** - 针对移动端触摸交互优化
- ✅ **性能优化** - 使用防抖和 useCallback 优化性能
- ✅ **TypeScript** - 完整的 TypeScript 类型支持

## 基本用法

```tsx
import { InfinityScroll } from '@/components/common/infinity-scroll';

function MyList() {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  const handleRefresh = async () => {
    // 刷新数据逻辑
    const newData = await fetchData();
    setItems(newData);
  };

  const handleLoadMore = async () => {
    // 加载更多数据逻辑
    setLoading(true);
    const moreData = await fetchMoreData();
    setItems(prev => [...prev, ...moreData]);
    setLoading(false);
  };

  return (
    <InfinityScroll
      className="h-full"
      enablePullRefresh={true}
      enableLoadMore={true}
      hasMore={hasMore}
      loading={loading}
      onRefresh={handleRefresh}
      onLoadMore={handleLoadMore}
    >
      {items.map(item => (
        <div key={item.id}>{item.content}</div>
      ))}
    </InfinityScroll>
  );
}
```

## API 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `className` | `string` | - | 外部传入的样式类名 |
| `children` | `React.ReactNode` | - | 子组件内容 |
| `enablePullRefresh` | `boolean` | `true` | 是否启用下拉刷新 |
| `enableLoadMore` | `boolean` | `true` | 是否启用上拉加载 |
| `hasMore` | `boolean` | `true` | 是否还有更多数据 |
| `loading` | `boolean` | `false` | 是否正在加载 |
| `onRefresh` | `() => Promise<void> \| void` | - | 下拉刷新回调函数 |
| `onLoadMore` | `() => Promise<void> \| void` | - | 上拉加载回调函数 |
| `refreshThreshold` | `number` | `60` | 下拉刷新触发阈值(px) |
| `loadMoreThreshold` | `number` | `100` | 上拉加载触发阈值(px) |
| `refreshIndicator` | `(props) => ReactNode` | - | 自定义下拉刷新指示器 |
| `loadMoreIndicator` | `(props) => ReactNode` | - | 自定义上拉加载指示器 |

## 自定义指示器

### 下拉刷新指示器

```tsx
const customRefreshIndicator = ({ status, distance }) => {
  return (
    <div className="flex items-center justify-center py-4">
      {status === PullRefreshStatus.REFRESHING ? (
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600" />
      ) : (
        <div>下拉距离: {distance}px</div>
      )}
    </div>
  );
};
```

### 上拉加载指示器

```tsx
const customLoadMoreIndicator = ({ status }) => {
  const getText = () => {
    switch (status) {
      case LoadMoreStatus.LOADING: return "加载中...";
      case LoadMoreStatus.NO_MORE: return "没有更多了";
      case LoadMoreStatus.ERROR: return "加载失败，点击重试";
      default: return "";
    }
  };

  return (
    <div className="text-center py-4">
      {getText()}
    </div>
  );
};
```

## 状态枚举

### PullRefreshStatus (下拉刷新状态)

- `IDLE` - 空闲状态
- `PULLING` - 下拉中
- `READY` - 准备刷新
- `REFRESHING` - 刷新中

### LoadMoreStatus (上拉加载状态)

- `IDLE` - 空闲状态
- `LOADING` - 加载中
- `NO_MORE` - 没有更多数据
- `ERROR` - 加载错误

## 使用示例

查看 `infinity-scroll-example.tsx` 文件获取完整的使用示例。

## 注意事项

1. **容器高度**: 确保组件有明确的高度，否则可能无法正确触发滚动事件
2. **异步处理**: `onRefresh` 和 `onLoadMore` 支持异步函数，组件会自动处理加载状态
3. **触摸事件**: 组件针对移动端优化，在桌面端也可以正常使用鼠标滚动
4. **性能优化**: 组件内部使用了防抖和 useCallback 来优化性能

## 与项目集成

该组件遵循项目的设计规范：

- 使用 `@repo/ui/lib/utils` 的 `cn` 函数处理样式
- 支持 Tailwind CSS 类名
- 完整的 TypeScript 类型支持
- 遵循项目的代码风格和命名约定
