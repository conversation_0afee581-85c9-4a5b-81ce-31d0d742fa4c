"use client";

import { cn } from "@repo/ui/lib/utils";
import React, { useCallback, useEffect, useRef, useState } from "react";

/**
 * 下拉刷新状态
 */
export enum PullRefreshStatus {
  IDLE = "idle", // 空闲状态
  PULLING = "pulling", // 下拉中
  READY = "ready", // 准备刷新
  REFRESHING = "refreshing", // 刷新中
}

/**
 * 上拉加载状态
 */
export enum LoadMoreStatus {
  IDLE = "idle", // 空闲状态
  LOADING = "loading", // 加载中
  NO_MORE = "no_more", // 没有更多数据
  ERROR = "error", // 加载错误
}

/**
 * 无限滚动组件的属性接口
 */
export interface InfinityScrollProps {
  /** 外部传入的 className */
  className?: string;
  /** 子组件 */
  children: React.ReactNode;
  /** 是否启用下拉刷新 */
  enablePullRefresh?: boolean;
  /** 是否启用上拉加载 */
  enableLoadMore?: boolean;
  /** 是否还有更多数据 */
  hasMore?: boolean;
  /** 是否正在加载 */
  loading?: boolean;
  /** 下拉刷新回调 */
  onRefresh?: () => Promise<void> | void;
  /** 上拉加载回调 */
  onLoadMore?: () => Promise<unknown> | void;
  /** 下拉刷新阈值（px） */
  refreshThreshold?: number;
  /** 上拉加载阈值（px） */
  loadMoreThreshold?: number;
  /** 自定义下拉刷新指示器 */
  refreshIndicator?: (props: {
    status: PullRefreshStatus;
    distance: number;
  }) => React.ReactNode;
  /** 自定义上拉加载指示器 */
  loadMoreIndicator?: (props: { status: LoadMoreStatus }) => React.ReactNode;
}

/**
 * 默认下拉刷新指示器
 */
const DefaultRefreshIndicator = ({ status }: { status: PullRefreshStatus }) => {
  const getStatusText = () => {
    switch (status) {
      case PullRefreshStatus.PULLING:
        return "下拉刷新";
      case PullRefreshStatus.READY:
        return "释放刷新";
      case PullRefreshStatus.REFRESHING:
        return "刷新中...";
      default:
        return "";
    }
  };

  const getRotation = () => {
    if (status === PullRefreshStatus.READY) return "rotate-180";
    if (status === PullRefreshStatus.REFRESHING) return "animate-spin";
    return "";
  };

  return (
    <div className="flex flex-col items-center justify-center py-4 text-gray-500">
      <div
        className={cn("mb-2 transition-transform duration-200", getRotation())}
      >
        {status === PullRefreshStatus.REFRESHING ? (
          <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500" />
        ) : (
          <svg
            className="h-5 w-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 14l-7 7m0 0l-7-7m7 7V3"
            />
          </svg>
        )}
      </div>
      <span className="text-sm">{getStatusText()}</span>
    </div>
  );
};

/**
 * 默认上拉加载指示器
 */
const DefaultLoadMoreIndicator = ({ status }: { status: LoadMoreStatus }) => {
  const getStatusText = () => {
    switch (status) {
      case LoadMoreStatus.LOADING:
        return "加载中...";
      case LoadMoreStatus.NO_MORE:
        return "没有更多数据了";
      case LoadMoreStatus.ERROR:
        return "加载失败，点击重试";
      default:
        return "";
    }
  };

  return (
    <div className="flex items-center justify-center py-4 text-gray-500">
      {status === LoadMoreStatus.LOADING && (
        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500" />
      )}
      <span className="text-sm">{getStatusText()}</span>
    </div>
  );
};

/**
 * 无限滚动组件
 * 支持下拉刷新和上拉加载功能
 */
export const InfinityScroll: React.FC<InfinityScrollProps> = ({
  className,
  children,
  enablePullRefresh = true,
  enableLoadMore = true,
  hasMore = true,
  loading = false,
  onRefresh,
  onLoadMore,
  refreshThreshold = 60,
  loadMoreThreshold = 100,
  refreshIndicator = (props) => <DefaultRefreshIndicator {...props} />,
  loadMoreIndicator = (props) => <DefaultLoadMoreIndicator {...props} />,
}) => {
  // 容器引用
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // 下拉刷新状态
  const [pullRefreshStatus, setPullRefreshStatus] = useState<PullRefreshStatus>(
    PullRefreshStatus.IDLE
  );
  const [pullDistance, setPullDistance] = useState(0);

  // 上拉加载状态
  const [loadMoreStatus, setLoadMoreStatus] = useState<LoadMoreStatus>(
    LoadMoreStatus.IDLE
  );

  // 触摸相关状态
  const [touchStartY, setTouchStartY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  /**
   * 处理触摸开始
   */
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      if (!enablePullRefresh) return;

      const touch = e.touches[0];
      if (touch) {
        setTouchStartY(touch.clientY);
        setIsDragging(false);
      }
    },
    [enablePullRefresh]
  );

  /**
   * 处理触摸移动
   */
  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (
        !enablePullRefresh ||
        pullRefreshStatus === PullRefreshStatus.REFRESHING
      )
        return;

      const container = containerRef.current;
      if (!container) return;

      const touch = e.touches[0];
      if (!touch) return;
      const deltaY = touch.clientY - touchStartY;
      const currentScrollTop = container.scrollTop;

      // 只有在顶部且向下拉时才处理下拉刷新
      if (currentScrollTop <= 0 && deltaY > 0) {
        setIsDragging(true);

        // 计算拉动距离，添加阻尼效果
        const distance = Math.min(deltaY * 0.5, refreshThreshold * 2);
        setPullDistance(distance);

        // 更新状态
        if (distance >= refreshThreshold) {
          setPullRefreshStatus(PullRefreshStatus.READY);
        } else {
          setPullRefreshStatus(PullRefreshStatus.PULLING);
        }
      }
    },
    [enablePullRefresh, pullRefreshStatus, touchStartY, refreshThreshold]
  );

  /**
   * 处理触摸结束
   */
  const handleTouchEnd = useCallback(async () => {
    if (!enablePullRefresh || !isDragging) return;

    setIsDragging(false);

    // 如果达到刷新阈值，执行刷新
    if (pullRefreshStatus === PullRefreshStatus.READY && onRefresh) {
      setPullRefreshStatus(PullRefreshStatus.REFRESHING);
      try {
        await onRefresh();
      } catch (error) {
        console.error("Refresh failed:", error);
      } finally {
        setPullRefreshStatus(PullRefreshStatus.IDLE);
        setPullDistance(0);
      }
    } else {
      // 回弹动画
      setPullRefreshStatus(PullRefreshStatus.IDLE);
      setPullDistance(0);
    }
  }, [enablePullRefresh, isDragging, pullRefreshStatus, onRefresh]);

  /**
   * 处理加载更多
   */
  const handleLoadMore = useCallback(async () => {
    if (!onLoadMore || loadMoreStatus === LoadMoreStatus.LOADING) return;

    setLoadMoreStatus(LoadMoreStatus.LOADING);
    try {
      await onLoadMore();
      setLoadMoreStatus(LoadMoreStatus.IDLE);
    } catch (error) {
      console.error("Load more failed:", error);
      setLoadMoreStatus(LoadMoreStatus.ERROR);
    }
  }, [onLoadMore, loadMoreStatus]);

  /**
   * 处理滚动事件
   */
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const container = e.currentTarget;
      const scrollTop = container.scrollTop;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;

      // 检查是否需要加载更多
      if (
        enableLoadMore &&
        hasMore &&
        !loading &&
        loadMoreStatus !== LoadMoreStatus.LOADING &&
        scrollHeight - scrollTop - clientHeight <= loadMoreThreshold
      ) {
        handleLoadMore();
      }
    },
    [
      enableLoadMore,
      hasMore,
      loading,
      loadMoreStatus,
      loadMoreThreshold,
      handleLoadMore,
    ]
  );
  /**
   * 添加非被动的触摸事件监听器来处理下拉刷新
   */
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !enablePullRefresh) return;

    const handleTouchMoveNonPassive = (e: TouchEvent) => {
      if (pullRefreshStatus === PullRefreshStatus.REFRESHING) return;

      const touch = e.touches[0];
      if (!touch) return;

      const deltaY = touch.clientY - touchStartY;
      const currentScrollTop = container.scrollTop;

      // 只有在顶部且向下拉时才阻止默认行为
      if (currentScrollTop <= 0 && deltaY > 0 && isDragging) {
        e.preventDefault();
      }
    };

    // 添加非被动的事件监听器
    container.addEventListener("touchmove", handleTouchMoveNonPassive, {
      passive: false,
    });

    return () => {
      container.removeEventListener("touchmove", handleTouchMoveNonPassive);
    };
  }, [enablePullRefresh, pullRefreshStatus, touchStartY, isDragging]);

  /**
   * 更新加载状态
   */
  useEffect(() => {
    if (!hasMore && loadMoreStatus === LoadMoreStatus.IDLE) {
      setLoadMoreStatus(LoadMoreStatus.NO_MORE);
    } else if (hasMore && loadMoreStatus === LoadMoreStatus.NO_MORE) {
      setLoadMoreStatus(LoadMoreStatus.IDLE);
    }
  }, [hasMore, loadMoreStatus]);

  /**
   * 处理加载错误重试
   */
  const handleRetry = useCallback(() => {
    if (loadMoreStatus === LoadMoreStatus.ERROR) {
      handleLoadMore();
    }
  }, [loadMoreStatus, handleLoadMore]);

  // 计算下拉刷新指示器的样式
  const refreshIndicatorStyle = {
    transform: `translateY(${pullDistance - refreshThreshold}px)`,
    transition: isDragging ? "none" : "transform 0.3s ease-out",
  };

  // 计算内容区域的样式
  const contentStyle = {
    transform: `translateY(${Math.max(0, pullDistance)}px)`,
    transition: isDragging ? "none" : "transform 0.3s ease-out",
  };

  return (
    <div
      ref={containerRef}
      className="relative h-full w-full flex-1 overflow-auto"
      onScroll={handleScroll}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* 下拉刷新指示器 */}
      {enablePullRefresh && (
        <div
          className="absolute left-0 right-0 top-0 z-10 flex justify-center"
          style={refreshIndicatorStyle}
        >
          {refreshIndicator({
            status: pullRefreshStatus,
            distance: pullDistance,
          })}
        </div>
      )}

      {/* 内容区域 */}
      <div ref={contentRef} style={contentStyle} className={className}>
        {children}
      </div>

      {/* 上拉加载指示器 */}
      {enableLoadMore && hasMore && (
        <div
          className="flex justify-center"
          onClick={
            loadMoreStatus === LoadMoreStatus.ERROR ? handleRetry : undefined
          }
          style={{
            cursor:
              loadMoreStatus === LoadMoreStatus.ERROR ? "pointer" : "default",
          }}
        >
          {loadMoreIndicator({
            status: loading ? LoadMoreStatus.LOADING : loadMoreStatus,
          })}
        </div>
      )}
    </div>
  );
};
