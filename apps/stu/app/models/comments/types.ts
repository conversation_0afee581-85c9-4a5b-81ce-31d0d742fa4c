/**
 * 评论模块 - TypeScript 类型定义（前端传参）
 */

// ==================== 前端传参类型 ====================

// 请求参数类型从 schema 推断，避免重复定义
import type { z } from "zod";
import type {
  AddCommentPayloadSchema,
  DeleteCommentPayloadSchema,
  LikeCommentPayloadSchema,
  ReplyCommentPayloadSchema,
  UnlikeCommentPayloadSchema,
} from "./schemas";

export type AddCommentPayload = z.infer<typeof AddCommentPayloadSchema>;
export type DeleteCommentPayload = z.infer<typeof DeleteCommentPayloadSchema>;
export type ReplyCommentPayload = z.infer<typeof ReplyCommentPayloadSchema>;
export type LikeCommentPayload = z.infer<typeof LikeCommentPayloadSchema>;
export type UnlikeCommentPayload = z.infer<typeof UnlikeCommentPayloadSchema>;

// 从 schema 推断类型，避免重复定义
import type {
  ApiComment,
  ApiCommentsListData,
  ApiExtraInfo,
  ApiPageInfo,
  ApiPicReferenceCoordinate,
  ApiReferenceCoordinate,
  ApiReferenceItem,
  ApiReferencePosition,
  ApiReferencesListData,
  ApiTextReferenceCoordinate,
} from "./schemas";

export type ReferencePosition = ApiReferencePosition;
export type TextReferenceCoordinate = ApiTextReferenceCoordinate;
export type PicReferenceCoordinate = ApiPicReferenceCoordinate;
export type ReferenceCoordinate = ApiReferenceCoordinate;

// 以上类型已从 schema 推断，移除重复定义

/**
 * 获取一级评论列表请求参数
 */
export interface GetCommentsParams {
  /** 对象ID(对象类型为:100,objId就是课程ID) */
  objId: number;
  /** 对象类型(100:课程,200:笔记,300:社区) */
  objType: number;
  /** 页码 */
  page: number;
  /** 每页大小 */
  pageSize: number;
  /** 多个referenceID,用逗号分隔 */
  referenceId: string;
  /** 当前时间的时间戳 */
  baseTime: number;
}

/**
 * 获取二级评论列表请求参数
 */
export interface GetSubCommentsParams {
  /** 对象ID(对象类型为:100,objId就是课程ID) */
  objId: number;
  /** 对象类型(100:课程,200:笔记,300:社区) */
  objType: number;
  /** 页码 */
  page: number;
  /** 每页大小 */
  pageSize: number;
  /** 根据此ID,查询其子评论(一级评论列表里有这个值) */
  commentRootId: number;
  /** 当前的时间戳 */
  baseTime?: number;
}

/**
 * 获取引用列表请求参数
 */
export interface GetReferencesParams {
  /** 对象ID */
  objId: number;
  /** 对象类型 */
  objType: number;
  /** 页码 */
  page: number;
  /** 每页大小 */
  pageSize: number;
}

// ==================== 前端使用的数据类型 ====================
// 以下类型从 schema 推断，避免重复定义

export type Comment = ApiComment;
export type PageInfo = ApiPageInfo;
export type ExtraInfo = ApiExtraInfo;
export type CommentsListData = ApiCommentsListData;
export type ReferenceItem = ApiReferenceItem;
export type ReferencesListData = ApiReferencesListData;
