# Comments Model 层

## 概述
评论模块的数据模型层，负责与后端API交互，遵循MVVM架构模式。

## 🚀 功能特性

### 数据获取
- **获取评论列表** - 支持分页、父评论筛选、根评论展开等功能

### 数据操作
- **发布评论** - 支持文本、图片、引用内容等多种类型
- **删除评论** - 删除指定评论
- **回复评论** - 回复指定评论
- **点赞/取消点赞** - 评论点赞功能

## 📋 可用 Hooks

### 数据获取 Hooks

| Hook | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `useCommentsList` | `GetCommentsParams` | `{ data, error, isLoading, refresh }` | 获取一级评论列表 |
| `useSubCommentsList` | `GetSubCommentsParams` | `{ data, error, isLoading, refresh }` | 获取二级评论列表 |

### 数据操作 Hooks

| Hook | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `useAddComment` | - | `{ addComment, isAdding, error }` | 发布评论 |
| `useDeleteComment` | - | `{ deleteComment, isDeleting, error }` | 删除评论 |
| `useReplyComment` | - | `{ replyComment, isReplying, error }` | 回复评论 |
| `useLikeComment` | - | `{ likeComment, isLiking, error }` | 点赞评论 |

## 🔧 使用示例

### 获取一级评论列表
```typescript
import { useCommentsList } from '@/models/comments';

function CommentsComponent() {
  const { data, error, isLoading, refresh } = useCommentsList({
    objId: 6,
    objType: 100,
    page: 1,
    pageSize: 10,
    referenceId: 1, // 划线内容的ID
  });

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>加载失败</div>;

  return (
    <div>
      <h3>评论列表 (共 {data?.extraInfo.totalComments} 条)</h3>
      {data?.list.map(comment => (
        <div key={comment.commentId}>
          <p>{comment.commentContent}</p>
          <span>👍 {comment.likeCount}</span>
        </div>
      ))}
    </div>
  );
}
```

### 发布评论
```typescript
import { useAddComment } from '@/models/comments';

function AddCommentComponent() {
  const { addComment, isAdding, error } = useAddComment();

  const handleSubmit = async () => {
    try {
      await addComment({
        objId: 6,
        objType: 100,
        subject_id: 1,
        courseName: "数学",
        commentContent: "这是一条评论",
        commentScope: 2,
        referenceType: 1,
        referenceContent: "引用内容",
        referenceImage: "",
        referencePosition: [],
        widgetIndex: 5,
        widgetType: "TextParagraph",
        widgetName: "学习方法章节",
      });
      // 发布成功后的处理
    } catch (err) {
      console.error('发布失败:', err);
    }
  };

  return (
    <button onClick={handleSubmit} disabled={isAdding}>
      {isAdding ? '发布中...' : '发布评论'}
    </button>
  );
}
```

### 点赞评论
```typescript

function LikeButtonComponent({ commentId, isLiked }: { commentId: number, isLiked: boolean }) {
  const { likeComment, isLiking } = useLikeComment();

  const handleToggleLike = async () => {
    try {
      if (isLiked) {
        await unlikeComment({ commentId });
      } else {
        await likeComment({ commentId });
      }
    } catch (err) {
      console.error('操作失败:', err);
    }
  };

  return (
    <button
      onClick={handleToggleLike}
      disabled={isLiking || isUnliking}
    >
      {isLiked ? '❤️' : '🤍'}
      {(isLiking || isUnliking) && '...'}
    </button>
  );
}
```

## 📊 核心数据结构

### 评论信息
```typescript
interface Comment {
  commentId: number;           // 评论ID
  commentContent: string;      // 评论内容
  parentContent: string | null; // 父评论内容
  status: number;              // 状态
  llmScore: number;            // LLM评分
  likeCount: number;           // 点赞数
  subLikeCount: number;        // 子评论点赞数
  teacherRecommended: number;  // 老师推荐
  userId: number;              // 用户ID
  userName: string;            // 用户名
  userClassInfo: string;       // 用户班级信息
  publishedTimeStr: string;    // 发布时间字符串
  publishedRange: number;      // 发布范围
  course: string;              // 课程
  belongsToModule: string;     // 所属模块
}
```

### 引用位置信息
```typescript
interface ReferencePosition {
  data: ReferenceCoordinate[];  // 定位数据数组
}

// 文字类型引用坐标
interface TextReferenceCoordinate {
  lineId: string;              // 行ID
  textureId: string;           // 纹理ID
  start: string;               // 开始位置
  content: string;             // 内容
  end: string;                 // 结束位置
  type: "text";                // 类型：文字
}

// 图片类型引用坐标
interface PicReferenceCoordinate {
  lineId: string;              // 行ID
  content: string;             // 内容（图片URL）
  type: "pic";                 // 类型：图片
}

type ReferenceCoordinate = TextReferenceCoordinate | PicReferenceCoordinate;
```

## ⚠️ 使用注意事项

1. **类型安全**: 所有Hook都有完整的TypeScript类型定义
2. **错误处理**: 每个Hook都提供error状态用于错误处理
3. **加载状态**: 提供loading状态用于UI展示
4. **数据刷新**: GET类接口提供refresh方法用于手动刷新
5. **参数校验**: 所有数值参数都设置了合理的范围约束
6. **数据校验**: 使用Zod对后端返回数据进行严格校验

## 🔄 数据流转

1. **数据获取**: 使用SWR进行数据缓存和自动重新验证
2. **数据校验**: 使用Zod对后端返回数据进行严格校验
3. **状态管理**: 通过Hook返回的状态进行UI状态管理
4. **错误处理**: 统一的错误处理机制

## 🧪 API 接口映射

| Hook | API 端点 | 方法 | 说明 |
|------|----------|------|------|
| `useCommentsList` | `/api/v1/comments/list` | GET | 获取一级评论列表 |
| `useSubCommentsList` | `/api/v1/comments/sub-list` | GET | 获取二级评论列表 |
| `useAddComment` | `/api/v1/comments/add` | POST | 发布评论 |
| `useDeleteComment` | `/api/v1/comments/delete` | POST | 删除评论 |
| `useReplyComment` | `/api/v1/comments/reply` | POST | 回复评论 |
| `useLikeComment` | `/api/v1/comment/like` | POST | 点赞评论 |
