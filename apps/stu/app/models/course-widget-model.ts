import { ApiLessonWidget } from "@/types/api/lesson";
import { CourseWidget, CourseWidgetSummary } from "@/types/app/course";
import { ApiGetNextQuestionData } from "@repo/core/exercise/model";
import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import useSWR from "swr";
import { get } from "../utils/fetcher";

const getApiUrl = ({
  knowledgeId,
  lessonId,
  summary,
  nextQuestionParams,
}: {
  knowledgeId: number;
  lessonId: number;
  summary: CourseWidgetSummary | undefined;
  nextQuestionParams?: string;
}) => {
  if (!summary) {
    return null;
  }

  const { type, index, cdnUrl } = summary;

  if (type === "exercise") {
    return `/api/v1/study_session/next_question?widgetIndex=${index}&${nextQuestionParams}`;
    // return `/api/v1/lesson/widget/info?knowledgeId=${knowledgeId}&lessonId=${lessonId}&widgetIndex=${index}`;
  } else if (type == "guide" && cdnUrl) {
    return cdnUrl;
    // ? cdnUrl
    // : `/api/v1/lesson/widget/info?knowledgeId=${knowledgeId}&lessonId=${lessonId}&widgetIndex=${index}`;
  } else {
    return `/api/v1/lesson/widget/info?knowledgeId=${knowledgeId}&lessonId=${lessonId}&widgetIndex=${index}`;
  }
};

const useCourseWidgetModel = ({
  knowledgeId,
  lessonId,
  summary,
  nextQuestionParams,
}: {
  knowledgeId: number;
  lessonId: number;
  summary: CourseWidgetSummary | undefined;
  nextQuestionParams?: string;
}) => {
  const { data, isLoading } = useSWR(
    getApiUrl({ knowledgeId, lessonId, summary, nextQuestionParams }),
    (url) => get<GuideWidgetData | ApiGetNextQuestionData>(url, {})
  );

  if (!data) {
    return {
      data,
      isLoading,
    };
  }

  const parse = (
    data: GuideWidgetData | ApiGetNextQuestionData | ApiLessonWidget
  ) => {
    if (!summary) {
      return undefined;
    }
    const { index, name, type, cdnUrl } = summary;

    if (type === "exercise") {
      return {
        index,
        name,
        type,
        data,
      } as CourseWidget<"exercise">;
    }

    if (type === "guide") {
      if (cdnUrl) {
        return {
          index,
          name,
          type,
          data,
        } as CourseWidget<"guide">;
      } else {
        // 兼容旧版，旧版没有cdnUrl
        const widgetData = data as ApiLessonWidget;
        return {
          index,
          name,
          type,
          data: widgetData.data,
        } as CourseWidget<"guide">;
      }
    }

    if (type === "video") {
      const widgetData = data as ApiLessonWidget;
      return {
        index,
        name,
        type,
        data: widgetData.data,
      } as CourseWidget<"video">;
    }

    if (type === "interactive") {
      const widgetData = data as ApiLessonWidget;
      return {
        index,
        name,
        type,
        data: widgetData.data,
      } as CourseWidget<"interactive">;
    }

    return undefined;
  };

  return {
    data: parse(data),
    isLoading,
  };
};

export { useCourseWidgetModel };
