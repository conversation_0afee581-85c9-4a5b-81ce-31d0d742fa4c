import { ApiLessonSummary } from "@/types/api/lesson";
import useS<PERSON> from "swr";

import { CourseSummary, WidgetStatus, WidgetType } from "@/types/app/course";
import { get } from "../utils/fetcher";

const useCourseModel = (knowledgeId: number) => {
  const { data, isLoading, error, mutate } = useSWR(
    `/api/v1/lesson/info?knowledgeId=${knowledgeId}`,
    (url) => get<ApiLessonSummary>(url, {})
  );

  if (!data) {
    return {
      data: {
        lessonId: 0,
        name: "",
        theme: "",
        total: 0,
        currentWidgetIndex: 0,
        widgets: [],
        nextQuestionParams: "",
      } as CourseSummary,
      isLoading,
      error,
      mutate,
    };
  }

  const {
    lessonId,
    lessonName,
    theme,
    totalWidgetNum,
    lessonWidgets,
    nextQuestionParams,
    currentWidgetIndex,
  } = data;

  const lessonSummary: CourseSummary = {
    lessonId,
    name: lessonName,
    theme: theme,
    total: totalWidgetNum,
    currentWidgetIndex,
    nextQuestionParams,
    widgets: lessonWidgets.map((widget) => {
      const { widgetIndex, widgetName, widgetType, status, cdnUrl } = widget;
      return {
        index: widgetIndex,
        name: widgetName,
        type: widgetType as WidgetType,
        status: status as WidgetStatus,
        cdnUrl,
      };
    }),
  };

  return {
    data: lessonSummary,
    isLoading,
    error,
    mutate,
  };
};

export { useCourseModel };
