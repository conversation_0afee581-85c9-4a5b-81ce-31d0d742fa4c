import { request } from '@umijs/max';
import type { SemesterData, Gradesetting } from './type';
// const BASE_URL = '';

// 获取学期列表
export async function getSemesterList(params: {
  school_id: number;
  school_year_id: number;
}) {
  return request<API.ResponseBody<SemesterData>>('/api/v1/semester/get', {
    method: 'GET',
    params,
  });
}



// 更新学期列表
export async function updateSemesterList(params: {
  school_id: number;
  school_year_id: number;
  grade_settings: Gradesetting[];
}) {
  return request<API.ResponseBody<SemesterData>>('/api/v1/semester/update', {
    method: 'POST',
    data: params,
  });
}
