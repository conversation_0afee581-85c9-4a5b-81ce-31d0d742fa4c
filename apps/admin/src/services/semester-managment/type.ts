
export interface SemesterData {
  school_id: number;
  school_year_id: number;
  school_year_name: string;
  grade_settings: Gradesetting[];
  start_date: string;
  end_date: string;
}

export interface Gradesetting {
  grade_id: number;
  grade_name: string;
  semesters: Semester[];
}

export interface Semester {
  semester_id?: number;
  semester_type_id: number;
  semester_name: string;
  start_date: string;
  end_date: string;
}