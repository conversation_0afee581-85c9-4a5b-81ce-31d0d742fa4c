# Services API 文档

## 学生行为处理接口

### 统一接口：handleStudentBehavior

用于处理学生的表扬和提醒行为的统一接口。

#### 接口地址
```
POST /api/v1/behavior/student/handle
```

#### 请求参数

```typescript
interface StudentBehaviorParams {
  studentIds: number[];           // 学生ID列表
  behaviorType: 'task_praise' | 'task_attention';  // 行为类型
  taskId: number;                 // 任务ID
  assignId: number;               // 任务布置ID
  content?: string;               // 行为内容（提醒时必填，表扬时可选）
}
```

#### 使用示例

##### 1. 直接使用统一接口

```typescript
import { handleStudentBehavior } from '@/services/homework';

// 表扬学生
await handleStudentBehavior({
  studentIds: [123, 456],
  behaviorType: 'task_praise',
  taskId: 67890,
  assignId: 11111,
  content: '表现优秀！' // 可选
});

// 提醒学生
await handleStudentBehavior({
  studentIds: [789],
  behaviorType: 'task_attention',
  taskId: 67890,
  assignId: 11111,
  content: '请及时完成作业' // 必填
});
```

##### 2. 使用便捷方法

```typescript
import { praiseStudents, attentionStudents } from '@/services/homework';

// 表扬学生（便捷方法）
await praiseStudents(
  [123, 456],        // 学生ID列表
  67890,             // 任务ID
  11111,             // 任务布置ID
  '表现优秀！'        // 表扬内容（可选）
);

// 提醒学生（便捷方法）
await attentionStudents(
  [789],             // 学生ID列表
  67890,             // 任务ID
  11111,             // 任务布置ID
  '请及时完成作业'    // 提醒内容（必填）
);
```

#### 对应的 curl 命令

```bash
# 表扬学生
curl -X POST http://localhost:8080/api/v1/behavior/student/handle \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "studentIds": [123, 456],
    "behaviorType": "task_praise",
    "taskId": 67890,
    "assignId": 11111
  }'

# 提醒学生
curl -X POST http://localhost:8080/api/v1/behavior/student/handle \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "studentIds": [789],
    "behaviorType": "task_attention",
    "content": "请及时完成作业",
    "taskId": 67890,
    "assignId": 11111
  }'
```

#### 迁移指南

如果你之前使用的是旧的接口格式，请按以下方式更新：

**旧的调用方式：**
```typescript
// 旧的表扬接口
await praiseStudents({
  classroomID: 123,
  studentIDs: [456, 789],
  teacherID: 101,
  teacherName: "张老师",
  praiseMessage: "表现优秀！"
});

// 旧的提醒接口
await attentionStudents({
  classroomID: 123,
  studentIDs: [456],
  teacherID: 101,
  attentionMessage: "请及时完成作业"
});
```

**新的调用方式：**
```typescript
// 新的表扬接口
await praiseStudents(
  [456, 789],        // studentIds
  67890,             // taskId
  11111,             // assignId
  "表现优秀！"        // content (可选)
);

// 新的提醒接口
await attentionStudents(
  [456],             // studentIds
  67890,             // taskId
  11111,             // assignId
  "请及时完成作业"    // content (必填)
);
```

#### 主要变更

1. **接口统一**：表扬和提醒合并为一个接口 `/behavior/student/handle`
2. **参数简化**：移除了 `classroomID`、`teacherID`、`teacherName` 等参数
3. **增加任务关联**：新增 `taskId` 和 `assignId` 参数，明确关联到具体任务
4. **参数重命名**：`studentIDs` → `studentIds`，`praiseMessage`/`attentionMessage` → `content`
5. **行为类型**：通过 `behaviorType` 字段区分表扬和提醒行为 