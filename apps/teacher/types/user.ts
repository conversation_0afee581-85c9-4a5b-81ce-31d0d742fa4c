export interface UserInfo {
  /**
   * 当前登录的学校ID
   */
  currentSchoolID: number;
  schoolInfos: SchoolInfo[];
  /**
   * 教师当前登录的学校的任职信息、学科、班级
   */
  teacherJobInfos: TeacherJobInfo[];
  /**
   * 教师ID
   */
  userID: number;
  /**
   * 教师姓名
   */
  userName: string;
  /**
   * 教师手机号
   */
  userPhone: string;
  /**
   * 教师头像
   */
  avatarUrl: string;
}

export interface SchoolInfo {
  /**
   * 学校备注
   */
  remark: string;
  /**
   * 学校地址
   */
  schoolAddress: string;
  /**
   * 学段：1 小学，2 初中，3 高中
   */
  schoolEduLevel: number;
  /**
   * 学制
   */
  schoolEduSystem: number;
  /**
   * 办学特色：1 初中+高中，2 小学+初中，3 小学+初中+高中
   */
  schoolFeature: number;
  /**
   * 学校ID
   */
  schoolID: number;
  /**
   * 是否测试学校
   */
  schoolIsTest: number;
  /**
   * 学校名称
   */
  schoolName: string;
  /**
   * 办学性质：1 私立，2 公立，3 其它
   */
  schoolNature: number;
  /**
   * 学校编号，例如 SCH44
   */
  schoolNumber: string;
  /**
   * 学校地区ID，省市区
   */
  schoolRegionID: number;
  /**
   * 学校状态
   */
  schoolStatus: number;
  /**
   * 学校标签
   */
  schoolTag: string;
  /**
   * 教师在职状态
   */
  teacherEmploymentStatus: number;
  /**
   * 是否测试用户
   */
  userIsTest: number;
}

export interface TeacherJobInfo {
  jobInfos?: JobInfo[];
  jobSubject?: JobSubject;
  /**
   * 职务信息
   */
  jobType?: JobType;
}

export interface JobInfo {
  /**
   * 职务下的年级和班级
   */
  jobClass?: JobClass[];
  /**
   * 年级枚举值：1 ~ 14
   */
  jobGrade?: number;
  /**
   * 年级名称：一年级 ~ 六年级，初一 ~ 初四，高一 ~ 高三复读
   */
  name?: string;
}

export interface JobClass {
  /**
   * 班级ID
   */
  jobClass: number;
  /**
   * 班级名称
   */
  name: string;
}

export interface TargetJobClass {
  jobClass: number;
  jobGrade?: number;
  jobGradeName?: string;
  name: string;
  studentList?: any[];
}

export interface JobSubject {
  /**
   * 学科枚举值：1 ~ 9
   */
  jobSubject: number;
  /**
   * 学科名称：语文，数学，英语，物理，化学，生物，历史，地理，道德与法治
   */
  name: string;
}

/**
 * 职务信息
 */
export interface JobType {
  /**
   * 职务枚举值：1 ~ 5
   */
  jobType: number;
  /**
   * 职务名称：1 校长，2 年级主任，3 学科组长，4 学科教师，5 班主任
   */
  name: string;
}
