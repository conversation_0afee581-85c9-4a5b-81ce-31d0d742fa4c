"use client";

import { useTaskContext } from "../../../_context/task-context";
import { useUmeng } from "@/hooks/useUmeng";
import { UmengCategory } from "@/utils";  
import NormalEmpty from "@/components/common/normal-empty";

export default function Results() {
  const { viewMode } = useTaskContext();
  useUmeng(UmengCategory.HOMEWORK, viewMode.value === "student" ? "homework_list_report_student_detail_ask_tab" : "homework_list_report_ask_tab");
  return (
    <div className="p-4">
      {/* TODO: 实现答题结果内容 */}
      <div className="text-center text-gray-500 py-8">答题结果内容将在这里显示</div>

      <NormalEmpty className="h-88.25" />
    </div>
  );
}
