"use client";

import { But<PERSON> } from "@/ui/button";
import { cn } from "@/utils/utils";
import Image from "next/image";
import { useCallback, useState } from "react";

interface GifButtonProps extends React.ComponentProps<typeof Button> {
  src: string;
  activeSrc: string;
  duration: number;
  alt?: string;
  imgWidth?: number;
  imgHeight?: number;
  imgClassName?: string;
  activeImgClassName?: string;
}

export function GifButton ({ onClick, children, alt, className, src, activeSrc, duration, imgWidth = 16, imgHeight = 16 , imgClassName, activeImgClassName, ...props}: GifButtonProps) {
  const [isPlaying, setIsPlaying] = useState(false);

  const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
    if(isPlaying) return;

    if(onClick instanceof Function) {
      await onClick(event);
    }

    setIsPlaying(true);

    setTimeout(() => {
      setIsPlaying(false);
    }, duration);
  }

  return (
    <Button
      onClick={handleClick}
      className={cn(
        "rounded-[1.125rem] flex items-center gap-0.375rem justify-center h-2.25rem padding-0rem-1rem border border-line-1 text-sm text-gray-2 text-0.875rem font-[500] leading-[150%]", 
        className,
        isPlaying && "pointer-events-none"
      )}
      {...props}
    >
      <div className="relative select-none">
      <Image
        src={src}
        alt={alt || typeof children === "string" ? children as string : ""}
        className={cn("h-5 w-5 transform", imgClassName, 
          isPlaying && "opacity-0"
        )}
        width={imgWidth}
        height={imgHeight}
      />
      <Image
        src={isPlaying ? activeSrc : src}
        alt={alt || typeof children === "string" ? children as string : ""}
        className={cn("h-5 w-5 transform absolute top-0 left-0", activeImgClassName, 
          !isPlaying && "hidden"
        )}
        width={imgWidth}
        height={imgHeight}
      />
      
      </div>
      {children}
    </Button>
  );
}
