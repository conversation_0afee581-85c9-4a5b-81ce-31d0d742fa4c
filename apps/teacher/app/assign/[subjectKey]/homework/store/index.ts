"use client";

import { QaContentType } from "@repo/core/views/tch-question-view";
import { AssignResourceTypeEnum } from "@/configs/assign";
import {
  fetchKnowledgeTreeDetail,
  fetchBizTreeDetail,
} from "@/services/assign";
import {
  BizTreeDetailData,
  BizTreeDetailNode,
  BizTreeListItem,
  QuestionFiltersType,
} from "@/types/assign";
import {
  defaultQuestionFilters,
  getDefaultBizTreeId,
  getTreeDefaultNodeById,
} from "@/utils";
import {
  computed,
  effect,
  ReadonlySignal,
  Signal,
  signal,
} from "@preact-signals/safe-react";
import { createContext } from "react";

export interface AssignHomeworkInfoType {
  resourceType: string;
  resourcePublicCategory: string;
  resourcePrivateCategory: string;
  questionFilters: QuestionFiltersType;
  treeId: number | undefined;
  bizTreeNodeId: number | undefined;
  bizTreeNode: BizTreeDetailNode | undefined;
  knowledgeTreeId: number | undefined;
  knowledgeTreeNodeId: number | undefined;
  knowledgeTreeNode: BizTreeDetailNode | undefined;
  treeSearchKeyword: string;
}

export interface AssignHomeworkContextType {
  historySelectionList: Signal<QaContentType[]>;
  selectionResourceList: Signal<QaContentType[]>;
  selectedResourceListLength: ReadonlySignal<number>;
  resourceType: Signal<string>;
  resourcePublicCategory: Signal<string>;
  resourcePrivateCategory: Signal<string>;
  questionFilters: Signal<QuestionFiltersType>;
  bizTreeList: Signal<BizTreeListItem[]>;
  treeId: Signal<number | undefined>;
  bizTreeDetail: Signal<BizTreeDetailData | undefined>;
  bizTreeNodeMap: Signal<Map<number, BizTreeDetailNode>>;
  bizTreeNodeIndexMap: Signal<Map<number, BizTreeDetailNode>>;
  knowledgeTreeList: Signal<BizTreeListItem[]>;
  knowledgeTreeId: Signal<number | undefined>;
  knowledgeTreeDetail: Signal<BizTreeDetailData | undefined>;
  knowledgeTreeNodeMap: Signal<Map<number, BizTreeDetailNode>>;
  knowledgeTreeNodeIndexMap: Signal<Map<number, BizTreeDetailNode>>;
  bizTreeNodeId: Signal<number | undefined>;
  bizTreeNode: Signal<BizTreeDetailNode | undefined>;
  knowledgeTreeNodeId: Signal<number | undefined>;
  knowledgeTreeNode: Signal<BizTreeDetailNode | undefined>;
  treeSearchKeyword: Signal<string>;
  homeworkTreeInfo: Signal<AssignHomeworkInfoType>;
  deleteSelectionResourceByIds: (
    resourceType: AssignResourceTypeEnum,
    resourceIds: string[]
  ) => void;
  deleteSelectionQuestionByIds: (questionIds: string[]) => void;
}

export function createAssignHomeworkState(): AssignHomeworkContextType {
  const historySelectionList = signal<QaContentType[]>([]);
  const selectionResourceList = signal<QaContentType[]>([]);

  // 业务树列表
  const bizTreeList = signal<BizTreeListItem[]>([]);
  // 业务树id
  const treeId = signal<number | undefined>(undefined);
  // 业务树详情
  const bizTreeDetail = signal<BizTreeDetailData | undefined>(undefined);
  // 业务树节点map
  const bizTreeNodeMap = signal<Map<number, BizTreeDetailNode>>(new Map());
  // 业务树节点index map
  const bizTreeNodeIndexMap = signal<Map<number, BizTreeDetailNode>>(new Map());
  // 业务树节点id
  const bizTreeNodeId = signal<number | undefined>(undefined);
  // 业务树节点
  const bizTreeNode = signal<BizTreeDetailNode | undefined>(undefined);

  // 知识树列表
  const knowledgeTreeList = signal<BizTreeListItem[]>([]);
  // 知识树id
  const knowledgeTreeId = signal<number | undefined>(undefined);
  // 知识树详情
  const knowledgeTreeDetail = signal<BizTreeDetailData | undefined>(undefined);
  // 知识树节点map
  const knowledgeTreeNodeMap = signal<Map<number, BizTreeDetailNode>>(
    new Map()
  );
  // 知识树节点index map
  const knowledgeTreeNodeIndexMap = signal<Map<number, BizTreeDetailNode>>(
    new Map()
  );
  // 业务树节点id
  const knowledgeTreeNodeId = signal<number | undefined>(undefined);
  // 知识树节点
  const knowledgeTreeNode = signal<BizTreeDetailNode | undefined>(undefined);

  // 节点树搜索关键字
  const treeSearchKeyword = signal<string>("");

  const resourceType = signal<string>("public");
  const resourcePublicCategory = signal<string>("chapter");
  const resourcePrivateCategory = signal<string>("chapter");
  const selectedResourceListLength = computed(() => {
    return selectionResourceList.value.length;
  });

  const questionFilters = signal<QuestionFiltersType>(defaultQuestionFilters());

  const homeworkTreeInfo = computed<AssignHomeworkInfoType>(() => {
    const bizNode = bizTreeNode.value?.bizTreeNodeChildren?.length ? {...bizTreeNode.value, bizTreeNodeChildren: []} : bizTreeNode.value;
    const knowledgeNode = knowledgeTreeNode.value?.bizTreeNodeChildren?.length ? {...knowledgeTreeNode.value, bizTreeNodeChildren: []} : knowledgeTreeNode.value;
    return {
      resourceType: resourceType.value,
      resourcePublicCategory: resourcePublicCategory.value,
      resourcePrivateCategory: resourcePrivateCategory.value,
      questionFilters: questionFilters.value,
      treeId: treeId.value,
      bizTreeNodeId: bizTreeNodeId.value,
      bizTreeNode: bizNode,
      knowledgeTreeId: knowledgeTreeId.value,
      knowledgeTreeNodeId: knowledgeTreeNodeId.value,
      knowledgeTreeNode: knowledgeNode,
      treeSearchKeyword: treeSearchKeyword.value,
    };
  });

  // 业务树id改变时， 获取业务树详情
  effect(() => {
    if (treeId.value) {
      const treeItem = bizTreeList.value.find(
        (item) => item.bizTreeId === treeId.value
      );
      if (treeItem && treeItem.treeFormatInfo) {
        const { treeData, treeNodeMap, treeNodeIndexMap } =
          treeItem.treeFormatInfo;
        const node = getTreeDefaultNodeById(treeItem.treeFormatInfo, bizTreeNodeId.value);
        bizTreeDetail.value = treeData;
        bizTreeNodeId.value = node?.bizTreeNodeId;
        bizTreeNodeMap.value = treeNodeMap;
        bizTreeNodeIndexMap.value = treeNodeIndexMap;
        bizTreeNode.value = node;
        return;
      }
      fetchBizTreeDetail(treeId.value).then((res) => {
        const { treeData, treeNodeMap, treeNodeIndexMap } = res;
        const node = getTreeDefaultNodeById(res, bizTreeNodeId.value);
        bizTreeDetail.value = treeData;
        bizTreeNodeId.value = node?.bizTreeNodeId;
        bizTreeNodeMap.value = treeNodeMap;
        bizTreeNodeIndexMap.value = treeNodeIndexMap;
        bizTreeNode.value = node;
        if (treeItem) {
          treeItem.treeFormatInfo = { treeData, treeNodeMap, treeNodeIndexMap };
          bizTreeList.value = [...bizTreeList.value];
        }
      });
      return;
    }
    bizTreeDetail.value = undefined;
    bizTreeNodeMap.value = new Map();
    bizTreeNodeIndexMap.value = new Map();
  });

  // 知识树id改变时， 获取知识树详情
  effect(() => {
    if (knowledgeTreeId.value) {
      const treeItem = knowledgeTreeList.value.find(
        (item) => item.bizTreeId === knowledgeTreeId.value
      );
      if (treeItem && treeItem.treeFormatInfo) {
        const { treeData, treeNodeMap, treeNodeIndexMap } =
          treeItem.treeFormatInfo;
        const node = getTreeDefaultNodeById(treeItem.treeFormatInfo, knowledgeTreeNodeId.value);
        knowledgeTreeDetail.value = treeData;
        knowledgeTreeNodeId.value = node?.bizTreeNodeId;
        knowledgeTreeNodeMap.value = treeNodeMap;
        knowledgeTreeNodeIndexMap.value = treeNodeIndexMap;
        knowledgeTreeNode.value = node;
        return;
      }
      fetchKnowledgeTreeDetail(knowledgeTreeId.value).then((res) => {
        const { treeData, treeNodeMap, treeNodeIndexMap } = res;
        const node = getTreeDefaultNodeById(res, knowledgeTreeNodeId.value);
        knowledgeTreeDetail.value = treeData;
        knowledgeTreeNodeId.value = node?.bizTreeNodeId;
        knowledgeTreeNodeMap.value = treeNodeMap;
        knowledgeTreeNodeIndexMap.value = treeNodeIndexMap;
        knowledgeTreeNode.value = node;
        if (treeItem) {
          treeItem.treeFormatInfo = { treeData, treeNodeMap, treeNodeIndexMap };
          knowledgeTreeList.value = [...knowledgeTreeList.value];
        }
      });
      return;
    }
    knowledgeTreeDetail.value = undefined;
    knowledgeTreeNodeMap.value = new Map();
    knowledgeTreeNodeIndexMap.value = new Map();
  });

  // 业务树列表改变时， 处理默认选中的业务树id
  effect(() => {
    treeId.value = getDefaultBizTreeId(bizTreeList.value, treeId.peek());
  });

  // 知识树列表改变时， 处理默认选中的知识树id
  effect(() => {
    knowledgeTreeId.value = getDefaultBizTreeId(
      knowledgeTreeList.value,
      knowledgeTreeId.peek()
    );
  });

  /**
   * 根据资源类型和资源id删除资源
   * @param resourceType 资源类型
   * @param resourceIds 资源id
   */
  function deleteSelectionResourceByIds(
    resourceType: AssignResourceTypeEnum,
    resourceIds: string[]
  ) {
    switch (resourceType) {
      case AssignResourceTypeEnum.RESOURCE_QUESTION:
        selectionResourceList.value = selectionResourceList.value.filter(
          (item) =>
            !(
              item.resourceType === AssignResourceTypeEnum.RESOURCE_QUESTION &&
              resourceIds.includes(item.questionId)
            )
        );
        break;
      default:
        break;
    }
  }

  function deleteSelectionQuestionByIds(questionIds: string[]) {
    deleteSelectionResourceByIds(
      AssignResourceTypeEnum.RESOURCE_QUESTION,
      questionIds
    );
  }

  return {
    historySelectionList,
    selectionResourceList,
    selectedResourceListLength,
    resourceType,
    resourcePublicCategory,
    resourcePrivateCategory,
    questionFilters,
    bizTreeList,
    treeId,
    bizTreeDetail,
    bizTreeNodeMap,
    bizTreeNodeIndexMap,
    knowledgeTreeList,
    knowledgeTreeId,
    knowledgeTreeDetail,
    knowledgeTreeNodeMap,
    knowledgeTreeNodeIndexMap,
    bizTreeNodeId,
    bizTreeNode,
    knowledgeTreeNodeId,
    knowledgeTreeNode,
    treeSearchKeyword,
    deleteSelectionResourceByIds,
    deleteSelectionQuestionByIds,
    homeworkTreeInfo,
  };
}

export const AssignHomeworkContext = createContext<AssignHomeworkContextType>(
  {} as AssignHomeworkContextType
);

export const AssignHomeworkProvider =
  AssignHomeworkContext.Provider as React.Provider<AssignHomeworkContextType>;
