"use client";
import { AssignHeader } from "@/components/assign/assign-header";
import { AssignPageContainer } from "@/components/assign/assign-page-container";
import { useContext, useEffect, useCallback, useMemo } from "react";

import { AssignSubjectContext } from "../store";
import { useComputed, useSignal, useSignalEffect } from "@preact-signals/safe-react";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import StepSelectTarget from "./components/step-select-target";
import { HomeworkFooter } from "./components/homework-footer";
import StepSelectResource from "./components/step-select-resource";
import { StepSetTime } from "./components/setp-set-time";
import { AssignCancelAlert } from "@/components/assign/assign-cancel-alert";
import { AssignHomeworkContext } from "./store";
import { useUmeng } from "@/hooks/useUmeng";
import {
  umeng,
  UmengAssignAction,
  UmengAssignPageName,
  UmengCategory,
} from "@/utils";
import { useSearchParams } from "next/navigation";
import to from "await-to-js";
import { getAssignTaskDetail } from "@/services/assign";
import { useTaskExtraInfo } from "./hooks";
import { useApp } from "@/hooks";

export default function AssignPage() {
  const { recoverContext } = useTaskExtraInfo();
  useUmeng(UmengCategory.ASSIGN, UmengAssignPageName.TASK_LIST_HOMEWORK_SETUP);
  const searchParams = useSearchParams();
  const { statusBarHeight } = useApp();
  const taskId = +(searchParams.get("taskId") ?? 0);
  const { gotoBack } = useTchNavigation();
  const cancelAssignOpen = useSignal(false);
  // const containerRef = useRef<HTMLDivElement>(null);
  const { currentAssignStep, checkedClasses, setTimeStepOrigin } = useContext(AssignSubjectContext);
  const { selectedResourceListLength } = useContext(AssignHomeworkContext);
  const assignHeaderTitle = useComputed(() => {
    return currentAssignStep.value === "select-resource"
      ? "资源中心选题"
      : "布置作业任务";
  });

  const onBack = () => {
    if (currentAssignStep.value === "set-time") {
      currentAssignStep.value = setTimeStepOrigin.value || "select-target";
      return;
    }
    if (currentAssignStep.value !== "select-target") {
      currentAssignStep.value = "select-target";
      return;
    }
    if (selectedResourceListLength.value > 0) {
      cancelAssignOpen.value = true;
    } else {
      gotoBack();
    }
  };

  const fetchTaskDetail = useCallback(async (taskId: number) => {
    const [err, res] = await to(getAssignTaskDetail(taskId));
    if (err) {
      console.log(err);
      return null;
    }
    console.log(res);
    return res;
  }, []);

  useEffect(() => {
    if (taskId) {
      // 获取任务详情
      fetchTaskDetail(taskId).then((res) => {
        if (res && res.taskExtraInfo) {
          recoverContext(res);
        }
      });
    }
  }, [taskId, recoverContext, fetchTaskDetail]);

  useSignalEffect(() => {
    if (checkedClasses.value.length > 0) {
      umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_CLASS_CLICK, {});
    }
  });
  useSignalEffect(() => {
    if (currentAssignStep.value === "set-time") {
      umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_TIME_DONE, {});
    }
  });

  const outerHeight = useMemo(() => {
    // 顶和底 fixed占用的空间
    const num = 19 + 16;
    return `calc(100% - ${statusBarHeight}px - ${num} * var(--spacing))`;
  }, [statusBarHeight]);

  return (
    <AssignPageContainer>
      <div className="fixed flex h-16 items-center px-6">
        <AssignHeader title={assignHeaderTitle.value} onBack={onBack} />
      </div>
      <div
        className="mb-19 mt-16 h-full overflow-hidden"
        style={{ height: outerHeight }}
      >
        {currentAssignStep.value === "select-target" && <StepSelectTarget />}
        {currentAssignStep.value === "select-resource" && (
          <StepSelectResource />
        )}
        {currentAssignStep.value === "set-time" && <StepSetTime />}
      </div>
      <div className="fixed bottom-0 left-0 right-0">
        <HomeworkFooter />
      </div>
      <AssignCancelAlert
        open={cancelAssignOpen.value}
        onCancel={() => {
          cancelAssignOpen.value = false;
        }}
        onOk={() => {
          cancelAssignOpen.value = false;
          gotoBack();
        }}
      />
    </AssignPageContainer>
  );
}
