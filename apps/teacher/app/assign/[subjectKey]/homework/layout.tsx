"use client";
import {
  AssignHomeworkContext,
  AssignHomeworkContextType,
  AssignHomeworkProvider,
  createAssignHomeworkState,
} from "./store";
import { useEffect, useContext, useState } from "react";
import { fetchTreeList } from "@/services/assign";
import { useParams } from "next/navigation";
import { convertQuestion, fetchHistorySelectionList } from "@/utils";
import { AssignSubjectContext } from "../store";
import { useSignalEffect } from "@preact-signals/safe-react";
import { useApp } from "@/hooks";
import { useTaskExtraInfo } from "./hooks";

function HomeworkWrapper({ children }: { children: React.ReactNode }) {
  const { userInfo } = useApp();
  const { historySelectionList, bizTreeList, knowledgeTreeList, homeworkTreeInfo } = useContext(AssignHomeworkContext);
  const { questionFilterEnumMap } = useContext(AssignSubjectContext);
  const { subjectKey } = useParams();
  const { recoverTreeInfoFromLocalStorage } = useTaskExtraInfo();
  const currentSubjectKey = Number(subjectKey);

  const localStorageKey = `assign-homework-${userInfo?.userID}-${currentSubjectKey}`;

  recoverTreeInfoFromLocalStorage(localStorageKey);

  useSignalEffect(() => {
    localStorage.setItem(localStorageKey, JSON.stringify(homeworkTreeInfo.value));
  });


  useEffect(() => {
    fetchHistorySelectionList(currentSubjectKey).then(res => {
      historySelectionList.value = res.map(item => {
        return convertQuestion(item, questionFilterEnumMap.value)
      });
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    fetchTreeList(currentSubjectKey, "bizTree").then((res) => {
      bizTreeList.value = res;
    });
    fetchTreeList(currentSubjectKey, "knowledgeTree").then((res) => {
      knowledgeTreeList.value = res;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return <>{children}</>;
}

export default function AssignHomeworkLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [state, setState] = useState<AssignHomeworkContextType>(
    null as unknown as AssignHomeworkContextType
  );
  useEffect(() => {
    setState(createAssignHomeworkState());
  }, []);
  if (!state) {
    return null;
  }
  return (
    <AssignHomeworkProvider value={state}>
      <HomeworkWrapper>{children}</HomeworkWrapper>
    </AssignHomeworkProvider>
  );
}
