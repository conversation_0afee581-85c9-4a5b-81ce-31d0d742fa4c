import { useCallback, useContext } from "react";
import { AssignHomeworkContext } from "../store";
import { batch } from "@preact-signals/safe-react";
import { AssignCourseResourceItem, AssignTaskDetailData } from "@/types/assign/course";
import { convertQuestion, defaultQuestionFilters, fetchQuestionDetails } from "@/utils";
import { AssignSubjectContext } from "../../store";

export interface TaskExtraInfo {
  getTaskExtraInfo: () => string;
  recoverContext: (taskData: AssignTaskDetailData) => void;
  recoverTreeInfoFromLocalStorage: (key: string) => void;
}

export function useTaskExtraInfo(): TaskExtraInfo {
  const {
    resourceType,
    resourcePublicCategory,
    resourcePrivateCategory,
    questionFilters,
    treeId,
    bizTreeNodeId,
    bizTreeNode,
    knowledgeTreeId,
    knowledgeTreeNodeId,
    knowledgeTreeNode,
    treeSearchKeyword,
    selectionResourceList,
    homeworkTreeInfo,
  } = useContext(AssignHomeworkContext);
  const { questionFilterEnumMap, assignTimeRanges } = useContext(AssignSubjectContext);

  const getTaskExtraInfo = useCallback(() => {
    const info = {
      ...homeworkTreeInfo.value,
      treeSearchKeyword: treeSearchKeyword.value,
      assignTimeRanges: assignTimeRanges.value,
    };
    console.log('保存的任务信息 ===> ', info);
    return JSON.stringify(info);
  }, [homeworkTreeInfo, treeSearchKeyword, assignTimeRanges]);

  const recoverHomeworkTreeInfo = useCallback((taskExtraInfo: string) => {
    if (!taskExtraInfo) {
      return;
    }
    const info = JSON.parse(taskExtraInfo);
    batch(() => {
        resourceType.value = info.resourceType ?? "public";
        resourcePublicCategory.value = info.resourcePublicCategory ?? "chapter";
        resourcePrivateCategory.value = info.resourcePrivateCategory ?? "chapter";
        questionFilters.value = info.questionFilters ?? defaultQuestionFilters();
        treeId.value = info.treeId;
        bizTreeNodeId.value = +info.bizTreeNodeId;
        bizTreeNode.value = info.bizTreeNode;   
        knowledgeTreeId.value = info.knowledgeTreeId;
        knowledgeTreeNodeId.value = +info.knowledgeTreeNodeId;
        knowledgeTreeNode.value = info.knowledgeTreeNode;
        treeSearchKeyword.value = info.treeSearchKeyword ?? "";
    });
  }, [resourceType, resourcePublicCategory, resourcePrivateCategory, questionFilters, treeId, bizTreeNodeId, bizTreeNode, knowledgeTreeId, knowledgeTreeNodeId, knowledgeTreeNode, treeSearchKeyword]);

  const recoverSelectionResourceList = useCallback(async (resources: AssignCourseResourceItem[]) => {
    if (!resources) {
      selectionResourceList.value = [];
      return;
    }
    const res = await fetchQuestionDetails(resources);
    selectionResourceList.value = res.map((item) => convertQuestion(item, questionFilterEnumMap.value));
    console.log('复原选择的问题: ', selectionResourceList.value);
  }, [questionFilterEnumMap, selectionResourceList]);

  const recoverContext = useCallback((taskData: AssignTaskDetailData) => {
    const { resources, taskExtraInfo } = taskData;
    recoverSelectionResourceList(resources);
    recoverHomeworkTreeInfo(taskExtraInfo);
  }, [recoverSelectionResourceList, recoverHomeworkTreeInfo]);

  const recoverTreeInfoFromLocalStorage = useCallback((key: string) => {
    const taskExtraInfo = localStorage.getItem(key);
    if (!taskExtraInfo) {
      return;
    }
    recoverHomeworkTreeInfo(taskExtraInfo);
  }, [recoverHomeworkTreeInfo]);


  return {
    getTaskExtraInfo,
    recoverContext,
    recoverTreeInfoFromLocalStorage,
  };
}
