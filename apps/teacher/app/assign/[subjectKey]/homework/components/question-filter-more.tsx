import { cn } from "@/utils/utils";
import { useContext } from "react";
import { AssignHomeworkContext } from "../store";
import { QuestionFiltersType } from "@/types/assign";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import filterIcon from "@/public/assign/homework/filter.svg";
import Image from "next/image";
import { TchDrawer } from "@/ui/tch-drawer";
import { AssignCard } from "@/components/assign/assign-card";
import { Button } from "@/ui/tch-button";
import { TchFilterItem } from "@/components/common/tch-filter-item";
import { filterEnumFieldNames } from "@/configs";
import { QuestionFilterSource } from "@/enums";
import { AssignSubjectContext } from "../../store";

interface QuestionFilterMoreProps {
  style?: React.CSSProperties;
  className?: string;
}

export function QuestionFilterMore({
  style = {},
  className = "",
}: QuestionFilterMoreProps) {
  const { questionFilters } = useContext(AssignHomeworkContext);
  const { questionFilterEnum } = useContext(AssignSubjectContext);
  const tempQuestionFilters = useSignal(getInitFilters());
  const open = useSignal(false);

  const yearList = useComputed(() => {
    return [
      { nameZh: "全部", value: "all" },
      ...questionFilterEnum.value.yearList,
    ];
  });

  const provinceList = useComputed(() => {
    return [
      { nameZh: "全部", value: "all" },
      ...questionFilterEnum.value.provinceList,
    ];
  });

  const yearValue = useComputed(() => {
    return tempQuestionFilters.value.yearList.join(",") || "all";
  });

  const provinceValue = useComputed(() => {
    return tempQuestionFilters.value.provinceList.join(",") || "all";
  });

  function getInitFilters() {
    return {
      yearList: [...questionFilters.value.yearList],
      sourceList: [...questionFilters.value.sourceList],
      provinceList: [...questionFilters.value.provinceList],
    };
  }

  const onValueChange = (
    value: string | string[],
    key: keyof QuestionFiltersType
  ) => {
    const filters = tempQuestionFilters.value;
    if (key === "yearList") {
      filters.yearList = value === "all" ? [] : [Number(value)];
    } else if (key === "sourceList") {
      filters.sourceList =
        value === "all" ? [] : [value as QuestionFilterSource];
    } else if (key === "provinceList") {
      filters.provinceList = value === "all" ? [] : [Number(value)];
    }

    tempQuestionFilters.value = { ...filters };
  };

  const onReset = () => {
    tempQuestionFilters.value = getInitFilters();
  };

  const onOk = () => {
    questionFilters.value = {
      ...questionFilters.value,
      ...tempQuestionFilters.value,
    };
    open.value = false;
  };

  return (
    <>
      <div
        className={cn("flex cursor-pointer items-center gap-1", className)}
        style={style}
        onClick={() => (open.value = true)}
      >
        <Image
          src={filterIcon}
          alt="filter"
          width={14}
          height={14}
          className="size-3.5"
        />
        <span className="text-sm font-normal leading-tight">更多筛选</span>
      </div>
      <TchDrawer
        open={open.value}
        onOpenChange={(v) => (open.value = v)}
        title="筛选"
      >
        <AssignCard className="flex-1 overflow-y-auto outline-none">
          <div className="flex flex-col gap-3">
            <TchFilterItem
              name="年份"
              options={yearList.value}
              onChange={(value) => onValueChange(value, "yearList")}
              fieldNames={filterEnumFieldNames}
              value={yearValue.value}
            />
            {/* <TchFilterItem
              name="来源"
              options={sourceList.value}
              onChange={(value) => onValueChange(value, "sourceList")}
              fieldNames={filterEnumFieldNames}
              value={sourceValue.value}
            /> */}
            <TchFilterItem
              name="地区"
              options={provinceList.value}
              onChange={(value) => onValueChange(value, "provinceList")}
              fieldNames={filterEnumFieldNames}
              type="select"
              value={provinceValue.value}
              contentClassName="w-50"
            />
          </div>
        </AssignCard>
        <div className="absolute bottom-0 left-0 right-0 flex h-16 flex-row items-center justify-between gap-3 bg-white px-6 py-4">
          <Button
            type="default"
            size="lg"
            radius="full"
            className="flex-1"
            onClick={() => (open.value = false)}
          >
            取消
          </Button>
          <Button
            type="outline"
            size="lg"
            radius="full"
            className="flex-1"
            onClick={onReset}
          >
            重置
          </Button>
          <Button
            type="primary"
            size="lg"
            radius="full"
            className="flex-1"
            onClick={onOk}
          >
            确定
          </Button>
        </div>
      </TchDrawer>
    </>
  );
}
