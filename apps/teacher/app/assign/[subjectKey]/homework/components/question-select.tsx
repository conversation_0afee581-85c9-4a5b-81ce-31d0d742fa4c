import { getQuestionList } from "@/services/assign-homework";
import { cn } from "@/utils/utils";
import to from "await-to-js";
import { useContext } from "react";
import { AssignHomeworkContext } from "../store";
import {
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { AssignContext } from "@/app/assign/store";
import {
  convertQuestion,
  createTempSelection,
  removeTempSelection,
  umeng,
  UmengAssignAction,
  UmengCategory,
} from "@/utils";
import { QuestionList } from "./question-list";
import { toast } from "@/ui/toast";
import { AssignSubjectContext } from "../../store";
import { QaContentType } from "@repo/core/views/tch-question-view";

interface QuestionSelectProps {
  style?: React.CSSProperties;
  className?: string;
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
}

export function QuestionSelect({
  style = {},
  className = "",
  onScroll,
}: QuestionSelectProps) {
  const {
    bizTreeNodeId,
    knowledgeTreeNodeId,
    questionFilters,
    selectionResourceList,
    deleteSelectionQuestionByIds,
    historySelectionList,
    resourcePublicCategory
  } = useContext(AssignHomeworkContext);
  const { questionFilterEnumMap } = useContext(AssignSubjectContext);
  const { currentSubject } = useContext(AssignContext);
  const page = useSignal(1);
  const questionList = useSignal<QaContentType[]>([]);
  const hasMore = useSignal(false);
  const fileters = useComputed(() => {
    const {
      provinceList,
      questionDifficultList,
      questionTypeList,
      sort,
      sourceList,
      yearList,
    } = questionFilters.value;
    return {
      questionProvince: provinceList,
      questionDifficult: questionDifficultList,
      questionType: questionTypeList,
      questionYears: yearList,
      sort,
      resourceType: sourceList,
    };
  });
  const nodeIds = useComputed(() => {
    const bizTreeNodeIds = bizTreeNodeId.value ? [bizTreeNodeId.value] : [];
    const knowledgeTreeNodeIds = knowledgeTreeNodeId.value
      ? [knowledgeTreeNodeId.value]
      : [];
    if (resourcePublicCategory.value === "chapter") {
      return {
        bizTreeNodeIds,
      };
    }
    return {
      bizTreeNodeIds: knowledgeTreeNodeIds,
    };
  });
  const payload = useComputed(() => {
    return {
      subject: currentSubject.value.subjectKey,
      ...nodeIds.value,
      ...fileters.value,
      page: page.value,
      pageSize: 20,
    };
  });

  async function fetchQuestionList() {
    const [err, res] = await to(getQuestionList(payload.value));
    if (err) {
      console.error(err);
      questionList.value = [];
      return;
    }
    const list = Array.isArray(res.list) ? res.list : [];
    const convertedList = list.map((item) => {
      return convertQuestion(item, questionFilterEnumMap.value);
    });
    questionList.value =
      page.value === 1
        ? convertedList
        : [...questionList.value, ...convertedList];
    hasMore.value = res.page < (res.total / 20);
    console.log("res***: ", res);
  }

  // useEffect(() => {
  //   if (isFirstRender.current) {
  //     isFirstRender.current = false;
  //     fetchQuestionList();
  //     return;
  //   }

  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [payload]);

  useSignalEffect(() => {
    fetchQuestionList();
  });

  const onQuestionAdd = (qaContent: QaContentType) => {
    createTempSelection(qaContent.questionId, currentSubject.value.subjectKey).then((res) => {
      if (!res) {
        deleteSelectionQuestionByIds([qaContent.questionId]);
        toast.error("加入失败");
        return;
      }
      toast.success("加入成功");
      umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_RESOURCE_ADD_QUESTION_CLICK, {
        question_selected: qaContent.questionId,
      });
    });
  };

  const onQuestionRemove = (qaContent: QaContentType) => {
    if (
      historySelectionList.value.some(
        (item) => item.questionId === qaContent.questionId
      )
    ) {
      return;
    }
    removeTempSelection([qaContent.questionId], currentSubject.value.subjectKey).then((res) => {
      if (!res) {
        selectionResourceList.value = [
          ...selectionResourceList.value,
          qaContent,
        ];
        toast.error("取消加入失败");
        return;
      }
      toast.success("取消加入成功");
      umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_RESOURCE_DELETE_QUESTION_CLICK, {
        question_delected: qaContent.questionId,
      });
    });
  };

  return (
    <div
      className={cn("space-y-4", className)}
      style={style}
      onScroll={onScroll}
    >
      <QuestionList
        list={questionList.value}
        onAdd={onQuestionAdd}
        onRemove={onQuestionRemove}
      />
    </div>
  );
}
