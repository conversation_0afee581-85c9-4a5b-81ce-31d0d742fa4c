"use client";
import { useContext, useEffect, useRef } from "react";
import BaseTabNav from "@/components/common/tab-nav";

import {
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { AssignHomeworkContext } from "../store";
import {
  HOMEWORK_RESOURCE_TYPES,
  PRIVATE_RESOURCE_CATEGORIES,
  PUBLIC_RESOURCE_CATEGORIES,
} from "@/configs/assign";
import { TchButtonGroup } from "@/components/common/tch-button-group";
import { QuestionFilters } from "./question-filters";
import { TreeNodeSelect } from "./tree-node-select";
// import { InfiniteQuestionList } from "./infinite-question-list";
import { VirtuosoQuestionList } from "./virtuoso-question-list";

export default function StepSelectResource() {
  const collapsedRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const treeScrollTop = useSignal(0);
  const collapsedHeight = useSignal(0);
  const containerHeight = useSignal(0);
  const scrollContainerHeight = useSignal(0);
  let timer: string | number | NodeJS.Timeout | null | undefined = null;

  const { resourceType, resourcePublicCategory, resourcePrivateCategory, treeSearchKeyword } =
    useContext(AssignHomeworkContext);
  const categoryBtns = useComputed(() => {
    return resourceType.value === "public"
      ? PUBLIC_RESOURCE_CATEGORIES
      : PRIVATE_RESOURCE_CATEGORIES;
  });
  const activeCategoryBtn = useComputed(() => {
    return resourceType.value === "public"
      ? resourcePublicCategory.value
      : resourcePrivateCategory.value;
  });

  useEffect(() => {
    if (collapsedRef.current && containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const collapsedRect = collapsedRef.current.getBoundingClientRect();

      containerHeight.value = containerRect.height;
      collapsedHeight.value = collapsedRect.height;
      scrollContainerHeight.value = containerRect.height - collapsedRect.height;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    collapsedRef.current,
    containerRef.current,
    containerHeight,
    collapsedHeight,
  ]);
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      const target = e.target as HTMLDivElement;
      treeScrollTop.value = target.scrollTop;
    }, 100);
  };

  useSignalEffect(() => {
    if (treeScrollTop.value > 0) {
      containerRef.current?.scrollTo({
        top: containerRef.current?.scrollHeight,
        behavior: "smooth",
      });
      return;
    }
    containerRef.current?.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  });

  const onTabChange = (tab: string) => {
    resourceType.value = tab;
    treeSearchKeyword.value = "";
    console.log("tab: ", tab);
  };

  const onCategoryChange = (category: string) => {
    
    treeSearchKeyword.value = "";
    if (resourceType.value === "public") {
      resourcePublicCategory.value = category;
    } else {
      resourcePrivateCategory.value = category;
    }
  };

  return (
    <div
      className="flex h-full flex-1 flex-col overflow-y-auto pl-6 pr-4"
      ref={containerRef}
    >
      <div ref={collapsedRef}>
        <BaseTabNav
          tabs={HOMEWORK_RESOURCE_TYPES}
          activeTab={resourceType.value}
          onTabChange={onTabChange}
        />
        <div className="flex items-center justify-between py-4">
          <TchButtonGroup
            btns={categoryBtns.value}
            activeBtn={activeCategoryBtn.value}
            onActiveChange={onCategoryChange}
          />
          <QuestionFilters />
        </div>
      </div>

      <div className="w-full">
        <div className="flex items-stretch gap-4">
          <TreeNodeSelect
            className="min-w-66.5 w-[28%] overflow-y-auto"
            style={{
              minHeight: scrollContainerHeight.value + "px",
              maxHeight: containerHeight.value + "px",
            }}
            onScroll={handleScroll}
          />
          <VirtuosoQuestionList 
            className="flex-1"
            minHeight={scrollContainerHeight.value}
            maxHeight={containerHeight.value}
            onScroll={handleScroll}
          />
        </div>
      </div>
    </div>
  );
}
