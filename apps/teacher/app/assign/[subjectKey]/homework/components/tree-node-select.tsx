import { AssignCard } from "@/components/assign/assign-card";
import { cn } from "@/utils/utils";
import { TreeListAccordion } from "./tree-list-accordion";
import { useContext, useEffect } from "react";
import { AssignHomeworkContext } from "../store";
// import { AssignSearch } from "@/components/assign/assign-search";
import { useDebounce } from "ahooks";
import { useSignal } from "@preact-signals/safe-react";

interface TreeNodeSelectProps {
  style?: React.CSSProperties;
  className?: string;
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
}

export function TreeNodeSelect({
  style = {},
  className = "",
  onScroll,
}: TreeNodeSelectProps) {
 
  
  const { treeSearchKeyword } = useContext(AssignHomeworkContext);
  const search = useSignal(treeSearchKeyword.value);
  const debouncedSearch = useDebounce(search.value, { maxWait: 300 });
  // const onSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   search.value = e.target.value;
  // };
  useEffect(() => {
    treeSearchKeyword.value = debouncedSearch;
  }, [debouncedSearch, treeSearchKeyword]);

  return (
    <AssignCard
      className={cn("gap-4 px-5 outline-none", className)}
      style={style}
      onScroll={onScroll}
    >
      <div className="flex flex-col gap-4">
        {/* <AssignSearch
          className="h-8 rounded-full"
          placeholder="请输入关键词"
          type="text"
          value={treeSearchKeyword.value}
          onChange={onSearch}
          classNames={{
            search: "size-6 py-1",
            input: "text-sm shadow-none",
            clear: "py-2.5",
          }}
        /> */}
        <div className="">
          <TreeListAccordion />
        </div>
      </div>
    </AssignCard>
  );
}
