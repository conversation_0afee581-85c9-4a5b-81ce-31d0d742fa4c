import { cn } from "@/utils/utils";
import { AddQuestionEntry } from "./add-question-entry";
import { Separator } from "@/ui/separator";
import { AssignHeading } from "@/components/assign/assign-heading";
import { QuestionList } from "./question-list";
import { useContext } from "react";
import { AssignHomeworkContext } from "../store";
import { toast } from "@/ui/toast";
import { umeng, UmengAssignAction, UmengCategory } from "@/utils";
import { QaContentType } from "@repo/core/views/tch-question-view";

interface TaskContentContainerProps {
  style?: React.CSSProperties;
  className?: string;
}

export function TaskContentContainer({
  style = {},
  className = "",
}: TaskContentContainerProps) {
  const { historySelectionList } = useContext(AssignHomeworkContext);
  return (
    <div className={cn("flex flex-col", className)} style={style}>
      <AddQuestionEntry className="h-full" />
      <Separator className="bg-line-3" />
      <div className="space-y-4 py-6">
        <AssignHeading content="资源中心试题篮同步" className="h-6" />
        <QuestionList
          questionClassName="border! border-solid! border-line-1 flex-1 py-5"
          list={historySelectionList.value}
          onAdd={(qaContent: QaContentType) => {
            toast.success("加入成功");
            umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_QUESTION_BASKET_USED_1, {
              question_used: qaContent.questionId,
            });
          }}
          onRemove={(qaContent: QaContentType) => {
            toast.success("取消成功");
            umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_QUESTION_BASKET_USED_2, {
              question_unused: qaContent.questionId,
            });
          }}
        />
      </div>
    </div>
  );
}
