import { cn } from "@/utils/utils";
import plusIcon from "@/public/assign/homework/plus.svg";
import disablePlusIcon from "@/public/assign/homework/disable_plus.svg";
import Image from "next/image";
import { Button } from "@/ui/tch-button";
import { AssignSubjectContext } from "../../store";
import { useContext } from "react";
import { umeng, UmengAssignAction, UmengCategory } from "@/utils";

interface AddQuestionEntryProps {
  style?: React.CSSProperties;
  className?: string;
}

export function AddQuestionEntry({
  style = {},
  className = "",
}: AddQuestionEntryProps) {
  const { currentAssignStep, checkedClasses } = useContext(AssignSubjectContext);
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center gap-4 px-8 py-10",
        className
      )}
      style={style}
    >
      <div className="text-gray-2 text-base font-semibold leading-normal">
        资源中心选择任务题目
      </div>
      <Button
        type="default"
        radius="full"
        className="border-line-3 h-12 px-10 py-3 disabled:bg-[#F0F4FF] disabled:border-[#BACEFD]"
        disabled={checkedClasses.value.length === 0}
        onClick={() => {
          currentAssignStep.value = "select-resource";
          umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_RESOURCE_CLICK, {});
        }}
      >
        <Image
          src={checkedClasses.value.length === 0 ? disablePlusIcon : plusIcon}
          alt="plus"
          width={16}
          height={16}
          className="mr-1 size-4"
        />
        <div className={cn("text-primary-1 text-base font-medium leading-[normal]", checkedClasses.value.length === 0 && "text-primary-4")}>
          资源中心选题
        </div>
      </Button>
    </div>
  );
}
