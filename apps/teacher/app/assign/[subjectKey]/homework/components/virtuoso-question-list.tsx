import { getQuestionList } from "@/services/assign-homework";
import { cn } from "@/utils";
import to from "await-to-js";
import { memo, useCallback, useContext, useEffect, useRef } from "react";
import { AssignHomeworkContext } from "../store";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { AssignContext } from "@/app/assign/store";
import {
  convertQuestion,
  createTempSelection,
  removeTempSelection,
} from "@/utils";
import { toast } from "@/ui/toast";
import { QuestionItem, QaContentType } from "@repo/core/views/tch-question-view";
import { Button } from "@/ui/tch-button";
import { AssignEmpty } from "@/components/assign/assign-empty";
import { AssignSubjectContext } from "../../store";
import { Virtuoso, VirtuosoHandle } from "react-virtuoso";


const PAGE_SIZE = 10;

interface VirtuosoQuestionListProps {
  style?: React.CSSProperties;
  className?: string;
  minHeight?: number;
  maxHeight?: number;
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
}

export function VirtuosoQuestionList({
  style = {},
  className = "",
  onScroll,
  minHeight,
  maxHeight,
}: VirtuosoQuestionListProps) {
  const {
    bizTreeNodeId,
    knowledgeTreeNodeId,
    questionFilters,
    selectionResourceList,
    deleteSelectionQuestionByIds,
    historySelectionList,
    resourcePublicCategory,
  } = useContext(AssignHomeworkContext);
  const { questionFilterEnumMap } = useContext(AssignSubjectContext);
  const { currentSubject } = useContext(AssignContext);

  const questionList = useSignal<QaContentType[]>([]);
  const loading = useSignal(false);
  const page = useSignal(1);
  const parentRef = useRef<HTMLDivElement>(null);
  const virtuosoRef = useRef<VirtuosoHandle>(null);
  const hasMore = useSignal(false);
  const fileters = useComputed(() => {
    const {
      provinceList,
      questionDifficultList,
      questionTypeList,
      sort,
      sourceList,
      yearList,
    } = questionFilters.value;
    return {
      questionProvince: provinceList,
      questionDifficult: questionDifficultList,
      questionType: questionTypeList,
      questionYears: yearList,
      sort,
      resourceType: sourceList,
    };
  });
  const nodeIds = useComputed(() => {
    const bizTreeNodeIds = bizTreeNodeId.value ? [bizTreeNodeId.value] : [];
    const knowledgeTreeNodeIds = knowledgeTreeNodeId.value
      ? [knowledgeTreeNodeId.value]
      : [];
    if (resourcePublicCategory.value === "chapter") {
      return {
        bizTreeNodeIds,
      };
    }
    return {
      bizTreeNodeIds: knowledgeTreeNodeIds,
    };
  });
  const payload = useComputed(() => {
    return {
      subject: currentSubject.value.subjectKey,
      ...nodeIds.value,
      ...fileters.value,
      pageSize: PAGE_SIZE,
    };
  });

  

  // 获取题目
  const fetchQuestionList = useCallback(async () => {
    const [err, res] = await to(
      getQuestionList({ ...payload.value, page: page.value })
    );
    if (err) {
      console.error(err);
      questionList.value = [];
      return;
    }
    const list = Array.isArray(res.list) ? res.list : [];
    const convertedList = list.map((item) => {
      return convertQuestion(item, questionFilterEnumMap.value);
    });
    const isFirstPage = page.value === 1;
    questionList.value = isFirstPage
      ? convertedList
      : [...questionList.value, ...convertedList];
    hasMore.value = res.page < res.total / PAGE_SIZE;
    if (isFirstPage) {
      // 滚动到顶部
      virtuosoRef.current?.scrollToIndex(0);
    }
  }, [
    payload,
    questionFilterEnumMap,
    questionList,
    hasMore,
    page,
  ]);

  const loadMoreQuestions = useCallback(async () => {
    if (loading.value) {
      return;
    }
    loading.value = true;
    await fetchQuestionList();
    page.value++;
    loading.value = false;
  }, [loading, page, fetchQuestionList]);

  // 组件初始化时，获取题目
  useEffect(() => {
    loadMoreQuestions();
  }, [loadMoreQuestions]);

  useEffect(() => {
    page.value = 1;
    loadMoreQuestions();
  }, [payload, payload.value, page, loadMoreQuestions]);

  const onScrollBottomHandler = (state: boolean) => {
    console.log("onScrollBottomHandler: ", state);
    // if (state && hasMore.value && !loading.value) {
    //   loadMoreQuestions();
    // }
  };

  const onEndReached = () => {
    console.log("onEndReached: ");
    if (hasMore.value && !loading.value) {
      loadMoreQuestions();
    }
  };

  const onScrollTopHandler = (state: boolean) => {
    console.log("onScrollTopHandler: ", state);
  };

  const onQuestionAdd = (qaContent: QaContentType) => {
    createTempSelection(qaContent.questionId, currentSubject.value.subjectKey).then((res) => {
      if (!res) {
        deleteSelectionQuestionByIds([qaContent.questionId]);
        toast.error("加入失败");
        return;
      }
      toast.success("加入成功");
    });
  };

  const onQuestionRemove = (qaContent: QaContentType) => {
    if (
      historySelectionList.value.some(
        (item) => item.questionId === qaContent.questionId
      )
    ) {
      return;
    }
    removeTempSelection([qaContent.questionId], currentSubject.value.subjectKey).then((res) => {
      if (!res) {
        selectionResourceList.value = [
          ...selectionResourceList.value,
          qaContent,
        ];
        toast.error("取消加入失败");
        return;
      }
      toast.success("取消加入成功");
    });
  };

  const selectQuestionHandler = (
    qaContent: QaContentType,
    isAdd: boolean = true
  ) => {
    if (isAdd) {
      selectionResourceList.value = [...selectionResourceList.value, qaContent];
      onQuestionAdd(qaContent);
      return;
    }
    deleteSelectionQuestionByIds([qaContent.questionId]);
    onQuestionRemove(qaContent);
  };

  const FooterButton = ({ qaContent }: { qaContent: QaContentType }) => {
    const hasQuestion = selectionResourceList.value.some(
      (item) => item.questionId === qaContent.questionId
    );
    return hasQuestion ? (
      <Button
        type="outline"
        size="md"
        radius="full"
        className="h-8.5 border-danger-2 text-danger-1"
        onClick={() => selectQuestionHandler(qaContent, false)}
      >
        取消加入
      </Button>
    ) : (
      <Button
        type="outline"
        size="md"
        radius="full"
        className="h-8.5"
        onClick={() => selectQuestionHandler(qaContent)}
      >
        加入
      </Button>
    );
  };

  const VirtuosoQuestionItem = memo(({ index }: { index: number }) => {
    
    const question = questionList.value[index];
    if (!question) {
      return null;
    }
    return (
      <div className="pb-4">
        <QuestionItem
          qaContent={question}
          index={index}
          footerButton={<FooterButton qaContent={question} />}
        />
      </div>
    );
  });
  VirtuosoQuestionItem.displayName = "VirtuosoQuestionItem";

  return (
    <div
      className={cn("", className)}
      style={style}
      ref={parentRef}
    >
      {/* 虚拟列表容器 */}
      {questionList.value.length > 0 ? (
        <Virtuoso
          // style={{
          //     height: `700px`,
          // }}
          style={{
            minHeight: minHeight + "px",
            height: maxHeight + "px",
          }}
          totalCount={questionList.value.length}
          itemContent={(index) => {
            return <VirtuosoQuestionItem index={index} />;
          }}
          atBottomThreshold={50}
          atBottomStateChange={onScrollBottomHandler}
          atTopStateChange={onScrollTopHandler}
          endReached={onEndReached}
          onScroll={onScroll}
          ref={virtuosoRef}
        />
      ) : (
        <AssignEmpty
          type="normal"
          className="flex-1 rounded-2xl border-none h-full"
          content="当前条件没有可选题目~"
          style={{
            // height: minHeight + "px",
          }}
        />
      )}
    </div>
  );
}
