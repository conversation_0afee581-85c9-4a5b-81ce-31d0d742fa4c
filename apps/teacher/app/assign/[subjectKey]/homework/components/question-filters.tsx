import { AssignSelect } from "@/components/assign/assign-select";
import { cn } from "@/utils/utils";
import { useContext } from "react";
import { AssignHomeworkContext } from "../store";
import { QuestionFiltersType } from "@/types/assign";
import { QuestionFilterSort } from "@/enums";
import { useComputed } from "@preact-signals/safe-react";
import { QuestionFilterMore } from "./question-filter-more";
import { filterEnumFieldNames } from "@/configs";
import { AssignSubjectContext } from "../../store";

interface QuestionFiltersProps {
  style?: React.CSSProperties;
  className?: string;
}

export function QuestionFilters({
  style = {},
  className = "",
}: QuestionFiltersProps) {
  const { questionFilters } = useContext(AssignHomeworkContext);
  const { questionFilterEnum } = useContext(AssignSubjectContext);
  const sort = useComputed(() => {
    return questionFilters.value.sort;
  });
  const questionType = useComputed(() => {
    return String(questionFilters.value.questionTypeList[0] ?? "");
  });
  const questionDifficult = useComputed(() => {
    return String(questionFilters.value.questionDifficultList[0] ?? "");
  });
  const questionTypeList = useComputed(() => {
    return [
      { nameZh: "全部", value: "all" },
      ...questionFilterEnum.value.questionTypeList,
    ];
  });
  const questionDifficultList = useComputed(() => {
    return [
      { nameZh: "全部", value: "all" },
      ...questionFilterEnum.value.questionDifficultList,
    ];
  });

  const onValueChange = (value: string, key: keyof QuestionFiltersType) => {
    const filters = questionFilters.value;
    if (key === "sort") {
      filters.sort = value as QuestionFilterSort;
    }
    if (key === "questionTypeList") {
      filters.questionTypeList = value === "all" ? [] : [Number(value)];
    }
    if (key === "questionDifficultList") {
      filters.questionDifficultList = value === "all" ? [] : [Number(value)];
    }
    questionFilters.value = { ...filters };
  };

  return (
    <div
      className={cn("flex items-center justify-center gap-5", className)}
      style={style}
    >
      <AssignSelect
        placeholder="排序"
        options={questionFilterEnum.value.sortList}
        fieldNames={filterEnumFieldNames}
        value={sort.value}
        onValueChange={(value) => onValueChange(value, "sort")}
        variant="borderless"
        align="center"
      />
      <AssignSelect
        placeholder="类型"
        options={questionTypeList.value}
        fieldNames={filterEnumFieldNames}
        value={questionType.value}
        onValueChange={(value) => onValueChange(value, "questionTypeList")}
        variant="borderless"
        align="center"
      />
      <AssignSelect
        placeholder="难度"
        options={questionDifficultList.value}
        fieldNames={filterEnumFieldNames}
        value={questionDifficult.value}
        onValueChange={(value) => onValueChange(value, "questionDifficultList")}
        variant="borderless"
        align="center"
      />
      <QuestionFilterMore />
    </div>
  );
}
