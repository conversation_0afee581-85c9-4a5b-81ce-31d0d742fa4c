import { cn } from "@/utils/utils";
import { useContext } from "react";
import { AssignHomeworkContext } from "../store";
import { Button } from "@/ui/tch-button";
import { QuestionItem, QaContentType } from "@repo/core/views/tch-question-view";

interface QuestionListProps {
  style?: React.CSSProperties;
  className?: string;
  questionClassName?: string;
  list: QaContentType[];
  onlyDelete?: boolean;
  onAdd?: (qaContent: QaContentType) => void;
  onRemove?: (qaContent: QaContentType) => void;
}

export function QuestionList({
  style = {},
  className = "",
  questionClassName = "",
  list = [],
  onlyDelete = false,
  onAdd,
  onRemove,
}: QuestionListProps) {
  const { selectionResourceList, deleteSelectionQuestionByIds } = useContext(
    AssignHomeworkContext
  );

  const selectQuestionHandler = (
    qaContent: QaContentType,
    isAdd: boolean = true
  ) => {
    if (isAdd) {
      selectionResourceList.value = [...selectionResourceList.value, qaContent];
      onAdd?.(qaContent);
      return;
    }
    deleteSelectionQuestionByIds([qaContent.questionId]);
    onRemove?.(qaContent);
  };

  const FooterButton = ({ qaContent }: { qaContent: QaContentType }) => {
    if (onlyDelete) {
      return (
        <Button
          type="default"
          size="lg"
          radius="full"
          onClick={() => selectQuestionHandler(qaContent, false)}
        >
          删 除
        </Button>
      );
    }
    const hasQuestion = selectionResourceList.value.some(
      (item) => item.questionId === qaContent.questionId
    );
    return hasQuestion ? (
      <Button
        type="outline"
        size="md"
        radius="full"
        className="h-8.5 border-danger-2 text-danger-1"
        onClick={() => selectQuestionHandler(qaContent, false)}
      >
        取消加入
      </Button>
    ) : (
      <Button
        type="outline"
        size="md"
        radius="full"
        className="h-8.5"
        onClick={() => selectQuestionHandler(qaContent)}
      >
        加入
      </Button>
    );
  };

  return (
    <div className={cn("space-y-4", className)} style={style}>
      {list.map((qaContent, index) => (
        <QuestionItem
          key={qaContent.questionId + index}
          qaContent={qaContent}
          index={index}
          className={cn("", questionClassName)}
          footerButton={<FooterButton qaContent={qaContent} />}
        />
      ))}
    </div>
  );
}
