import { getQuestionList } from "@/services/assign-homework";
import { cn } from "@/utils/utils";
import to from "await-to-js";
import { useCallback, useContext, useEffect, useRef } from "react";
import { AssignHomeworkContext } from "../store";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { AssignContext } from "@/app/assign/store";
import {
  convertQuestion,
  createTempSelection,
  removeTempSelection,
} from "@/utils";
import { toast } from "@/ui/toast";
import { useVirtualizer } from "@tanstack/react-virtual";
import { Button } from "@/ui/tch-button";
import { AssignEmpty } from "@/components/assign/assign-empty";
import { AssignSubjectContext } from "../../store";
import { QuestionItem, QaContentType } from "@repo/core/views/tch-question-view";

const PAGE_SIZE = 10;

interface InfiniteQuestionListProps {
  style?: React.CSSProperties;
  className?: string;
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
}

export function InfiniteQuestionList({
  style = {},
  className = "",
  onScroll,
}: InfiniteQuestionListProps) {
  const {
    bizTreeNodeId,
    knowledgeTreeNodeId,
    questionFilters,
    selectionResourceList,
    deleteSelectionQuestionByIds,
    historySelectionList,
    resourcePublicCategory,
  } = useContext(AssignHomeworkContext);
  const { questionFilterEnumMap } = useContext(AssignSubjectContext);
  const { currentSubject } = useContext(AssignContext);

  const questionList = useSignal<QaContentType[]>([]);
  const loading = useSignal(false);
  const page = useSignal(1);
  const parentRef = useRef<HTMLDivElement>(null);
  const hasMore = useSignal(false);
  const fileters = useComputed(() => {
    const {
      provinceList,
      questionDifficultList,
      questionTypeList,
      sort,
      sourceList,
      yearList,
    } = questionFilters.value;
    return {
      questionProvince: provinceList,
      questionDifficult: questionDifficultList,
      questionType: questionTypeList,
      questionYears: yearList,
      sort,
      resourceType: sourceList,
    };
  });
  const nodeIds = useComputed(() => {
    const bizTreeNodeIds = bizTreeNodeId.value ? [bizTreeNodeId.value] : [];
    const knowledgeTreeNodeIds = knowledgeTreeNodeId.value
      ? [knowledgeTreeNodeId.value]
      : [];
    if (resourcePublicCategory.value === "chapter") {
      return {
        bizTreeNodeIds,
      };
    }
    return {
      bizTreeNodeIds: knowledgeTreeNodeIds,
    };
  });
  const payload = useComputed(() => {
    return {
      subject: currentSubject.value.subjectKey,
      ...nodeIds.value,
      ...fileters.value,
      pageSize: PAGE_SIZE,
    };
  });

  const rowVirtualizer = useVirtualizer({
    count: questionList.value.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 520,
    overscan: PAGE_SIZE,
    // measureElement: (element: HTMLElement) => element?.offsetHeight || 50,
    // onChange: (instance) => {
    //   console.log("instance***: ", instance, payload.value);
    // }
  });

  // 获取题目
  const fetchQuestionList = useCallback(async () => {
    const [err, res] = await to(getQuestionList({...payload.value, page: page.value}));
    if (err) {
      console.error(err);
      questionList.value = [];
      return;
    }
    const list = Array.isArray(res.list) ? res.list : [];
    const convertedList = list.map((item) => {
      return convertQuestion(item, questionFilterEnumMap.value);
    });
    const isFirstPage = page.value === 1;
    questionList.value = isFirstPage
      ? convertedList
        : [...questionList.value, ...convertedList];
    hasMore.value = res.page < (res.total / PAGE_SIZE);
    if (isFirstPage) {
      rowVirtualizer.scrollToIndex(0);
    }
  }, [payload, questionFilterEnumMap, questionList, hasMore, page, rowVirtualizer]);

  const loadMoreQuestions = useCallback(async () => {
    if (loading.value) {
      return;
    }
    loading.value = true;
    await fetchQuestionList();
    page.value++;
    loading.value = false;
  }, [loading, page, fetchQuestionList]);

  

  // 组件初始化时，获取题目
  useEffect(() => {
    loadMoreQuestions();
  }, [loadMoreQuestions]);

  useEffect(() => {
    page.value = 1;
    loadMoreQuestions();
  }, [payload, payload.value, page, loadMoreQuestions, rowVirtualizer]);

  useEffect(() => {
    const scrollElement = parentRef.current;
    if (!scrollElement) {
      return;
    }

    const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
      if (rowVirtualizer.getVirtualItems().length === 0) {
        return;
      }
      onScroll?.(e);
      const lastItem =
        rowVirtualizer.getVirtualItems()[
          rowVirtualizer.getVirtualItems().length - 1
        ];

      console.log("最后一个可视项索引: ", lastItem.index);
      console.log("题目列表长度: ", questionList.value.length);
      console.log("是否还有更多: ", hasMore.value);
      console.log("是否正在加载: ", loading.value);
      if (
        lastItem.index === questionList.value.length - 1 &&
        hasMore.value &&
        !loading.value
      ) {
        loadMoreQuestions();
      }
    };

    const typedHandleScroll = (e: Event) =>
      handleScroll(e as unknown as React.UIEvent<HTMLDivElement>);

    scrollElement.addEventListener("scroll", typedHandleScroll);
    return () => scrollElement.removeEventListener("scroll", typedHandleScroll);
  }, [
    rowVirtualizer,
    loadMoreQuestions,
    hasMore,
    questionList,
    loading,
    onScroll,
  ]);

  const onQuestionAdd = (qaContent: QaContentType) => {
    createTempSelection(qaContent.questionId, currentSubject.value.subjectKey).then((res) => {
      if (!res) {
        deleteSelectionQuestionByIds([qaContent.questionId]);
        toast.error("加入失败");
        return;
      }
      toast.success("加入成功");
    });
  };

  const onQuestionRemove = (qaContent: QaContentType) => {
    if (
      historySelectionList.value.some(
        (item) => item.questionId === qaContent.questionId
      )
    ) {
      return;
    }
    removeTempSelection([qaContent.questionId], currentSubject.value.subjectKey).then((res) => {
      if (!res) {
        selectionResourceList.value = [
          ...selectionResourceList.value,
          qaContent,
        ];
        toast.error("取消加入失败");
        return;
      }
      toast.success("取消加入成功");
    });
  };

  const selectQuestionHandler = (
    qaContent: QaContentType,
    isAdd: boolean = true
  ) => {
    if (isAdd) {
      selectionResourceList.value = [...selectionResourceList.value, qaContent];
      onQuestionAdd(qaContent);
      return;
    }
    deleteSelectionQuestionByIds([qaContent.questionId]);
    onQuestionRemove(qaContent);
  };

  const FooterButton = ({ qaContent }: { qaContent: QaContentType }) => {
    const hasQuestion = selectionResourceList.value.some(
      (item) => item.questionId === qaContent.questionId
    );
    return hasQuestion ? (
      <Button
        type="outline"
        size="md"
        radius="full"
        className="h-8.5 border-danger-2 text-danger-1"
        onClick={() => selectQuestionHandler(qaContent, false)}
      >
        取消加入
      </Button>
    ) : (
      <Button
        type="outline"
        size="md"
        radius="full"
        className="h-8.5"
        onClick={() => selectQuestionHandler(qaContent)}
      >
        加入
      </Button>
    );
  };

  return (
    <div
      className={cn("space-y-4 flex flex-col", className)}
      style={style}
      ref={parentRef}
      onScroll={onScroll}
    >
      {/* 虚拟列表容器 */}
      {questionList.value.length > 0 ? (
        <div
          className="relative"
          style={{ height: `${rowVirtualizer.getTotalSize()}px` }}
      >
        {/* 渲染可见项 */}
        {rowVirtualizer.getVirtualItems().map((virtualRow) => {
          const qaContent = questionList.value[virtualRow.index];
          return (
            <div
              className="absolute top-0 left-0 w-full pb-4"
              key={qaContent.questionId + String(virtualRow.index)}
              data-index={virtualRow.index}
              ref={rowVirtualizer.measureElement}
              style={{
                transform: `translateY(${virtualRow.start}px)`,
              }}
            >
              <QuestionItem
                qaContent={qaContent}
                index={virtualRow.index}
                className={cn("")}
                footerButton={<FooterButton qaContent={qaContent} />}
              />
            </div>
          );
        })}
        </div>
      ) : (
        <AssignEmpty
          type="normal"
          className="flex-1 rounded-2xl border-none"
          content="当前条件没有可选题目~"
        />
      )}
    </div>
  );
}
