"use client";
import { cn } from "@/utils/utils";
import { useContext } from "react";
import { AssignHomeworkContext } from "../../store";
import { useComputed } from "@preact-signals/safe-react";
import { QuestionFilterEnumValueType } from "@/types/assign";
import * as echarts from "echarts";
import { PieChart } from "echarts/charts";
import { LegendComponent } from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
import ReactECharts from "echarts-for-react";
import { difficultColorScheme } from "@/configs";
import { AssignCard } from "@/components/assign/assign-card";
import { AssignSubjectContext } from "../../../store";
import { groupBy } from "@/utils/assign";
// eslint-disable-next-line @typescript-eslint/no-explicit-any
echarts.use([PieC<PERSON>, LegendComponent, CanvasRenderer] as any);

interface DifficultChartProps {
  style?: React.CSSProperties;
  className?: string;
}

export function DifficultChart({
  style = {},
  className = "",
}: DifficultChartProps) {
  const { selectionResourceList } = useContext(AssignHomeworkContext);
  const { questionFilterEnumMap } = useContext(AssignSubjectContext);
  const questionDifficults = useComputed(() => {
    const map = questionFilterEnumMap.value.questionDifficultList;
    return map ? [...map.keys()] : [];
  });
  const typedQuestionGroups = useComputed(() => {
    const difficultMap = questionFilterEnumMap.value.questionDifficultList;
    if (!difficultMap) {
      return [];
    }
    const difficults = questionDifficults.value;
    const tempGroups = groupBy(selectionResourceList.value, (item) => {
      return (
        difficults.includes(item.questionDifficult ?? -1)
          ? item.questionDifficult
          : -1
      ) as QuestionFilterEnumValueType;
    });
    const groups = Array.from(difficultMap, ([key, value]) => {
      const questions = tempGroups[key] || [];
      return {
        name: value.nameZh,
        value: questions.length,
        itemStyle: {
          color:
            difficultColorScheme[
              value.nameZh as keyof typeof difficultColorScheme
            ],
        },
      };
    });
    if (tempGroups[-1]) {
      groups.push({
        name: "未知",
        value: (tempGroups[-1] || []).length,
        itemStyle: {
          color: difficultColorScheme["未知"],
        },
      });
    }
    return groups.filter((item) => item.value > 0);
  });

  const option = useComputed(() => {
    return {
      legend: {
        orient: "vertical",
        right: 20,
        top: "center",
        itemGap: 18,
        itemHeight: 8,
        itemWidth: 8,
        textStyle: {
          color: "#101019",
          fontSize: 14,
          height: 25,
          padding: [0, 0, 0, 5],
        },
      },
      tooltip: {
        show: true,
        formatter: "占比 {d}%",
      },
      series: [
        {
          name: "难度构成",
          type: "pie",
          center: ["40%", "50%"],
          radius: ["50%", "70%"], // 环形图的内外半径
          avoidLabelOverlap: false,
          label: {
            show: true,
            formatter: "{b} {c}",
            rich: {
              b: {
                fontSize: 14,
                color: "#444963",
              },
              c: {
                fontSize: 14,
                color: "#444963",
              },
            },
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: "bold",
            },
          },
          labelLine: {
            show: true,
          },
          data: typedQuestionGroups.value,
        },
      ],
    };
  });
  return (
    <AssignCard className={cn("p-5", className)} style={style}>
      <div className="h-60">
        <ReactECharts
          echarts={echarts}
          option={option.value}
          className="h-full w-full"
          style={{ height: "100%", width: "100%" }}
        />
      </div>
    </AssignCard>
  );
}
