import { QuestionFilterEnumValueType, QuestionTypeGroup } from "@/types/assign";
import { But<PERSON> } from "@/ui/tch-button";
import { Separator } from "@/ui/separator";
import { cn } from "@/utils/utils";
import { X } from "lucide-react";

interface QuestionTypeStatisticsProps {
  style?: React.CSSProperties;
  className?: string;
  list: QuestionTypeGroup[];
  onQuestionTypeClick?: (questionType: QuestionFilterEnumValueType) => void;
  onRemove: (questionType: QuestionFilterEnumValueType) => void;
}

export function QuestionTypeStatistics({
  style = {},
  className = "",
  onQuestionTypeClick = () => {},
  list = [],
  onRemove = () => {},
}: QuestionTypeStatisticsProps) {
  return (
    <div className={cn("flex flex-wrap gap-x-3 gap-y-2", className)} style={style}>
      {list.map((item) => (
        <Button
          key={item.questionType}
          type="default"
          className="bg-fill-gray-1 border-line-1 flex items-center rounded-md p-0"
        >
          <span
            className="text-gray-2 px-3 py-1.5 text-sm font-normal leading-normal"
            onClick={() => onQuestionTypeClick(item.questionType)}
          >
            {item.typeName} ({item.questions.length})
          </span>
          <Separator
            className="h-5.25! w-px bg-slate-200"
            orientation="vertical"
          />
          <span className="py-1.5 pl-1.5 pr-3">
            <X
              className="text-gray-2 size-4 cursor-pointer"
              onClick={() => onRemove(item.questionType)}
            />
          </span>
        </Button>
      ))}
    </div>
  );
}
