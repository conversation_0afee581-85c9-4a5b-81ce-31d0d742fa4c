import { cn } from "@/utils/utils";

interface HeadingProps {
    style?: React.CSSProperties,
    content: string,
    className?: string,
    extra?: React.ReactNode,
}

export function Heading({
    content,
    style = {},
    className = "",
    extra = null,
}: HeadingProps) {
    return (
        <div className={cn("flex items-center select-none text-base font-medium leading-6 text-gray-1", className)} style={style}>
            <div className="flex-1">{content}</div>
            {extra}
        </div>
    )
}

