import { cn } from "@/utils/utils";
import { useContext, useRef } from "react";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { TchDrawer } from "@/ui/tch-drawer";
import { AssignCard } from "@/components/assign/assign-card";
import { Button } from "@/ui/tch-button";
import { TchClearBtn } from "@/components/common/tch-clear-btn";
import { AssignHomeworkContext } from "../../store";
import { Heading } from "./heading";
import { QuestionTypeStatistics } from "./question-type-statistics";
import {
  QuestionFilterEnumValueType,
  QuestionTypeGroup,
  removeTempSelection,
} from "@/types/assign";
import { DifficultChart } from "./difficult-chart";
import { QuestionList } from "../question-list";
import { toast } from "@/ui/toast";
import { AlertDialog } from "@/ui/alertDialog";
import { AssignEmpty } from "@/components/assign/assign-empty";
import { AssignSubjectContext } from "../../../store";
import { groupBy, umeng, UmengAssignAction, UmengCategory } from "@/utils";
import { AssignContext } from "@/app/assign/store";
import { QaContentType } from "@repo/core/views/tch-question-view";

interface QuestionAnalysisProps {
  style?: React.CSSProperties;
  className?: string;
}

export function QuestionAnalysis({
  style = {},
  className = "",
}: QuestionAnalysisProps) {
  const contentRefs = useRef(
    {} as Record<QuestionFilterEnumValueType, HTMLDivElement>
  );
  const {
    selectedResourceListLength,
    selectionResourceList,
    historySelectionList,
  } = useContext(AssignHomeworkContext);
  const { currentSubject } = useContext(AssignContext);
  const { questionFilterEnumMap } = useContext(AssignSubjectContext);
  const open = useSignal(false);
  const deleteAlertOpen = useSignal(false);
  const alertTitle = useSignal("");
  const alertDescription = useSignal("");
  const deletQuestionType = useSignal<QuestionFilterEnumValueType | undefined>(
    undefined
  );
  const alertTip = useSignal("");

  const questionTypes = useComputed(() => {
    const map = questionFilterEnumMap.value.questionTypeList;
    return map ? [...map.keys()] : [];
  });
  const typedQuestionGroups = useComputed<QuestionTypeGroup[]>(() => {
    const questionTypesMap = questionFilterEnumMap.value.questionTypeList;
    if (!questionTypesMap) {
      return [];
    }
    const types = questionTypes.value;
    const tempGroups = groupBy(selectionResourceList.value, (item) => {
      return (
        types.includes(item.questionType) ? item.questionType : -1
      ) as QuestionFilterEnumValueType;
    });
    const groups = Array.from(questionTypesMap, ([key, value]) => {
      const questions = tempGroups[key] || [];
      return {
        questionType: key,
        typeName: value.nameZh,
        questions,
      };
    });
    if (tempGroups[-1]) {
      groups.push({
        questionType: -1,
        typeName: "其他题",
        questions: tempGroups[-1] || [],
      });
    }
    return groups.filter((item) => item.questions.length > 0);
  });

  const historyQuestionIds = useComputed(() => {
    return historySelectionList.value.map((item) => item.questionId);
  });

  const onOpen = () => {
    open.value = true;
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_QUESTION_ANALYST_BUTTON_CLICK,
      {}
    );
  };

  // 点击题型，滚动到对应的问题
  const onQuestionTypeClick = (questionType: QuestionFilterEnumValueType) => {
    contentRefs.current[questionType]?.scrollIntoView({
      behavior: "smooth",
      block: "start", // 对齐到顶部
    });
  };

  const onQuestionRemove = (
    qaContents: QaContentType[],
    tip: string = "删除"
  ) => {
    const questions = qaContents.filter(
      (item) => !historyQuestionIds.value.includes(item.questionId)
    );
    const questionIds = questions.map((item) => item.questionId);
    removeTempSelection(questionIds, currentSubject.value.subjectKey).then(
      (res) => {
        if (!res) {
          selectionResourceList.value = [
            ...selectionResourceList.value,
            ...qaContents,
          ];
          toast.error(`${tip}失败`);
          return;
        }
        toast.success(`${tip}成功`);
      }
    );
  };

  // 删除选择的问题
  const removeQuestionsByType = (
    questionType: QuestionFilterEnumValueType | undefined = undefined
  ) => {
    // 删除所有问题
    if (questionType === undefined) {
      const allQuestions = selectionResourceList.value;
      selectionResourceList.value = [];
      onQuestionRemove(allQuestions, "清空");
      umeng.trackEvent(
        UmengCategory.ASSIGN,
        UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_QUESTION_ANALYST_WINDOW_CLICK_1,
        {}
      );
      return;
    }
    // 删除指定类型的问题
    const group = typedQuestionGroups.value.find(
      (item) => item.questionType === questionType
    );
    const { questions, typeName } = group || {};
    const deleteList = questions || [];
    if (deleteList.length > 0) {
      const filteredList = selectionResourceList.value.filter((item) => {
        if (questionType === -1) {
          return questionTypes.value.includes(item.questionType);
        }
        return item.questionType !== questionType;
      });
      selectionResourceList.value = filteredList;
      onQuestionRemove(deleteList, `删除${typeName}`);
      umeng.trackEvent(
        UmengCategory.ASSIGN,
        UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_QUESTION_ANALYST_WINDOW_CLICK_3,
        {
          delete_category_id: questionType,
          delete_category_name: typeName,
        }
      );
    }
  };

  const onDeleteAll = () => {
    alertTitle.value = "确定要清空所有试题篮吗？";
    alertDescription.value = "清空后，当前选择的试题不保存。";
    deletQuestionType.value = undefined;
    alertTip.value = "清空";
    deleteAlertOpen.value = true;
  };

  const clearQuestionsByType = (questionType: QuestionFilterEnumValueType) => {
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_QUESTION_ANALYST_WINDOW_CLICK_2,
      {
        delete_category_clicked: "已点击",
      }
    );
    const group = typedQuestionGroups.value.find(
      (item) => item.questionType === questionType
    );
    const { questions, typeName } = group || {};
    if (questions && questions.length > 0) {
      alertTitle.value = `确定要删除所有${typeName}吗？`;
      alertDescription.value = "删除后，当前选择的试题不保存。";
      deletQuestionType.value = questionType;
      alertTip.value = `删除${typeName}`;
      deleteAlertOpen.value = true;
    } else {
      removeQuestionsByType(questionType);
    }
  };

  const onStatisticsRemove = (questionType: QuestionFilterEnumValueType) => {
    clearQuestionsByType(questionType);
  };

  return (
    <>
      <Button
        type="outline"
        radius="full"
        className={cn(
          "border-primary-2 disabled:border-primary-3 disabled:text-primary-4 h-9 px-5",
          className
        )}
        style={style}
        disabled={selectedResourceListLength.value === 0}
        onClick={onOpen}
      >
        {/* 题目分析 */}
        已加入 ({selectedResourceListLength.value})
      </Button>
      <TchDrawer
        open={open.value}
        onOpenChange={(v) => (open.value = v)}
        title={`已加入（${selectedResourceListLength.value}题）`}
      >
        {selectedResourceListLength.value > 0 ? (
          <AssignCard className="flex-1 p-5 outline-none">
            <div className="space-y-6">
              <div className="space-y-2">
                <Heading
                  content="题型统计"
                  extra={<TchClearBtn content="清空" onClick={onDeleteAll} />}
                />
                <QuestionTypeStatistics
                  list={typedQuestionGroups.value}
                  onRemove={(questionType) => onStatisticsRemove(questionType)}
                  onQuestionTypeClick={onQuestionTypeClick}
                />
              </div>
              <div className="space-y-2">
                <Heading content="难度构成" />
                <DifficultChart />
              </div>
              {typedQuestionGroups.value.map((item) => (
                <div
                  className="space-y-2"
                  key={item.questionType}
                  ref={(el: HTMLDivElement | null) => {
                    if (el) {
                      contentRefs.current[item.questionType] = el;
                    }
                  }}
                >
                  <Heading
                    content={item.typeName}
                    extra={
                      <TchClearBtn
                        content="全部清空"
                        onClick={() => clearQuestionsByType(item.questionType)}
                      />
                    }
                  />
                  <div className="space-y-3">
                    <QuestionList
                      list={item.questions}
                      onAdd={() => {}}
                      onRemove={(question) => {
                        onQuestionRemove([question], "删除");
                        umeng.trackEvent(
                          UmengCategory.ASSIGN,
                          UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_QUESTION_ANALYST_WINDOW_CLICK_4,
                          {
                            delete_single_question_clicked: "已点击",
                            delete_single_question_id: question.questionId,
                          }
                        );
                      }}
                      onlyDelete={true}
                      className="border! border-solid! border-line-1 flex-1 py-5"
                    />
                  </div>
                </div>
              ))}
            </div>
          </AssignCard>
        ) : (
          <AssignEmpty
            type="normal"
            className="flex-1 rounded-2xl border-none"
            content="这里还没有哦~"
          />
        )}

        <AlertDialog
          open={deleteAlertOpen.value}
          title={alertTitle.value}
          description={alertDescription.value}
          onCancel={() => {
            deleteAlertOpen.value = false;
          }}
          onOk={() => {
            deleteAlertOpen.value = false;
            removeQuestionsByType(deletQuestionType.value);
          }}
          variant="warning"
        />
      </TchDrawer>
    </>
  );
}
