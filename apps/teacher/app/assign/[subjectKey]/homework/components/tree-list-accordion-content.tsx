import { AssignTree } from "@/components/assign/assign-tree";
import { TchTreeSkeleton } from "@/components/common/tch-tree-skeleton";
import { BizTreeListItem } from "@/types/assign";
import { filterTreeNodes } from "@/utils";
import { cn } from "@/utils/utils";
import { DataNode } from "antd/es/tree";
import { Key, useMemo } from "react";
interface TreeListAccordionContentProps {
  style?: React.CSSProperties;
  className?: string;
  treeItem: BizTreeListItem;
  treeNodeId: number;
  defaultExpandedKeys: Key[];
  search?: string;
  onSelectTreeNode?: (
    keys: Key[],
    { selectedNodes }: { selectedNodes: DataNode[] }
  ) => void;
}

export function TreeListAccordionContent({
  style = {},
  className = "",
  treeItem,
  treeNodeId,
  defaultExpandedKeys,
  search = "",
  onSelectTreeNode = () => {},
}: TreeListAccordionContentProps) {
  const treeData = useMemo(() => {
    const treeData = treeItem.treeFormatInfo?.treeData;
    if (treeData) {
      const filteredTreeData = filterTreeNodes(treeData.bizTreeDetail.bizTreeNodeChildren, search);
      return filteredTreeData;
    }
    return null;
  }, [treeItem.treeFormatInfo, search]);

  return (
    <div className={cn("", className)} style={style}>
      {Array.isArray(treeData) ? (
        <AssignTree
          className="mt-1"
          style={{
            overflowY: "auto",
          }}
          treeData={treeData as unknown as DataNode[]}
          fieldNames={{
            title: "bizTreeNodeName",
            key: "bizTreeNodeId",
            children: "bizTreeNodeChildren",
          }}
          defaultExpandParent
          defaultExpandedKeys={defaultExpandedKeys}
          defaultSelectedKeys={[treeNodeId]}
          onSelect={onSelectTreeNode}
        />
      ) : (
        <TchTreeSkeleton />
      )}
    </div>
  );
}
