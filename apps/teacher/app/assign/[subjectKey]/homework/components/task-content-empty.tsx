import { cn } from "@/utils/utils";
import { AssignCard } from "@/components/assign/assign-card";
import { AddQuestionEntry } from "./add-question-entry";

interface TaskContentEmptyProps {
  style?: React.CSSProperties;
  className?: string;
}

export function TaskContentEmpty({
  style = {},
  className = "",
}: TaskContentEmptyProps) {
  return <AssignCard className={cn("flex items-center justify-center", className)} style={style}>
    <AddQuestionEntry className="h-full" />
  </AssignCard>;
}
