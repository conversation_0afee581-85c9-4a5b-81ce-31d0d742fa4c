import { cn } from "@/utils/utils";

import { useContext } from "react";
import { AssignSubjectContext } from "@/app/assign/[subjectKey]/store";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import { AssignFooterContainer } from "@/components/assign/assign-footer-container";
import { Button } from "@/ui/tch-button";
import { AssignHomeworkContext } from "../store";
import { QuestionAnalysis } from "./question-analysis";
import { AssignCancelAlert } from "@/components/assign/assign-cancel-alert";
import { toast } from "@/ui/toast";
import to from "await-to-js";
import { assignCourse } from "@/services/assign";
import { AssignCourseParams } from "@/types/assign/course";
import { AssignContext } from "@/app/assign/store";
import { AssignTaskTypeEnum } from "@/configs/assign";
import { useTaskExtraInfo } from "../hooks";
import { umeng, UmengAssignAction, UmengCategory } from "@/utils";

interface HomeworkFooterProps {
  style?: React.CSSProperties;
  className?: string;
}

export function HomeworkFooter({
  style = {},
  className = "",
}: HomeworkFooterProps) {
  const { getTaskExtraInfo } = useTaskExtraInfo();
  const { gotoTchAssignPage } = useTchNavigation();
  const { currentSubject } = useContext(AssignContext);
  const { currentAssignStep, checkedClasses, assignTaskName, assignTimeRanges, setTimeStepOrigin } = useContext(AssignSubjectContext);
  const { selectedResourceListLength, selectionResourceList, historySelectionList } = useContext(AssignHomeworkContext);
  const cancelAssignOpen = useSignal(false);

  const disabledNext = useComputed(() => {
    return selectedResourceListLength.value === 0 || checkedClasses.value.length === 0;
  });

  const resources = useComputed(() => {
    return selectionResourceList.value.map((item) => {
      return {
        resourceId: item.resourceId,
        resourceType: item.resourceType,
      };
    });
  });

  const taskParams = useComputed<AssignCourseParams>(() => {
    return {
      subject: currentSubject.value.subjectKey,
      taskType: AssignTaskTypeEnum.TASK_TYPE_HOMEWORK,
      taskName: assignTaskName.value,
      teacherComment: "",
      resources: resources.value,
      studentGroups: assignTimeRanges.value.map((item) => {
        const { classInfo, startTime, endTime, isImmediate } = item;
        return {
          groupType: 2,
          groupId: classInfo.jobClass,
          studentIds: [],
          startTime: isImmediate ? Math.floor(Date.now() / 1000) : Math.floor(startTime / 1000),
          deadline: Math.floor(endTime / 1000),
        };
      }),
    };
  });

  // 检查布置任务的名称、开始时间、结束时间是否合法
  const checkValid = () => {
    if (!assignTaskName.value) {
      alert("名称不能为空");
      return false;
    }
    const startTimeErrItem = assignTimeRanges.value.find((item) => {
      const { startTime } = item;
      const now = Date.now();
      return startTime < now;
    });
    if (startTimeErrItem) {
      const { name } = startTimeErrItem.classInfo;
      alert(`${name} 开始时间不能小于当前时间`);
      return false;
    }
    const endTimeErrItem = assignTimeRanges.value.find((item) => {
      const { startTime, endTime } = item;
      return endTime < startTime;
    });
    if (endTimeErrItem) {
      const { name } = endTimeErrItem.classInfo;
      alert(`${name} 结束时间不能小于开始时间`);
      return false;
    }
    return true;
  };

  const onComplete = async () => {
    if (!checkValid()) {
      return;
    }
    const taskExtraInfo = getTaskExtraInfo();
    const params = {
      ...taskParams.value,
      taskExtraInfo,
    };
    console.log("onComplete params*****--->: ", params);
    const [err, res] = await to(assignCourse(params));
    if (err) {
      console.error(err);
      if (err.message) {
        toast.error(err.message);
      }
      return;
    }
    console.log("onComplete", res);
    toast.success("布置成功");
    umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_SUBMIT, {});
    gotoTchAssignPage(true);
  };

  const umengCancelStep = useComputed(() => {
    const selectedResources = selectionResourceList.value.map((item) => item.resourceId);
    const historyResources = historySelectionList.value.map((item) => item.resourceId);
    const newResources = selectedResources.filter((item) => !historyResources.includes(item));
    const hasHistory = selectedResources.length - newResources.length > 0;
    if (selectedResourceListLength.value > 0 && newResources.length > 0 && checkedClasses.value.length === 0) {
      // 有新题，是复制时取消布置
      return "";
    }
    const ret: string[] = [];
    if (currentAssignStep.value === 'select-target') {
      if (checkedClasses.value.length > 0) {
        ret.push('class_selected');
      }
      if (hasHistory) {
        ret.push('history_selected');
      }
    }
    if (currentAssignStep.value === 'select-resource') {
      ret.push('resource_entered');
    }
    if (currentAssignStep.value === 'set-time') {
      ret.push('time_set');
    }
    return ret.join('/');
  });

  const gotoTimeSetting = () => {
    setTimeStepOrigin.value = currentAssignStep.value;
    currentAssignStep.value = "set-time";
  };

  const onCancelAssign = () => {
    umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_CANCEL_CLICK_1, {});
    if (selectedResourceListLength.value === 0) {
      umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_CANCEL_CLICK_2, {
        cancel_status: '已取消'
      });
      umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_CANCEL_CLICK_3, {
        unfinish_step: umengCancelStep.value
      });
      gotoTchAssignPage(true);
    } else {
      cancelAssignOpen.value = true;
    }
  };


  return (
    <AssignFooterContainer className={cn(className)} style={style}>
      {/* <div className="text-primary-2 text-sm font-normal leading-tight">
        已添加 {selectedResourceListLength.value} 题
      </div> */}
      <Button
        type="default"
        size="lg"
        radius="full"
        onClick={onCancelAssign}
      >
        取消布置
      </Button>
      <QuestionAnalysis />

      {currentAssignStep.value === "select-target" && (
        <Button
          type="primary"
          size="lg"
          radius="full"
          onClick={gotoTimeSetting}
          disabled={disabledNext.value}
        >
          下一步
        </Button>
      )}
      {currentAssignStep.value === "select-resource" && (
        <Button
          type="primary"
          size="lg"
          radius="full"
          onClick={gotoTimeSetting}
          disabled={disabledNext.value}
        >
          去布置
        </Button>
      )}
      {currentAssignStep.value === "set-time" && (
        <Button
          type="primary"
          size="lg"
          radius="full"
          onClick={onComplete}
          disabled={selectedResourceListLength.value === 0}
        >
          完成并布置
        </Button>
      )}
      <AssignCancelAlert
        open={cancelAssignOpen.value}
        onCancel={() => {
          cancelAssignOpen.value = false;
          umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_CANCEL_CLICK_2, {
            cancel_status: '未取消'
          });
        }}
        onOk={() => {
          cancelAssignOpen.value = false;
          umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_CANCEL_CLICK_2, {
            cancel_status: '已取消'
          });
          umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_HOMEWORK_SETUP_CANCEL_CLICK_3, {
            unfinish_step: umengCancelStep.value
          });
          gotoTchAssignPage(true);
        }}
      />
    </AssignFooterContainer>
  );
}
