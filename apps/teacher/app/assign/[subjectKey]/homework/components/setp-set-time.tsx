import { cn } from "@/utils/utils";
import { useContext, useEffect } from "react";
import { AssignSubjectContext } from "@/app/assign/[subjectKey]/store";
import { AssignCourseTimeRange } from "@/types/assign/course";
import { getEndOfDayAfter, getFormatTimeStr } from "@/utils";
import {
  AssignDatePicker,
  DateTimeChangePayload,
} from "@/components/assign/assign-date-picker";
import { AssignHeading } from "@/components/assign/assign-heading";
import { AssignCard } from "@/components/assign/assign-card";
import { TchCounterInput } from "@/components/common/tch-counter-input";
import { AssignContext } from "@/app/assign/store";

interface StepSetTimeProps {
  style?: React.CSSProperties;
  className?: string;
}

export function StepSetTime({ style = {}, className = "" }: StepSetTimeProps) {
  const { assignTimeRanges, updateAssignTimeRange, invalidTimeRanges, assignTaskName } =
    useContext(AssignSubjectContext);
  const { currentSubject } = useContext(AssignContext);
  const recommendEndTime = (range: AssignCourseTimeRange) => {
    return getEndOfDayAfter(range.startTime, 1);
  };

  const onDateTimeChange = (payload: DateTimeChangePayload) => {
    console.log("日期时间变化: ", payload);
    updateAssignTimeRange(payload, 1);
  };

  useEffect(() => {
    if (assignTaskName.value === "") {
      assignTaskName.value = `${getFormatTimeStr(Date.now(), 'MM月dd日')}${currentSubject.value?.subjectName}标准作业`;
    }
  }, [currentSubject.value, assignTaskName]);
  

  return (
    <div
      className={cn("flex h-full flex-col gap-4 px-6 pt-2", className)}
      style={style}
    >
      <AssignHeading content="3. 任务设置" />
      <AssignCard className="flex-1 outline-none">
        <div className="mb-6 flex items-center gap-6">
          <span className="w-17 text-right text-sm leading-normal text-slate-600">
            任务名称
          </span>
          <div className="flex-1">
            <TchCounterInput
              maxLength={100}
              value={assignTaskName.value || ""}
              placeholder="请输入任务名称"
              onChange={(e) => {
                assignTaskName.value = e.target.value;
              }}
            />
          </div>
        </div>
        <div className="flex items-start gap-6">
          <span className="w-17 text-right text-sm leading-normal text-slate-600">
            <span className="text-danger-2 mr-1 align-middle">*</span>
            <span>任务时间</span>
          </span>
          <AssignCard className="bg-fill-gray-2 flex-1 py-4 outline-none">
            <div className="text-gray-4 grid grid-cols-[4.5rem_1fr_1fr] gap-3 text-xs font-normal leading-none">
              <div className="col-span-1"></div>
              <div className="col-span-1">发布时间</div>
              <div className="col-span-1">要求完成时间</div>
            </div>
            {assignTimeRanges.value.map((cls, index) => (
              <div
                key={index}
                className="grid grid-cols-[4.5rem_1fr_1fr] items-start gap-3 py-2"
              >
                <div className="min-h-7.75 col-span-1 flex items-center text-sm font-normal leading-tight text-slate-600">
                  {cls.classInfo.name}
                </div>

                <div className="col-span-1">
                  {/* <Button
                    variant="outline"
                    className="flex h-12 w-full items-center justify-between bg-white"
                  >
                    <span>立即发布</span>
                    <Calendar className="h-5 w-5 text-gray-400" />
                  </Button> */}
                  <div className="h-7.75">
                    <AssignDatePicker
                      className="w-full"
                      isStart={true}
                      timeRange={cls}
                      timeRangeIndex={index}
                      onDateTimeChange={onDateTimeChange}
                    />
                  </div>
                  {invalidTimeRanges.value.includes(cls) && (
                    <div className="text-danger-2 mt-1 text-xs leading-normal">
                      要求完成时间不允许早于或等于发布时间
                    </div>
                  )}
                </div>

                <div className="col-span-1">
                  <div className="h-7.75">
                    <AssignDatePicker
                      className="w-full"
                      isStart={false}
                      timeRange={cls}
                      timeRangeIndex={index}
                      recommendEndTime={recommendEndTime(cls)}
                      onDateTimeChange={onDateTimeChange}
                    />
                  </div>
                </div>
              </div>
            ))}
          </AssignCard>
        </div>
      </AssignCard>
    </div>
  );
}
