"use client";
import { AssignHeading } from "@/components/assign/assign-heading";
import { AssignTargets } from "@/components/assign/assign-targets";
import { AssignCard } from "@/components/assign/assign-card";
import { useContext, useEffect, useRef } from "react";

import {
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { TaskContentContainer } from "./task-content-container";
import { AssignHomeworkContext } from "../store";
import { TaskContentEmpty } from "./task-content-empty";

export default function StepSelectTarget() {
  const stepOneRef = useRef<HTMLDivElement>(null);
  const stepTwoTitleRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const treeScrollTop = useSignal(0);
  const stepOneHeight = useSignal(0);
  const stepTwoMaxHeight = useSignal(0);
  const containerHeight = useSignal(0);
  let timer: string | number | NodeJS.Timeout | null | undefined = null;

  const { historySelectionList } = useContext(AssignHomeworkContext);

  useEffect(() => {
    if (stepOneRef.current && containerRef.current && stepTwoTitleRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const stepOneRect = stepOneRef.current.getBoundingClientRect();
      const stepTwoTitleRect = stepTwoTitleRef.current.getBoundingClientRect();

      containerHeight.value = containerRect.height;
      stepOneHeight.value = stepOneRect.height;
      stepTwoMaxHeight.value = containerHeight.value - stepTwoTitleRect.height;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    stepOneRef.current,
    stepTwoTitleRef.current,
    containerRef.current,
    containerHeight,
    stepOneHeight,
    stepTwoMaxHeight,
  ]);
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      const target = e.target as HTMLDivElement;
      treeScrollTop.value = target.scrollTop;
    }, 100);
  };

  useSignalEffect(() => {
    if (treeScrollTop.value > 0) {
      containerRef.current?.scrollTo({
        top: containerRef.current?.scrollHeight,
        behavior: "smooth",
      });
      return;
    }
    containerRef.current?.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  });

  return (
    <div
      className="flex h-full flex-1 flex-col overflow-y-auto pl-6 pr-4"
      ref={containerRef}
    >
      <div className="pb-6 pt-2" ref={stepOneRef}>
        <AssignHeading content="1. 选择布置对象" className="mb-4" />
        <AssignTargets assignRecommendTime={1} />
      </div>

      <div className="pb-4 pt-2" ref={stepTwoTitleRef}>
        <AssignHeading content="2. 选择任务内容"  />
      </div>

      <div
        className="flex h-full flex-1 items-stretch gap-2"
        style={{ height: stepTwoMaxHeight.value + "px" }}
      >
        {historySelectionList.value.length > 0 ? (
          <AssignCard
            className="w-full overflow-y-auto"
            style={{ maxHeight: stepTwoMaxHeight.value + "px" }}
            onScroll={handleScroll}
        >
            <TaskContentContainer />
          </AssignCard>
        ) : (
          <TaskContentEmpty className="w-full flex-1" />
        )}
      </div>
    </div>
  );
}
