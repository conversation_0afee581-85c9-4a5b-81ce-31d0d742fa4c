import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/ui/accordion";
import { cn } from "@/utils/utils";
import { Key, useContext } from "react";
import { AssignHomeworkContext } from "../store";
import { useComputed } from "@preact-signals/safe-react";
import { BizTreeDetailNode, BizTreeListItem } from "@/types/assign";
import { ChevronDownIcon } from "lucide-react";
import { TreeListAccordionContent } from "./tree-list-accordion-content";
import { DataNode } from "antd/es/tree";

interface TreeListAccordionProps {
  style?: React.CSSProperties;
  className?: string;
}

export function TreeListAccordion({
  style = {},
  className = "",
}: TreeListAccordionProps) {
  const {
    treeId,
    bizTreeList,
    knowledgeTreeId,
    knowledgeTreeList,
    resourcePublicCategory,
    bizTreeNodeId,
    bizTreeNode,
    knowledgeTreeNodeId,
    knowledgeTreeNode,
    treeSearchKeyword
  } = useContext(AssignHomeworkContext);
  const isChapter = useComputed(() => {
    return resourcePublicCategory.value === "chapter";
  });
  const isKnowledge = useComputed(() => {
    return resourcePublicCategory.value === "knowledge";
  });
  const treeList = useComputed(() => {
    let list: BizTreeListItem[] = [];
    if (isChapter.value) {
      list = bizTreeList.value;
    } else if (isKnowledge.value) {
      list = knowledgeTreeList.value;
    }
    return list;
  });
  const treeNodeId = useComputed(() => {
    if (isChapter.value) {
      return bizTreeNodeId.value || 0;
    } else if (isKnowledge.value) {
      return knowledgeTreeNodeId.value || 0;
    }
    return 0;
  });
  const treeDefaultExpandedKeys = useComputed(() => {
    if (isChapter.value) {
      return bizTreeNode.value?.parentIds || [];
    } else if (isKnowledge.value) {
      return knowledgeTreeNode.value?.parentIds || [];
    }
    return [];
  });
  const accordionDefaultValue = useComputed(() => {
    if (isChapter.value) {
      return String(treeId.value);
    } else if (isKnowledge.value) {
      return String(knowledgeTreeId.value);
    }
  });
  const onSelectTreeNode = (
    keys: Key[],
    { selectedNodes }: { selectedNodes: DataNode[] }
  ) => {
    const id = Number(keys[0]);
    const node = selectedNodes[0] as unknown as BizTreeDetailNode;
    if (isChapter.value) {
      bizTreeNodeId.value = id;
      bizTreeNode.value = node;
    } else if (isKnowledge.value) {
      knowledgeTreeNodeId.value = id;
      knowledgeTreeNode.value = node;
    }
  };
  const onAccordionChange = (value: string) => {
    if (!value) return;
    if (isChapter.value) {
      treeId.value = Number(value);
    } else if (isKnowledge.value) {
      knowledgeTreeId.value = Number(value);
    }
  };

  return (
    <Accordion
      key={accordionDefaultValue.value}
      type="single"
      collapsible
      className="w-full select-none space-y-2"
      onValueChange={onAccordionChange}
      defaultValue={accordionDefaultValue.value}
    >
      {treeList.value.map((item) => (
        <AccordionItem
          value={String(item.bizTreeId)}
          key={item.bizTreeId}
          className="border-none"
        >
          <AccordionTrigger className={cn(className)} style={style} asChild>
            <div
              className={cn(
                "py-2! hover:no-underline! group m-0 w-full cursor-pointer px-3 leading-normal",
                className
              )}
              style={style}
            >
              <div className="text-gray-1 flex-1 text-sm font-normal">
                {item.bizTreeName}
              </div>
              <ChevronDownIcon className="size-3.5! group-data-[state=open]:-rotate-0! translate-y-0.75 text-sm group-data-[state=closed]:-rotate-90" />
            </div>
          </AccordionTrigger>
          <AccordionContent className="gil-accordion-content py-1 pl-1.5">
            <TreeListAccordionContent
              treeItem={item}
              treeNodeId={treeNodeId.value}
              defaultExpandedKeys={treeDefaultExpandedKeys.value}
              onSelectTreeNode={onSelectTreeNode}
              search={treeSearchKeyword.value}
            />
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}
