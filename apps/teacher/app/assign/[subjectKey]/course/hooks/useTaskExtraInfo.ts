import { useCallback, useContext } from "react";
import { AssignCourseContext } from "../store";
import { batch } from "@preact-signals/safe-react";
import { AiCourse, CourseResourceItem, Practice } from "@/types/assign/course";

export interface TaskExtraInfo {
  getTaskExtraInfo: () => string;
  recoverContext: (taskExtraInfo: string) => void;
  recoverTreeInfoFromLocalStorage: (key: string) => void;
}

export function useTaskExtraInfo(): TaskExtraInfo {
  const {
    treeId,
    bizTreeNodeId,
    bizTreeNode,
    courseResourceList,
    courseTreeInfo,
    aiCourseList,
    practiceList,
  } = useContext(AssignCourseContext);

  const getTaskExtraInfo = useCallback(() => {
    console.log('保存的任务信息 ===> ', courseTreeInfo.value);
    return JSON.stringify(courseTreeInfo.value);
  }, [courseTreeInfo]);

  const recoverTreeInfo = useCallback((taskExtraInfo: string) => {
    if (!taskExtraInfo) {
      return;
    }
    const info = JSON.parse(taskExtraInfo);
    batch(() => {
      treeId.value = info.treeId;
      bizTreeNodeId.value = +info.bizTreeNodeId;  
      bizTreeNode.value = info.bizTreeNode;
    });
  }, [treeId, bizTreeNodeId, bizTreeNode]);

  const recoverResourceList = useCallback((taskExtraInfo: string) => {
    if (!taskExtraInfo) {
      return;
    }
    const info = JSON.parse(taskExtraInfo);
    const courses: AiCourse[] = [];
    const practices: Practice[] = [];
    info.courseResourceList.forEach((item: CourseResourceItem) => {
      if (item.aiCourse) {
        courses.push(item.aiCourse);
      }
      if (item.practice) {
        practices.push(item.practice);
      }
    });
    batch(() => {
      courseResourceList.value = info.courseResourceList;
      aiCourseList.value = courses;
      practiceList.value = practices;
    });
  }, [courseResourceList, aiCourseList, practiceList]);

  const recoverContext = useCallback((taskExtraInfo: string) => {
    recoverTreeInfo(taskExtraInfo);
    recoverResourceList(taskExtraInfo);
  }, [recoverTreeInfo, recoverResourceList]);

  const recoverTreeInfoFromLocalStorage = useCallback((key: string) => {
    const taskExtraInfo = localStorage.getItem(key);
    if (!taskExtraInfo) {
      return;
    }
    recoverTreeInfo(taskExtraInfo);
  }, [recoverTreeInfo]);


  return {
    getTaskExtraInfo,
    recoverContext,
    recoverTreeInfoFromLocalStorage,
  };
}
