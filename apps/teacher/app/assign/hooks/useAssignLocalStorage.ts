import { useCallback } from "react";
import { useApp } from "@/hooks";
import { UserSubjectItem } from "@/types/assign";

export interface AssignLocalStorage {
    getSubjectLocalStorageKey: () => string;
    getCurrentSubjectFromLocalStorage: () => UserSubjectItem | null;
    setCurrentSubjectToLocalStorage: (subject: UserSubjectItem) => void;
}

export function useAssignLocalStorage(): AssignLocalStorage {
  const { userInfo } = useApp();

  const getSubjectLocalStorageKey = useCallback(() => {
    return `assign-subject-${userInfo?.userID}`; 
  }, [userInfo?.userID]);

  const setCurrentSubjectToLocalStorage = useCallback((subject: UserSubjectItem) => {
    const key = getSubjectLocalStorageKey();
    localStorage.setItem(key, JSON.stringify(subject));
  }, [getSubjectLocalStorageKey]);

  const getCurrentSubjectFromLocalStorage = useCallback(() => {
    const key = getSubjectLocalStorageKey();
    const subject = localStorage.getItem(key);
    return subject ? JSON.parse(subject) : null;
  }, [getSubjectLocalStorageKey]);

  return {
    getSubjectLocalStorageKey,
    getCurrentSubjectFromLocalStorage,
    setCurrentSubjectToLocalStorage,
  };
}
