"use client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/ui/table";
import { cn } from "@/utils/utils";
import { Button } from "@/ui/tch-button";
import Image from "next/image";
import Avatar from "@/ui/tch-avatar";
import { useMemo } from "react";
import { useSessionStorageState } from "ahooks";
import IcExpand from "@/public/icons/ic_expand.svg";
import IcRetract from "@/public/icons/ic_retract.svg";
import GoldMedal from "@/public/icons/gold_medal.svg";
import SilverMedal from "@/public/icons/silver_medal.svg";
import BronzeMedal from "@/public/icons/bronze_medal.svg";
import EmptyDynamics from "./empty-dynamics";

import IcZan from "@/public/icons/ic_zan.svg";
import { GetBehaviorCategorysResponse, StudentInfo } from "@/types/course";
import { umeng, UmengCategory, UmengCourseAction } from "@/utils";
import { useApp } from "@/hooks";
import { useRequest } from "ahooks";
import { toast } from "@/ui/toast";
import { praiseStudent } from "@/services";

// 奖章组件
function Medal({ rank }: { rank: number }) {
  if (rank === 1)
    return (
      <Image src={GoldMedal} alt="金牌" className="mr-1 inline-block h-5 w-5" />
    );
  if (rank === 2)
    return (
      <Image
        src={SilverMedal}
        alt="银牌"
        className="mr-1 inline-block h-5 w-5"
      />
    );
  if (rank === 3)
    return (
      <Image
        src={BronzeMedal}
        alt="铜牌"
        className="mr-1 inline-block h-5 w-5"
      />
    );
  return null;
}

export function DynamicRankingPersonalTable({
  setStudent,
  studentLatestBehaviorData,
  subject,
  isSubjectTeacher,
  classroomId,
  onPraiseSuccess,
}: {
  setStudent: (student: StudentInfo) => void;
  studentLatestBehaviorData?: GetBehaviorCategorysResponse;
  subject: string;
  isSubjectTeacher: boolean;
  classroomId: string;
  onPraiseSuccess?: () => void;
}) {
  const { userInfo } = useApp();
  const [isExpanded, setIsExpanded] = useSessionStorageState(
    "dynamic-ranking-personal-table-is-expanded",
    {
      defaultValue: false,
    }
  );

  // 点赞请求
  const praiseStudentRequest = useRequest(
    (studentId: number) =>
      praiseStudent({
        classroomID: parseInt(classroomId),
        studentIDs: [studentId],
        teacherID: userInfo!.userID,
        teacherName: userInfo!.userName,
      }),
    {
      manual: true,
      debounceWait: 500,
      onSuccess: (data) => {
        toast.success(data.message);
        if (onPraiseSuccess) onPraiseSuccess();
      },
    }
  );

  const displayList = useMemo(() => {
    const learningScores = studentLatestBehaviorData?.learningScores ?? [];
    return learningScores.slice(0, isExpanded ? undefined : 4);
  }, [studentLatestBehaviorData?.learningScores, isExpanded]);

  return (
    <div
      className={
        cn(
          "relative rounded-t-[1.25rem] bg-white px-5 pb-6 pt-4",
          isExpanded && "pb-14"
        ) + " personal_ranking_table"
      }
    >
      {Boolean(displayList.length) && (
        <Table className="overflow-hidden rounded-t-lg">
          <TableHeader>
            <TableRow className="bg-purple-5 border-b-0! hover:bg-purple-5">
              <TableHead className="text-gray-2 h-9.25 w-1/4 pl-4 text-sm/normal font-normal">
                学生
              </TableHead>
              <TableHead className="text-gray-2 h-9.25 text-sm/normal font-normal">
                本课能量排名
              </TableHead>
              <TableHead className="text-gray-2 h-9.25 w-1/10 text-center text-sm/normal font-normal">
                鼓励他们
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="text-gray-1">
            {displayList.map((student, idx) => (
              <TableRow
                key={student.studentId}
                className="h-13 overflow-hidden border-[#e7ebe7] bg-white hover:bg-white"
              >
                <TableCell className="pl-4">
                  <div className="flex items-center gap-2.5">
                    <Avatar
                      src={student.avatarUrl}
                      alt="avatar"
                      className="h-6 w-6 active:opacity-80"
                      onClick={() => setStudent(student)}
                    />
                    {student.studentName}
                    <Medal rank={idx + 1} />
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <span>{`第 ${idx + 1} 名`}</span>
                    <span
                      className={cn(
                        `h-5.5 bg-primary-6 text-primary-1 ml-auto hidden items-center justify-center rounded-sm px-1.5 text-xs/normal font-normal`,
                        student.isHandled && "inline-flex"
                      )}
                    >
                      已点赞
                    </span>
                  </div>
                </TableCell>
                <TableCell className="text-center">
                  <Button
                    className="border-line-2 h-7 w-11 rounded-[1.125rem]"
                    isIconOnly
                    disabled={praiseStudentRequest.loading || student.isHandled}
                    onClick={() => {
                      if (student.isHandled) return;

                      praiseStudentRequest.run(student.studentId);

                      umeng.trackEvent(
                        UmengCategory.COURSE,
                        UmengCourseAction.CLASSROOM_REPORT_LIKE,
                        {
                          single_like_action: {
                            subject: subject,
                            job: isSubjectTeacher ? "任课教师" : "班主任",
                          },
                        }
                      );
                    }}
                  >
                    <Image src={IcZan} alt="点赞" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
      {studentLatestBehaviorData?.learningScores &&
        studentLatestBehaviorData.learningScores.length > 4 && (
          <div className="absolute bottom-0 left-0 right-0 flex h-16 items-center justify-center rounded-b-lg bg-[linear-gradient(0deg,_#FFF_-12.5%,_rgba(255,_255,_255,_0.00)_139.06%)]">
            <div
              className="h-6.25 border-line-3 bg-line-1 text-gray-2 flex cursor-pointer select-none items-center gap-2 rounded-2xl border-[1px] border-solid px-3 py-0.5 text-sm/normal active:opacity-80"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? `收起展示` : "展开查看全部学生状态"}
              <Image
                src={isExpanded ? IcRetract : IcExpand}
                alt="expand"
                className="cursor-pointer"
              />
            </div>
          </div>
        )}
      {!displayList.length && <EmptyDynamics />}
    </div>
  );
}
