import { cn } from "@/utils/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/select";
import { useComputed, batch } from "@preact-signals/safe-react";
import { AssignTree } from "./assign-tree";
import { DataNode } from "antd/es/tree";
import { Key, useContext } from "react";
import { AssignCourseContext } from "@/app/assign/[subjectKey]/course/store";
import { BizTreeDetailNode } from "@/types/assign";
import { umeng, UmengAssignAction, UmengCategory } from "@/utils";


interface AssignKnowlageSelectProps {
  style?: React.CSSProperties;
  className?: string;
}

export function AssignKnowlageSelect({
  style = {},
  className = "",
}: AssignKnowlageSelectProps) {
  const { bizTreeList, treeId, bizTreeDetail, bizTreeNodeId, bizTreeNode } =
    useContext(AssignCourseContext);
  const treeData = useComputed(() => {
    if (bizTreeDetail.value) {
      return bizTreeDetail.value.bizTreeDetail.bizTreeNodeChildren;
    }
    return [];
  });


  const handleTypeChange = (value: string) => {
    treeId.value = Number(value);
  };

  const onSelectTreeNode = (keys: Key[], { selectedNodes }: { selectedNodes: DataNode[] }) => {
    umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_COURSE_SETUP_CHAPTER_CLICK, {});
    batch(() => {
      bizTreeNodeId.value = Number(keys[0]);
      bizTreeNode.value = selectedNodes[0] as unknown as BizTreeDetailNode;
    });
  };
  
  
  return (
    <div className={cn("flex flex-col gap-3", className)} style={style}>
      <Select
        defaultValue={String(treeId.value)}
        onValueChange={handleTypeChange}
      >
        <SelectTrigger className="group rounded-md w-full bg-white text-gray-2 transition-colors hover:bg-[#f5f7fa]" classNames={{ icon: "text-gray-3 group-data-[state=open]:rotate-180" }}>
          <SelectValue placeholder="选择类型" />
        </SelectTrigger>
        <SelectContent>
          {bizTreeList.value.map((tree) => (
            <SelectItem key={tree.bizTreeId} value={String(tree.bizTreeId)}>
              {tree.bizTreeName}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <AssignTree
        className="mt-2 overflow-y-auto"
        style={{
          // maxHeight: "calc(100vh - 33 * var(--spacing))",
          overflowY: "auto",
          // backgroundColor: "red",
        }}
        treeData={treeData.value as unknown as DataNode[]}
        fieldNames={{
          title: "bizTreeNodeName",
          key: "bizTreeNodeId",
          children: "bizTreeNodeChildren",
        }}
        defaultExpandParent
        defaultExpandedKeys={bizTreeNode.value?.parentIds}
        defaultSelectedKeys={[bizTreeNodeId.value ?? 0]}
        onSelect={onSelectTreeNode}
      />
    </div>
  );
}
