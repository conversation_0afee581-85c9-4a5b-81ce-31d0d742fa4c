---
【Cursor开发中rules配置文件】前端版（适配TS+Vue/React场景）
type: "agent_requested"
---
### 通用规则
1. 默认情况下，所有回复都必须是中文，而且需要在开头称呼用户为"帅哥：" 
2. 复杂需求拆解成小任务，分步实现，每完成一个小任务后再继续  
3. 代码实现前后要仔细检查，确保类型定义完整、组件 props 正确  
4. 在已有功能基础上添加新功能时，必须确保：  
   - 不影响原有功能和组件复用性  
   - 不添加其他功能、代码、逻辑、文件、配置、依赖  
5. 遵循项目架构设计，保持代码风格与 TypeScript/ESLint 规范一致  
6. 组件设计遵循单一职责原则，不混合多个变更  
7. 在进行组件设计规划时，符合"第一性原理"  
8. 在代码实现时，符合"KISS原则"和"SOLID原则"  
9. 优先使用现有组件库和 hooks，避免重复代码  
10. 不引入不必要的依赖，优先使用项目已有库  
11. 确保代码可读性，复杂逻辑添加注释，组件 props 类型详细定义  
12. 代码变更范围最小化，避免修改公共组件、全局状态  
13. 实现后进行基本逻辑自检，确保状态管理与生命周期正确  
14. 如有疑问，先询问再修改，不要擅自改变组件 API 设计  
15. 遇到第三方库, 使用MCP: context7查询第三库的API
16. 使用 monorepo 方式管理项目，参考文档[../monorepo.mdc]

### 自动化执行与安全策略
17. 自动执行无需严格确认的操作，提高效率：  
   - 自动执行 TypeScript 类型检查、ESLint 验证  
   - 文件操作（创建组件、修改样式文件）无需额外确认  
   - 常规命令（如 pnpm install、启动开发服务器）可直接执行  
   - 打包和启动命令按照标识单独启动或打包某个模块，如：`tch、stu、aipt`
     - `pnpm run build -F={标识}`，如：`pnpm run build -F=tch`
     - `pnpm run dev -F={标识}`，如：`pnpm run dev -F=tch`
   - 涉及构建配置、路由修改等重要变更仍需确认  
18. 重要操作（修改全局状态、路由配置）应先保留副本  
19. 涉及 API 接口变更，优先修改 TypeScript 接口定义  
20. 执行影响较大的修改前，自动检测组件依赖关系，分析影响范围  

### 代码质量优化
21. 代码生成后，自动优化（移除未使用 imports、合并重复样式）  
22. 对可能影响性能的代码（如不必要的重渲染、大型循环）提供优化建议  
23. 确保异常处理和加载状态管理，防止白屏和渲染错误  

### 架构感知
24. 优先分析现有组件库与状态管理模式，避免创建冗余组件  
25. 添加功能时，优先考虑复用 hooks、contexts 或现有组件  
26. 如遇架构不清晰，先梳理组件层次与数据流，再执行修改  
27. 使用 MVVM 架构设计，具体见[../application-architecture.mdc]

### 代码变更的可追溯性
28. 提供清晰的 commit 信息，描述组件变更和影响范围  
29. 对于 UI 组件重大调整，生成变更文档与截图对比  
30. API 或 props 变更时，提供向下兼容方案或迁移指南  
31. 执行任务前，先分析项目结构和组件关系文档  
32. 每次修改后，生成任务总结，说明组件变更和状态管理调整  
33. 手动维护组件文档与设计系统说明，确保长期可维护性