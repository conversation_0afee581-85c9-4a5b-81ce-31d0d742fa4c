---
type: "agent_requested"
---

# 前端代码 Review 指南
> 基于实际开发/重构经验总结的实战指南


如果用户使用本指南进行代码 Review，请遵循以下规范：

## 🚨 Review 操作规范

### 专业度保持
- 你的专业度远超用户，如何用户的需求明显不合理或者有明显漏洞，你必须指出并给出建议
- 你的眼光非常挑剔毒辣，总是想找出用户需求中的致命缺陷，并给出建议
- 禁止你以任何方式应承敷衍用户，禁止做好好先生，完全允许你对用户提出最坦率、最尖锐的批评

### ⚠️ **严格禁止修改现有代码**
- Review 过程中**不得修改任何现有代码**
- 只能通过评论和建议的方式提供反馈
- 如需代码修改，必须由原开发者执行

### 📋 **Review 报告生成**
- 默认情况下**不主动生成 Review 报告文件**
- 只有在用户明确要求时才创建独立的 Review 报告
- Review 结果通过评分系统和检查清单体现

### 🎯 **评分驱动的流程控制**
- 每个检查项都有具体评分标准
- 综合评分达到 **85分及以上** 时，可执行生成提交拆分报告流程
- 评分低于85分时，必须先解决问题再进行后续操作

### 📝 **TODO 标注处理规则**
- 如果代码中包含 `TODO`、`FIXME`、`HACK` 等标注，需要识别并说明
- **TODO 相关的功能逻辑不计入评分**，但需要在问题描述中明确指出
- TODO 标注应包含：具体待完成内容、预期完成时间、负责人等信息
- 建议将 TODO 转化为具体的任务或 Issue 进行跟踪管理

## 🎯 架构规范参考

**在进行 Review 时，请参考以下架构规范文档：**
- 📋 **应用架构规范**：`.cursor/rules/application-architecture.mdc`
- 🏗️ **MVVM 分层架构**：支持直接MVVM、Context协调、混合模式三种调用链
- 🚫 **核心约束**：View 层严禁直接调用 Model 层（跨层调用）

### 核心判断原则
1. **复用性现实检查**：复用可能性 > 70% 才建议抽象
2. **业务完整性优先**：高度耦合的业务逻辑应该聚合，而不是拆分
3. **扩展性 vs 复杂性权衡**：优先配置化而不是过度抽象

## 📋 全面 Review 检查清单

### 🎯 变更必要性评估 (权重: 15分)
- [ ] **问题明确性** (4分)：这个变更解决了什么具体问题？
  - 优秀(4分): 问题定义清晰，有具体的错误或性能指标
  - 良好(3分): 问题基本明确，但缺少量化指标
  - 一般(2分): 问题描述模糊，主要基于主观判断
  - 差(0分): 没有明确的问题定义
- [ ] **收益评估** (4分)：变更后的收益是否合理？
  - 优秀(4分): 收益明确且具体（如：删除了50行重复代码、组件复用度提升、页面加载速度提升等）
  - 良好(3分): 收益明确但不够具体（如：提升代码可读性、简化维护、减少bug等）
  - 一般(2分): 收益存在但比较模糊（如：代码更整洁、结构更合理、更易扩展等）
  - 差(0分): 没有明确收益或收益微乎其微
- [ ] **影响范围** (4分)：变更是否会影响其他模块？
  - 优秀(4分): 影响范围明确，有完整的依赖分析
  - 良好(3分): 主要影响范围明确，部分依赖已分析
  - 一般(2分): 影响范围基本明确但分析不够深入
  - 差(0分): 没有考虑影响范围
- [ ] **变更时机** (3分)：现在是进行此变更的合适时机吗？
  - 优秀(3分): 时机选择合理，考虑了业务节奏和团队状况
  - 良好(2分): 时机基本合适，有一定考虑
  - 一般(1分): 时机选择一般，缺少充分考虑
  - 差(0分): 时机选择不当或没有考虑

### 🧠 业务逻辑合理性 (权重: 20分)
- [ ] **业务流程清晰** (5分)：业务逻辑是否符合实际需求和用户预期？
  - 优秀(5分): 业务流程清晰合理，完全符合产品需求和用户体验
  - 良好(4分): 业务流程基本合理，有少量可优化的地方
  - 一般(2分): 业务流程基本可用，但存在一些不合理的设计
  - 差(0分): 业务流程混乱，不符合实际需求
- [ ] **边界条件处理** (5分)：是否充分考虑了异常情况和边界条件？
  - 优秀(5分): 充分考虑各种边界条件，错误处理完善
  - 良好(4分): 考虑了主要边界条件，有基本的错误处理
  - 一般(2分): 考虑了部分边界条件，错误处理不够完善
  - 差(0分): 缺乏边界条件考虑，容易出现异常
- [ ] **状态管理合理** (5分)：组件/功能的状态设计是否合理？
  - 优秀(5分): 状态设计清晰，状态变化逻辑合理，无冗余状态
  - 良好(4分): 状态设计基本合理，有少量可优化的状态
  - 一般(2分): 状态设计一般，存在一些不必要的状态或逻辑混乱
  - 差(0分): 状态设计混乱，存在大量冗余或矛盾的状态
- [ ] **数据流向清晰** (5分)：数据在组件间的传递是否清晰合理？
  - 优秀(5分): 数据流向清晰，props/state 传递合理，无不必要的数据传递
  - 良好(4分): 数据流向基本清晰，有少量可优化的数据传递
  - 一般(2分): 数据流向一般，存在一些不合理的数据传递
  - 差(0分): 数据流向混乱，存在大量不必要或错误的数据传递

### 🔍 代码冗余与重复检查 (权重: 15分)
- [ ] **重复代码识别** (5分)：是否存在不必要的重复代码？
  - 优秀(5分): 无重复代码，或合理复用了公共逻辑
  - 良好(4分): 存在少量重复，但在可接受范围内
  - 一般(2分): 存在明显重复代码，需要重构优化
  - 差(0分): 大量重复代码，严重影响维护性
- [ ] **无用代码清理** (5分)：是否存在无用的变量、函数、导入等？
  - 优秀(5分): 代码整洁，无无用代码
  - 良好(4分): 基本整洁，有少量无用代码
  - 一般(2分): 存在一些无用代码，需要清理
  - 差(0分): 大量无用代码，影响代码可读性
- [ ] **逻辑简化** (5分)：是否存在可以简化的复杂逻辑？
  - 优秀(5分): 逻辑简洁明了，无不必要的复杂性
  - 良好(4分): 逻辑基本清晰，有少量可简化的地方
  - 一般(2分): 存在一些可以简化的复杂逻辑
  - 差(0分): 逻辑过度复杂，存在大量可简化的部分

### 🧩 组件设计合理性 (权重: 15分)
- [ ] **组件粒度适中** (5分)：组件的大小和职责是否合适？
  - 优秀(5分): 组件粒度合适，职责清晰，易于理解和维护
  - 良好(4分): 组件粒度基本合适，有少量可拆分或合并的情况
  - 一般(2分): 组件粒度不够合适，存在过大或过小的组件
  - 差(0分): 组件粒度不合理，严重影响可维护性
- [ ] **组件复用性** (5分)：组件是否具有良好的复用性？
  - 优秀(5分): 组件设计通用，易于复用，接口设计合理
  - 良好(4分): 组件有一定复用性，接口基本合理
  - 一般(2分): 组件复用性一般，存在一些耦合或特化
  - 差(0分): 组件高度特化，难以复用
- [ ] **组件层次合理** (5分)：组件的嵌套和层次结构是否合理？
  - 优秀(5分): 组件层次清晰，嵌套合理，符合业务逻辑
  - 良好(4分): 组件层次基本合理，有少量可优化的结构
  - 一般(2分): 组件层次一般，存在一些不合理的嵌套
  - 差(0分): 组件层次混乱，嵌套过深或不合理

### 🏗️ 架构分层检查 (权重: 15分)
- [ ] **调用链正确性** (4分)：是否遵循合理的架构调用链？
  - 优秀(4分): 调用链清晰合理，符合选择的架构模式
  - 良好(3分): 基本遵循架构模式，有少量可接受的例外
  - 一般(2分): 部分遵循架构模式，存在一些不当调用
  - 差(0分): 不遵循任何架构模式，存在严重违规
- [ ] **职责边界清晰** (4分)：每层是否只承担自己的核心职责？
  - 优秀(4分): 职责边界非常清晰，无职责混乱
  - 良好(3分): 职责边界基本清晰，有少量重叠
  - 一般(2分): 职责边界模糊，存在一定混乱
  - 差(0分): 职责边界不清，严重混乱
- [ ] **依赖方向正确** (4分)：是否存在跨层调用或反向依赖？
  - 优秀(4分): 依赖方向完全正确，无反向依赖
  - 良好(3分): 依赖方向基本正确，有少量可接受的例外
  - 一般(2分): 存在一些反向依赖但不严重
  - 差(0分): 存在严重的反向依赖问题
- [ ] **接口设计合理** (3分)：组件间接口是否简洁稳定？
  - 优秀(3分): 组件接口设计简洁稳定，易于使用和维护
  - 良好(2分): 接口设计基本合理，有少量改进空间
  - 一般(1分): 接口设计一般，存在一些问题
  - 差(0分): 接口设计不合理，难以使用

### ⚡ 性能与优化 (权重: 10分)
- [ ] **渲染性能** (3分)：是否避免了不必要的重渲染？
  - 优秀(3分): 合理使用memo、useMemo、useCallback等优化手段
  - 良好(2分): 基本避免了明显的性能问题
  - 一般(1分): 存在一些性能优化空间
  - 差(0分): 存在明显的性能问题
- [ ] **内存管理** (3分)：是否正确处理了内存泄漏风险？
  - 优秀(3分): 正确清理事件监听器、定时器、订阅等
  - 良好(2分): 基本处理了主要的内存泄漏风险
  - 一般(1分): 存在一些潜在的内存泄漏风险
  - 差(0分): 存在明显的内存泄漏问题
- [ ] **异步处理** (4分)：异步操作是否处理得当？
  - 优秀(4分): 正确处理加载状态、错误状态、竞态条件等
  - 良好(3分): 基本处理了异步操作的主要情况
  - 一般(2分): 异步处理不够完善，存在一些问题
  - 差(0分): 异步处理存在严重问题

### 🔧 代码质量检查 (权重: 10分)
- [ ] **命名规范** (3分)：变量、函数、组件命名是否清晰规范？
  - 优秀(3分): 命名非常清晰，见名知意，遵循团队规范
  - 良好(2分): 命名基本清晰，有少量改进空间
  - 一般(1分): 命名一般，存在一些不够清晰的命名
  - 差(0分): 命名混乱，难以理解
- [ ] **代码复杂度** (3分)：函数/组件的复杂度是否在可控范围内？
  - 优秀(3分): 函数简洁，单一职责，嵌套层级合理(≤3层)
  - 良好(2分): 复杂度基本可控，有少量复杂函数
  - 一般(1分): 存在一些复杂度较高的函数/组件
  - 差(0分): 普遍存在高复杂度的代码
- [ ] **类型安全** (2分)：TypeScript类型定义是否完整准确？
  - 优秀(2分): 类型定义完整，无any类型滥用
  - 良好(1分): 类型定义基本完整，有少量改进空间
  - 差(0分): 类型定义不完整或滥用any
- [ ] **文档同步** (2分)：是否同步更新了相关的 README 文档？
  - 优秀(2分): 所有相关文档都已同步更新，内容准确完整
  - 良好(1分): 大部分文档已更新，有少量遗漏
  - 差(0分): 未更新文档或更新内容不准确

## 🎯 架构违规检查要点

### 🚫 严重违规（必须修复）
- View 直接调用 Model（跨层调用）
- ViewModel 依赖 Context（反向依赖）
- Model 层包含业务逻辑

### ✅ 允许的架构模式
- **直接 MVVM**：View → ViewModel → Model
- **Context 协调**：View → Context → ViewModel → Model
- **混合模式**：View → (Context + ViewModel) → Model

**详细架构规范请参考：`.cursor/rules/application-architecture.mdc`**

## 🚨 关键决策点

### 1. **何时保持现状**
- 当前代码工作良好，没有明显问题
- 重构成本 > 预期收益
- 团队对当前实现已经熟悉
- 业务迭代频繁，不适合大改动

### 2. **何时进行重构**
- 扩展新功能时需要修改多个地方
- 代码重复率超过 30%
- 新人理解代码需要超过 1 天
- 出现明显的性能问题
- **发现架构违规模式**
- 存在大量业务逻辑不合理的情况
- 组件设计不合理，影响开发效率

### 3. **何时进行抽象**
- 确定有 3+ 个真实的复用场景
- 抽象的接口已经稳定
- 团队有足够的时间进行重构
- 抽象后能显著降低维护成本

### 4. **何时简化逻辑**
- 存在明显的重复代码(>3处相似逻辑)
- 函数/组件过于复杂(>50行或嵌套>3层)
- 状态管理混乱，难以追踪状态变化
- 数据流向不清晰，props传递层级过深

## 📊 综合评分计算

### 🎯 评分汇总
```markdown
## Review 评分表
### 变更必要性评估 (15分)
- 问题明确性: ___/4分
- 收益评估: ___/4分
- 影响范围: ___/4分
- 变更时机: ___/3分
**小计: ___/15分**

### 业务逻辑合理性 (20分)
- 业务流程清晰: ___/5分
- 边界条件处理: ___/5分
- 状态管理合理: ___/5分
- 数据流向清晰: ___/5分
**小计: ___/20分**

### 代码冗余与重复检查 (15分)
- 重复代码识别: ___/5分
- 无用代码清理: ___/5分
- 逻辑简化: ___/5分
**小计: ___/15分**

### 组件设计合理性 (15分)
- 组件粒度适中: ___/5分
- 组件复用性: ___/5分
- 组件层次合理: ___/5分
**小计: ___/15分**

### 架构分层检查 (15分)
- 调用链正确性: ___/4分
- 职责边界清晰: ___/4分
- 依赖方向正确: ___/4分
- 接口设计合理: ___/3分
**小计: ___/15分**

### 性能与优化 (10分)
- 渲染性能: ___/3分
- 内存管理: ___/3分
- 异步处理: ___/4分
**小计: ___/10分**

### 代码质量检查 (10分)
- 命名规范: ___/3分
- 代码复杂度: ___/3分
- 类型安全: ___/2分
- 文档同步: ___/2分
**小计: ___/10分**

## 🎯 综合评分: ___/100分

## 📋 评分标准与后续流程
- **90-100分**: 优秀 - 可直接执行提交拆分流程
- **85-89分**: 良好 - 可执行提交拆分，建议优化部分问题
- **70-84分**: 一般 - 需要解决主要问题后再进行提交拆分
- **60-69分**: 较差 - 必须解决关键问题，重新Review
- **<60分**: 不合格 - 需要重大修改，暂停后续流程

## ⚡ 流程控制与自动化执行
**当综合评分 ≥ 85分时，必须自动执行以下流程：**

### 🔄 自动执行提交拆分流程
1. **立即读取** `.cursor/rules/review/commit-split-guidelines.md` 文件内容
2. **基于当前代码变更** 按照拆分规范生成具体的提交拆分建议
3. **直接输出** 完整的提交拆分报告，包括：
   - 提交拆分策略
   - 每个提交的具体内容和说明
   - 提交顺序和依赖关系
   - 风险评估和注意事项

### 📋 执行要求
- **不需要用户额外指令**：Review 完成后如果评分达标，直接执行提交拆分
- **一次性完成**：在同一个回复中完成 Review 评分 + 提交拆分建议
- **输出完整报告**：提供可直接执行的提交拆分方案

### ⚠️ 执行条件
- 综合评分 ≥ 85分
- 无严重架构违规问题
- 代码变更影响范围明确

## 🔍 问题识别与描述要求
**评分只是辅助工具，每个层面发现的具体问题都必须详细描述：**

### 📝 问题描述模板
对于每个检查项，除了评分外，还需要提供：

1. **具体问题描述**：明确指出代码中存在的问题
2. **问题影响分析**：说明问题可能带来的后果
3. **改进建议**：提供具体的解决方案
4. **优秀实践识别**：指出代码中值得肯定的部分

### 📋 问题严重程度分级
- **🚨 严重问题**：违反架构原则，影响系统稳定性
- **⚠️ 重要问题**：影响代码质量，需要优先解决
- **💡 改进建议**：可以提升代码质量的优化点
- **✅ 优秀实践**：值得保持和推广的做法
- **📝 TODO 标注**：代码中的待完成项，不计入评分但需要说明

### 🏷️ TODO 标注识别与处理
在 Review 过程中需要识别以下标注：
```javascript
// TODO: 待完成的功能
// FIXME: 需要修复的问题
// HACK: 临时解决方案
// NOTE: 重要说明
// XXX: 需要注意的问题
```

**处理原则**：
- 识别并列出所有 TODO 相关标注
- 说明标注的具体内容和影响范围
- **不将 TODO 相关功能纳入评分考虑**
- 建议将 TODO 转化为具体任务进行跟踪

## 🎉 最佳实践总结

### ✅ 推荐做法
1. **业务逻辑优先**：先确保业务逻辑正确，再考虑技术优化
2. **组件设计合理**：保持组件粒度适中，职责清晰
3. **代码简洁明了**：避免不必要的复杂度和重复代码
4. **性能意识**：在开发过程中考虑性能影响
5. **文档同步更新**：代码变更时同步更新相关文档

### ❌ 避免做法
1. **过度工程**：避免为了技术而技术的复杂设计
2. **代码重复**：不要复制粘贴相似的代码逻辑
3. **组件职责混乱**：一个组件不应该承担过多职责
4. **忽略边界条件**：不考虑异常情况和错误处理
5. **性能忽视**：忽略明显的性能问题

## 🎯 核心价值观

**基于我们的开发/重构实践总结：**

> 最好的代码不是最复杂的代码，而是最适合当前业务需求且易于维护的代码。
> 
> Review的目标不是追求技术完美，而是确保代码质量和业务价值的平衡。
> 
> 组件的价值在于解决真实的业务问题，而不是展示技术能力。

**记住：代码是为业务服务的，Review是为了让代码更好地服务业务。**