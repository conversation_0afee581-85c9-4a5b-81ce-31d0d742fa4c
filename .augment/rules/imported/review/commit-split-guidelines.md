---
type: "manual"
---

# 前端提交拆分规范
> 基于实际开发经验制定的提交拆分标准
你需要主动查看分析当前未暂存和未提交的代码

## 🎯 核心原则

### 1. **单一职责原则**
每个提交只解决一个明确的问题或实现一个具体的功能。

### 2. **原子性原则**
每个提交都应该是完整的、可独立运行的变更，不依赖其他提交。

### 3. **可回滚原则**
每个提交都应该可以独立回滚，不影响其他功能的正常运行。

## 📋 提交类型分类

### 🔧 **fix**: 问题修复
**定义**：修复现有功能的bug或问题
**特征**：
- 解决具体的功能异常
- 不改变现有API或接口
- 不添加新功能

**示例**：
```bash
fix: 修复选择题选项无法点击问题
fix: 解决表单验证错误提示不显示的问题
fix: 修复移动端样式错位问题
```

### ✨ **feat**: 新功能
**定义**：添加新的功能或特性
**特征**：
- 增加新的用户可见功能
- 可能涉及新的API或组件
- 扩展现有功能的能力

**示例**：
```bash
feat: 新增题目解析展示组件
feat: 实现智能漏选逻辑
feat: 添加用户头像上传功能
```

### 🎨 **style**: 样式和格式
**定义**：仅限于不影响功能逻辑的纯视觉样式和代码格式调整
**特征**：
- 代码格式化、缩进调整、空格统一
- CSS样式属性调整（颜色、字体、间距等）
- 代码规范统一（如px转rem、类名规范）
- **严格不改变任何业务逻辑、条件渲染、状态管理**

**⚠️ 重要边界说明**：
- ✅ **纯样式**：`className="text-gray-400"` → `className="text-text-4"`
- ✅ **格式化**：代码缩进、空格、换行调整
- ✅ **规范统一**：px转rem、颜色变量统一
- ❌ **条件渲染**：`{condition && <Component />}` 的逻辑调整
- ❌ **状态相关**：基于state的UI显示控制
- ❌ **交互逻辑**：事件处理、用户交互流程

**示例**：
```bash
style: 统一px单位转rem规范
style: 优化颜色类名使用设计系统变量
style: 格式化代码缩进和空格
style: 统一按钮圆角和阴影样式
```

**❌ 错误使用示例**：
```bash
# 这些应该使用 feat 或 refactor
style: 基于 questionState 优化条件渲染逻辑  # 这是业务逻辑
style: 完善解析区域的显示控制           # 这是功能调整
style: 优化按钮交互状态和点击反馈        # 这是交互逻辑
```

### ♻️ **refactor**: 重构
**定义**：改进代码结构但不改变功能
**特征**：
- 优化代码结构和可读性
- 提取公共逻辑
- 重新组织代码架构
- 功能保持不变

**示例**：
```bash
refactor: 提取公共验证逻辑到工具函数
refactor: 重构组件状态管理结构
refactor: 优化API调用的错误处理逻辑
```

### 🔄 **improve**: 功能改进
**定义**：改进现有功能的用户体验或交互逻辑，不添加新功能
**特征**：
- 优化用户交互流程
- 改进UI状态管理和显示逻辑
- 基于状态的条件渲染优化
- 提升用户体验但不改变核心功能

**与其他类型的区别**：
- 与 `feat` 区别：不添加新功能，只改进现有功能
- 与 `fix` 区别：不是修复bug，而是主动改进
- 与 `style` 区别：涉及逻辑变更，不仅仅是视觉调整
- 与 `refactor` 区别：可能改变用户可感知的行为

**示例**：
```bash
improve(exercise): 优化题目状态显示和交互逻辑
improve(form): 改进表单验证反馈机制
improve(modal): 优化弹窗显示时机和动画效果
```

### 📝 **docs**: 文档更新
**定义**：只涉及文档的变更
**特征**：
- README更新
- 注释添加或修改
- API文档更新
- 不涉及代码逻辑

**示例**：
```bash
docs: 更新组件使用文档
docs: 添加API接口说明
docs: 完善项目部署指南
```

### 🧪 **test**: 测试相关
**定义**：添加或修改测试代码
**特征**：
- 单元测试添加
- 集成测试更新
- 测试用例修复
- 不影响生产代码

**示例**：
```bash
test: 添加选择题组件单元测试
test: 更新API接口集成测试
test: 修复测试用例中的断言错误
```

### 🔧 **chore**: 构建和工具
**定义**：构建过程、工具配置的变更
**特征**：
- 依赖包更新
- 构建脚本修改
- 配置文件调整
- 不影响源代码

**示例**：
```bash
chore: 升级React版本到18.2.0
chore: 更新ESLint配置规则
chore: 添加新的构建脚本
```

## 🎯 拆分决策流程

### 步骤1：变更分析
```markdown
## 变更内容分析
- [ ] 是否包含bug修复？
- [ ] 是否包含新功能添加？
- [ ] 是否包含功能改进？（交互优化、状态逻辑改进等）
- [ ] 是否包含纯样式/格式调整？
- [ ] 是否包含重构优化？
- [ ] 是否包含文档更新？
- [ ] 是否包含测试代码？
```

### 步骤2：依赖关系检查
```markdown
## 依赖关系评估
- [ ] 各部分变更是否相互依赖？
- [ ] 是否可以独立运行和测试？
- [ ] 是否可以独立回滚？
- [ ] 是否会影响其他功能？
```

### 步骤3：拆分策略选择

#### 🟢 **必须拆分**的情况
- 包含2种以上不同类型的变更
- 修复多个不相关的问题
- 同时包含功能开发和bug修复
- 包含大量样式调整和功能变更

#### 🟡 **建议拆分**的情况
- 单次提交文件数量 > 10个
- 单次提交代码行数 > 500行
- 涉及多个不同的业务模块
- 包含实验性功能和稳定功能

#### 🔴 **可以合并**的情况
- 同一个功能的相关文件
- 同一个bug的完整修复
- 同一套样式规范的统一调整
- 紧密相关的小型优化

## 📝 提交信息规范

### 格式标准
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 各部分说明

#### **type**: 提交类型
- `feat`: 新功能
- `fix`: 问题修复
- `improve`: 功能改进
- `style`: 样式格式
- `refactor`: 重构
- `docs`: 文档
- `test`: 测试
- `chore`: 构建工具

#### **scope**: 影响范围（可选）
- 组件名：`(ChoiceQuestion)`
- 模块名：`(exercise)`
- 功能名：`(validation)`

#### **subject**: 简短描述
- 使用中文描述
- 不超过50个字符
- 不以句号结尾
- 使用祈使语气

#### **body**: 详细描述（可选）
- 解释变更的原因和内容
- 可以包含多行
- 每行不超过72个字符

#### **footer**: 脚注信息（可选）
- 关联的Issue: `Closes #123`
- 破坏性变更: `BREAKING CHANGE:`
- 影响范围说明

### 提交信息示例

#### ✅ 好的提交信息
```bash
feat(exercise): 新增题目解析展示组件

- 实现 QuestionExplanation 组件用于解析内容展示
- 支持自定义标题和内容格式化
- 集成数学公式渲染功能
- 提供灵活的样式配置选项

Closes #456
```

```bash
fix(choice-question): 修复选项无法点击问题

- 修复 useEffect 依赖项导致的无限循环
- 将依赖项从整个对象改为具体方法
- 添加调试日志便于问题排查

Fixes #123
```

```bash
style: 统一单位规范 - px转rem

- 将所有硬编码px值转换为rem单位
- 优化颜色类名使用简洁写法
- 符合项目Tailwind CSS规范

影响文件: 8个组件文件
```

#### ❌ 不好的提交信息
```bash
# 太简单，没有说明具体内容
fix: 修复bug

# 混合多种类型，应该拆分
feat: 新增功能并修复样式问题

# 描述不清楚
update: 更新一些文件

# 使用英文但不规范
fix: fix the click issue
```

## 🎯 实际拆分示例

### 示例1：复杂功能开发
**原始变更**：实现用户管理功能
```
- 新增用户列表组件
- 修复用户头像显示问题  
- 优化表格样式
- 添加用户搜索功能
- 更新相关文档
```

**拆分方案**：
```bash
# Commit 1: 核心功能
feat(user): 新增用户列表和搜索功能
- 实现用户列表展示组件
- 添加用户搜索和筛选功能
- 集成分页和排序功能

# Commit 2: 问题修复
fix(user): 修复用户头像显示问题
- 解决头像URL为空时的显示异常
- 添加默认头像占位符
- 优化头像加载失败的处理

# Commit 3: 样式优化
style(user): 统一用户管理页面视觉样式
- 统一表格行高和间距数值
- 调整按钮颜色使用设计系统变量
- 优化字体大小和行间距规范

# Commit 4: 文档更新
docs(user): 更新用户管理功能文档
- 添加组件使用说明
- 更新API接口文档
- 完善功能特性说明
```

### 示例2：Bug修复和优化
**原始变更**：修复表单验证问题
```
- 修复必填字段验证失效
- 优化错误提示样式
- 重构验证逻辑
- 添加单元测试
```

**拆分方案**：
```bash
# Commit 1: 核心问题修复
fix(form): 修复必填字段验证失效问题
- 解决验证规则不生效的问题
- 修复异步验证的时序问题
- 确保所有必填字段都能正确验证

# Commit 2: 样式优化
style(form): 统一表单错误提示视觉样式
- 统一错误提示的颜色使用设计系统变量
- 调整提示信息的字体大小和间距数值
- 规范移动端和桌面端的样式一致性

# Commit 3: 代码重构
refactor(form): 重构表单验证逻辑
- 提取公共验证函数到utils
- 简化验证规则配置方式
- 提高代码可读性和可维护性

# Commit 4: 测试补充
test(form): 添加表单验证单元测试
- 覆盖所有验证规则的测试用例
- 添加异步验证的测试场景
- 确保测试覆盖率达到90%以上
```

## 🚨 常见错误和避免方法

### ❌ 常见错误

#### 1. **大杂烩提交**
```bash
# 错误示例
feat: 实现用户功能并修复样式问题还有文档更新
```
**问题**：混合了功能、修复、样式、文档多种类型
**解决**：按类型拆分为4个独立提交

#### 2. **依赖性提交**
```bash
# 错误示例 - 第二个提交依赖第一个
Commit 1: feat: 添加用户组件（但缺少必要的类型定义）
Commit 2: fix: 添加缺失的类型定义
```
**问题**：第一个提交无法独立运行
**解决**：确保每个提交都是完整可运行的

#### 3. **过度拆分**
```bash
# 错误示例 - 过度拆分
Commit 1: 添加用户名字段
Commit 2: 添加用户邮箱字段  
Commit 3: 添加用户电话字段
```
**问题**：拆分过细，失去了业务完整性
**解决**：相关字段应该在一个提交中完成

### ✅ 最佳实践

#### 1. **保持业务完整性**
```bash
# 正确示例
feat(user): 实现用户基本信息管理
- 添加用户姓名、邮箱、电话字段
- 实现信息的增删改查功能
- 添加字段验证规则
```

#### 2. **确保独立可运行**
```bash
# 正确示例 - 每个提交都完整
Commit 1: feat(api): 实现用户API接口
- 完整的CRUD接口实现
- 包含必要的类型定义
- 添加错误处理逻辑

Commit 2: feat(ui): 实现用户管理界面
- 基于已有API的完整UI实现
- 包含所有必要的组件和样式
- 可独立测试和运行
```

#### 3. **合理的拆分粒度**
```bash
# 正确示例 - 合理粒度
Commit 1: feat(auth): 实现用户认证功能
Commit 2: feat(profile): 实现用户资料管理
Commit 3: feat(settings): 实现用户设置功能
```

## 📋 拆分检查清单

### 提交前自检
```markdown
## 单个提交检查
- [ ] 是否只包含一种类型的变更？
- [ ] 提交信息是否清晰描述了变更内容？
- [ ] 是否可以独立运行和测试？
- [ ] 是否可以独立回滚而不影响其他功能？
- [ ] 文件数量是否合理（建议 < 10个）？
- [ ] 代码行数是否合理（建议 < 500行）？

## 多个提交检查
- [ ] 各提交之间是否相互独立？
- [ ] 是否按照逻辑顺序排列？
- [ ] 是否涵盖了所有必要的变更？
- [ ] 是否有遗漏或重复的内容？
```

### 代码审查检查
```markdown
## 审查者检查清单
- [ ] 每个提交的目的是否明确？
- [ ] 提交拆分是否合理？
- [ ] 是否存在应该合并的提交？
- [ ] 是否存在应该拆分的提交？
- [ ] 提交顺序是否合理？
- [ ] 提交信息是否符合规范？
```

## 🎯 团队协作建议

### 1. **统一标准**
- 团队成员都应遵循相同的拆分规范
- 定期review和优化拆分标准
- 在代码审查中强化拆分质量

### 2. **工具支持**
- 使用commitizen等工具规范提交信息
- 配置git hooks检查提交格式
- 使用IDE插件辅助提交拆分

### 3. **持续改进**
- 定期回顾提交质量
- 收集团队反馈优化规范
- 分享最佳实践案例

## 📋 拆分报告输出规范

### 🎯 必须包含的详细信息

当生成提交拆分报告时，必须包含以下详细信息：

#### 1. **文件级别的变更分析**
对于每个被修改的文件，必须说明：
```markdown
### 文件变更详情
**文件路径**: `apps/stu/app/contexts/question-context.tsx`
**变更类型**: 重构 (refactor)
**变更内容**:
- 新增 submitCurrentAnswer 协调方法 (第46-57行)
- 优化 Context 值的 useMemo 依赖项 (第75-83行)
- 移除已注释的向后兼容代码 (第101-104行)
**影响范围**: Context 层状态协调逻辑
**所属提交**: Commit 3 - 重构题目状态管理架构
```

#### 2. **跨文件变更的关联分析**
当多个文件的变更属于同一个提交时，必须说明：
```markdown
### 关联变更分析
**提交**: refactor(question): 重构题目状态管理架构
**关联文件**:
1. `apps/stu/app/contexts/question-context.tsx` - Context 层协调逻辑
2. `apps/stu/app/viewmodels/exercise/question-viewmodel.ts` - ViewModel 业务逻辑
3. `apps/stu/app/viewmodels/exercise/choice-question-viewmodel.ts` - 选择题特定逻辑

**关联原因**: 这些文件共同构成了题目状态管理的完整架构重构，必须在同一个提交中完成以保证功能完整性。
```

#### 3. **单个文件多提交拆分说明**
当一个文件的变更需要拆分到多个提交时，必须详细说明：
```markdown
### 单文件多提交拆分
**文件**: `apps/stu/app/views/exercise/question-view.tsx`

**Commit 3 部分** (refactor):
- 第29-41行: 使用 Context 替代直接 props 传递
- 第108-118行: 移除 feedback 相关的状态管理逻辑

**Commit 4 部分** (style):
- 第85行: 颜色类名优化 `!text-orange-600` → `!text-orange-text`
- 第85行: 背景类名优化 `!bg-orange-100` → `!bg-orange-2`
- 第106-120行: 基于 questionState 的条件渲染逻辑优化

**拆分原因**: 架构重构和样式优化属于不同类型的变更，应该分别提交以便独立回滚。
```

#### 4. **提交间的依赖关系图**
必须提供清晰的依赖关系说明：
```markdown
### 提交依赖关系
```
Commit 1 (docs) ──┐
                  ├─→ 可并行执行
Commit 2 (feat) ──┘

Commit 3 (refactor) ──→ Commit 4 (style)
                         ↑
                    必须按顺序执行
```

**依赖说明**:
- Commit 1, 2 无依赖，可以任意顺序或并行
- Commit 4 依赖 Commit 3 的架构重构结果
- Commit 5 独立，可在任意时机执行
```

#### 5. **每个提交的完整性验证**
对每个提交必须说明如何验证其完整性：
```markdown
### 提交完整性验证
**Commit 3**: refactor(question): 重构题目状态管理架构
**验证方法**:
1. 编译检查: `npm run build` 应该无错误
2. 类型检查: `npm run type-check` 应该通过
3. 功能测试: 题目答题流程应该正常工作
4. 状态管理: questionState 状态流转应该正确

**预期结果**: 功能保持不变，但状态管理更加统一和清晰
```

#### 6. **风险评估和回滚策略**
必须为每个提交提供具体的风险评估：
```markdown
### 风险评估详情
**Commit 3**: 🟡 中等风险
**风险点**:
- Context 和 ViewModel 的协调逻辑变更可能影响状态同步
- questionState 状态流转逻辑变更可能影响 UI 显示

**回滚策略**:
- 独立回滚: 可以单独回滚此提交
- 连带回滚: 如果回滚此提交，建议同时回滚 Commit 4
- 测试重点: 重点测试题目状态切换和按钮显示逻辑

**回滚命令**: `git revert <commit-hash>`
```

### 🎯 输出格式要求

#### 必须使用的模板结构：
```markdown
# 🔄 详细提交拆分报告

## 📊 变更概览
[总体变更统计和分类]

## 📝 详细拆分方案

### Commit 1: [类型](范围): [简短描述]
**提交信息**:
```bash
[完整的提交信息格式]
```

**包含文件及变更详情**:
- `文件路径1`: [具体变更内容和行号范围]
- `文件路径2`: [具体变更内容和行号范围]

**完整性验证**: [如何验证此提交的完整性]
**风险评估**: [风险等级和具体风险点]

[重复以上格式为每个提交]

## 🔗 提交依赖关系
[依赖关系图和详细说明]

## ⚠️ 特殊情况处理
[单文件多提交、跨文件关联等特殊情况的详细说明]

## 🧪 测试和验证策略
[整体测试策略和每个提交的验证方法]
```

### 🚨 强制要求

1. **文件变更必须具体到行号范围**
2. **每个提交必须说明完整性验证方法**
3. **必须分析单文件的多提交拆分情况**
4. **必须提供具体的风险评估和回滚策略**
5. **必须说明提交间的依赖关系**
6. **必须提供可执行的验证命令**

## 🎉 总结

好的提交拆分应该：
- **目的明确**：每个提交解决一个具体问题
- **独立完整**：可以独立运行、测试、回滚
- **逻辑清晰**：提交顺序和内容都有明确逻辑
- **便于维护**：有助于代码审查和问题定位
- **详细可追溯**：每个变更都有具体的文件和行号说明

记住：**好的提交历史是项目最宝贵的文档**，它记录了项目的演进过程，帮助团队更好地理解和维护代码。

**AI 生成拆分报告时，必须严格按照上述输出规范提供详细信息，确保每个提交的变更内容、影响范围、验证方法都清晰明确。**

## 📚 实际案例反思

### 案例：题目界面优化的提交类型选择

**错误示例**：
```bash
style(exercise): 优化题目界面样式和交互逻辑
- 基于 questionState 优化条件渲染逻辑  # ❌ 这是业务逻辑
- 完善解析区域的显示控制           # ❌ 这是功能改进
- 统一颜色类名规范               # ✅ 这才是样式
```

**正确拆分**：
```bash
# 纯样式变更
style(exercise): 统一题目界面颜色类名规范
- 统一颜色类名：gray-400 → text-4, orange-600 → orange-text
- 符合设计系统变量使用规范

# 功能改进变更
improve(exercise): 优化题目状态显示和交互逻辑
- 基于 questionState 优化条件渲染逻辑
- 完善解析区域的显示控制和时机
- 改进按钮状态切换的用户反馈
```

### 🎯 关键判断原则

1. **问自己：这个变更是否改变了用户可感知的行为？**
   - 是 → 不是 `style`，考虑 `improve`、`feat` 或 `fix`
   - 否 → 可能是 `style`

2. **问自己：这个变更是否涉及条件判断或状态管理？**
   - 是 → 不是 `style`，是业务逻辑变更
   - 否 → 可能是 `style`

3. **问自己：如果回滚这个变更，功能是否会受影响？**
   - 是 → 不是 `style`
   - 否 → 可能是 `style`

### 💡 最佳实践建议

- **宁可保守，不要激进**：不确定时优先选择 `improve` 而不是 `style`
- **从用户角度思考**：用户能感知到的变化通常不是纯样式
- **查看代码逻辑**：包含 `if`、`&&`、`?:` 等逻辑判断的通常不是样式
- **考虑测试影响**：需要更新业务逻辑测试的变更不是样式

记住：**准确的提交类型比简洁的提交历史更重要**，它帮助团队更好地理解变更的性质和影响范围。