---
type: "agent_requested"
---

# 应用架构文档

## 1. 应用 (Application)

  - 每个应用是一个可独立部署的单元, 拥有独立的运行环境和部署流程。
  - 应用聚焦于某一类业务场景, 承担特定的业务目标。例如: 学生端应用、教师端应用、AI生产工具应用等。
  - 应用之间通过 API 或共享包（例如 `@repo/core`, `@repo/lib`）进行通信与复用, 保持高内聚、低耦合。
  - 每个应用拥有独立的配置、依赖和资源管理, 便于独立开发、测试和上线。

## 2. 页面 (Page)

  - 应用由一个或多个页面组成, 每个页面对应唯一的 URL 路径。
  - 页面是用户与应用交互的入口单元, 承载具体的业务流程。
  - 页面负责组织和调度业务视图 (Business Views) 或业务组件, 处理页面级的路由、权限、布局、配置等逻辑。
  - 页面结构清晰, 便于利用 Next.js 的 App Router 进行按需加载和性能优化。
  - 页面组件通常直接位于 `app/` 目录下各路由段中 (e.g., `app/dashboard/settings/page.tsx`), 符合 Next.js 的 App Router 规范。

## 3. 业务视图 (Business View)

  - 页面可以通过使用单个业务视图或组合多个业务视图来实现具体的业务功能。
  - 业务视图是构成页面的主要功能模块，封装了特定业务场景的完整展现和交互逻辑。
  - 业务视图通常位于 `app/views/` 目录下，例如 `app/views/user-management/user-list-view.tsx`。
  - 业务视图采用 **MVVM (Model-View-ViewModel)** 模式进行设计和实现，旨在清晰地分离关注点，提升代码的可维护性和可测试性。
  - 对于复杂的状态管理场景，可以使用 **MVVM + Context 混合模式**，结合两种架构的优势。

### 架构模式选择

业务视图根据复杂度和场景选择合适的架构模式：

#### 3个核心判断问题
1. **业务完整性**：这些状态是否属于同一个业务流程？
2. **状态复杂度**：是否涉及复杂的状态管理和数据转换？
3. **组件层级**：状态需要跨越多少层组件？

#### 场景驱动的架构分级体系

摒弃固定比例分配，采用基于业务场景的动态架构选择：

**🏗️ 架构分级（由简到繁）**

##### 级别1：简单交互（Simple Interactions）
- **适用场景**：按钮点击、表单输入、模态框开关
- **特征**：局部状态、自包含逻辑、无需共享
- **实现**：`useState`、`useReducer`

##### 级别2：业务逻辑（Business Logic）
- **适用场景**：表单处理、搜索功能、数据转换
- **特征**：复杂业务逻辑、可复用、易测试
- **实现**：独立`ViewModel`（Custom Hooks）

##### 级别3：状态协调（State Coordination）
- **适用场景**：多组件协作、复杂业务流程、状态同步
- **特征**：跨组件状态管理、生命周期协调
- **实现**：`Context` + `ViewModel`协调

##### 级别4：全局状态（Global State）
- **适用场景**：用户状态、主题设置、应用级配置
- **特征**：应用范围状态、长生命周期
- **实现**：全局`Context`或状态管理库

**🎯 快速决策框架**

```
新功能开发 → 选择架构级别
    ↓
1. 状态复杂度？
    ├─ 简单 → 级别1：Simple Interactions
    └─ 复杂 → 继续
            ↓
2. 是否需要跨组件共享？
    ├─ 否 → 级别2：Business Logic
    └─ 是 → 继续
            ↓
3. 共享范围？
    ├─ 功能内 → 级别3：State Coordination
    └─ 全应用 → 级别4：Global State
```

> 详细的实现指南和代码模板请参考：[ViewModel & Context 层规范文档](./generate/gen-vm_context-guide.mdc)

#### 架构演进策略

**渐进式升级路径**：
- **级别1→2**：当组件状态逻辑变复杂时，提取为ViewModel
- **级别2→3**：当多个组件需要共享状态时，引入Context协调
- **级别3→4**：当需要全应用范围状态时，升级为全局管理

**反向简化原则**：
- Context只有一个消费者 → 降级为ViewModel
- 复杂状态只在单组件使用 → 降级为局部状态
- 过度抽象影响可读性 → 简化为更直接的实现

> 详细的架构模式、代码模板、最佳实践请参考：[ViewModel & Context 层规范文档](./generate/gen-vm_context-guide.mdc)

### MVVM 模式详解

MVVM 模式将业务视图划分为三个核心部分：

  - **Model (数据模型层):**

      - **核心职责:**
          - 数据获取、转换、校验、缓存管理
          - 封装 SWR Hooks，提供统一的数据服务接口
          - 后端返回数据用 Zod 校验，前端传参用 TS 类型
      - **核心约束:**
          - Model 层不感知 View 或 ViewModel 的存在
          - 严禁包含业务逻辑、UI 状态、路由操作
          - 使用统一的 `fetcher` 函数进行 API 调用
      - **位置:** `app/models` 目录或共享包的 `models` 目录

  - **View (视图层):**

      - **核心职责:**
          - 负责界面的声明式渲染和用户原始输入的捕获与转发。
          - 作为纯粹的UI呈现层，专注于"如何展示数据"和"如何将用户行为通知给ViewModel或Context"。
      - **核心约束:**
          - **严禁包含任何业务逻辑、业务相关的状态声明或直接的数据操作方法。** 所有业务处理必须委托给ViewModel或Context。
          - **严禁直接调用 Model 层**，必须通过 ViewModel 或 Context 进行数据访问。
          - 仅允许包含纯UI相关的瞬时状态（如动画控制、特定UI元素的展开/折叠状态等，且这类状态不应影响或依赖业务流程）和简单的事件处理函数。
          - 事件处理函数的主要职责是调用ViewModel或Context暴露的命令或方法，并传递必要的事件参数。
      - **允许的调用方式:**
          - ✅ **直接调用 ViewModel**：`const viewModel = useUserViewModel()`
          - ✅ **使用 Context**：`const context = useUserContext()`
          - ✅ **混合使用**：同时使用 Context（共享状态）和 ViewModel（独立逻辑）
          - ❌ **禁止直接调用 Model**：不能直接使用 `useUsers()` 等 Model hooks
      - **核心任务:**
          - 接收来自 ViewModel 或 Context 的数据并将其渲染到用户界面。
          - 将用户的交互事件转换为对 ViewModel 或 Context 命令的调用。
      - **位置:** 通常是 React 组件，位于 `app/components` (应用内通用纯UI组件) 或业务视图内部的 `components` 子目录 (特定于该业务视图的纯UI组件)。若为跨应用复用的UI组件，则位于 `@repo/ui/components`。

  - **ViewModel (视图模型层):**

      - **核心职责:**
          - 作为 Model 和 View 之间的桥梁，集中处理所有业务逻辑、表现逻辑和用户交互响应。
          - 从 Model 获取原始业务数据，并根据 View 的需求进行处理、聚合和格式化。
          - 管理与特定视图相关的所有业务状态和UI控制状态，并向 View 暴露这些状态和操作界面。
      - **核心约束:**
          - - ✅ 允许：调用Model层封装的自定义Hooks（如 useUsers(), useSubmitUserAnswer() 等）
          - ❌ 禁止：直接使用原始数据获取方法和Hooks：
            - ❌ 禁止：直接调用 fetch(), axios.get() 等HTTP客户端
            - ❌ 禁止：直接使用 useSWR(), useSWRMutation() 等原始数据获取Hooks
            - ❌ 禁止：绕过Model层直接访问API端点
          - 核心原则：所有数据操作必须通过Model层提供的封装接口进行
          - **严禁在ViewModel层包含任何形式的Mock数据或Mock逻辑。** 所有数据Mocking应遵循[.cursor/rules/generate/gen-model-guide.mdc] 中定义的策略。
          - **严禁直接操作DOM或引用View层组件实例。** 与View的交互应完全通过数据绑定和命令执行。
      - **核心任务:**
          - 调用 Model 层提供的接口获取业务数据。
          - 管理视图所需的状态 (e.g., 加载状态 `isLoading`, 错误状态 `error`, 用户输入 `searchTerm`, UI控制状态 `isModalOpen` 等)，使用 React 的状态管理机制（如 `useState`, `useReducer`）。
          - 对从Model层获取的数据进行转换、计算或组合，以生成View可以直接消费的数据形态。
          - 向 View 暴露经过处理的、用于渲染的数据和可供调用的操作方法/命令 (通常是稳定的回调函数，使用 `useCallback` 包裹)。
          - 处理 View 传递过来的用户交互事件，根据业务规则更新自身状态、调用 Model 层接口或执行其他业务操作。
      - **位置:** 通常是自定义 React Hooks (e.g., `useUserListViewModel`)，位于 `app/viewmodels` 目录。若为跨应用复用的 ViewModel，则位于相应共享包的 `viewmodels` 目录。

> 如果基于本规范设计架构（TDD）,必须将核心约束在架构文档中体现

### 架构模式示例


#### Model层 - 数据获取与转换
```typescript
// app/models/user-model.ts
export function useUsers() {
  const { data: users, error, isLoading, mutate } = useSWR(
    '/api/users',
    fetchAndTransformUsers
  );
  return { users, isLoading, error, mutateUsers: mutate };
}
```



#### Context 协调模式示例
```typescript
// Context层 - 协调多个 ViewModel
// app/contexts/homework-management-context.tsx
export function HomeworkManagementProvider({ children }) {
  const listViewModel = useHomeworkListViewModel();
  const filterViewModel = useHomeworkFilterViewModel();
  const detailViewModel = useHomeworkDetailViewModel();
  
  // Context 级别的协调逻辑
  const handleHomeworkSelect = useCallback((homework) => {
    detailViewModel.loadHomework(homework.id);
    listViewModel.markAsViewed(homework.id);
  }, [detailViewModel, listViewModel]);
  
  const contextValue = useMemo(() => ({
    listViewModel,
    filterViewModel,
    detailViewModel,
    handleHomeworkSelect
  }), [listViewModel, filterViewModel, detailViewModel, handleHomeworkSelect]);
  
  return (
    <HomeworkManagementContext.Provider value={contextValue}>
      {children}
    </HomeworkManagementContext.Provider>
  );
}

// View层 - 使用 Context 协调的状态
export function HomeworkManagementPage() {
  return (
    <HomeworkManagementProvider>
      <HomeworkFilters />        {/* 共享筛选状态 */}
      <HomeworkList />           {/* 共享列表数据 */}
      <HomeworkDetail />         {/* 共享选中状态 */}
      <HomeworkToolbar />        {/* 共享操作状态 */}
    </HomeworkManagementProvider>
  );
}
```

#### 独立 ViewModel 模式示例
```typescript
// ViewModel层 - 独立业务逻辑
// app/viewmodels/use-search-viewmodel.ts
export function useSearchViewModel() {
  const { searchUsers } = useSearchUsers(); // Model层
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const handleSearch = useCallback(async (term: string) => {
    if (!term.trim()) return;
    
    setIsLoading(true);
    try {
      const searchResults = await searchUsers(term);
      setResults(searchResults);
    } finally {
      setIsLoading(false);
    }
  }, [searchUsers]);

  return {
    searchTerm,
    results,
    isLoading,
    setSearchTerm,
    onSearch: handleSearch
  };
}

// View层 - 直接使用 ViewModel
export function SimpleSearchForm() {
  const { searchTerm, results, isLoading, setSearchTerm, onSearch } = useSearchViewModel();
  
  return (
    <form onSubmit={() => onSearch(searchTerm)} className="search-form">
      <input 
        value={searchTerm} 
        onChange={e => setSearchTerm(e.target.value)}
        placeholder="搜索用户..."
      />
      {isLoading ? <Spinner /> : <SearchResults results={results} />}
    </form>
  );
}
```



## 🏗️ Context + MVVM 架构优化

### 性能优化策略

#### 1. Context细粒度拆分策略
```typescript
// ❌ 避免：单一巨型Context
interface MegaContextValue {
  user: User;
  exercises: Exercise[];
  progress: Progress;
  ui: UIState;
}

// ✅ 推荐：按职责拆分多个Context
interface UserContextValue {
  user: User;
  updateUser: (data: Partial<User>) => void;
}

interface ExerciseContextValue {
  exercises: Exercise[];
  currentExercise: Exercise | null;
  submitAnswer: (answer: string) => void;
}
```

#### 2. 计算属性前置到ViewModel
```typescript
// ✅ 在ViewModel中预计算，避免Context消费者重复计算
export function useExerciseViewModel() {
  const [exercises, setExercises] = useState<Exercise[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  
  // 预计算的派生状态
  const derivedState = useMemo(() => ({
    currentExercise: exercises[currentIndex] || null,
    progress: exercises.length > 0 ? Math.round((currentIndex / exercises.length) * 100) : 0,
    isFirstExercise: currentIndex === 0,
    isLastExercise: currentIndex === exercises.length - 1
  }), [exercises, currentIndex]);
  
  return { ...derivedState, exercises };
}
```

#### 3. 稳定引用优化
```typescript
// ✅ 确保Context value的稳定性
export function ExerciseProvider({ children }: { children: React.ReactNode }) {
  const viewModel = useExerciseViewModel();
  
  const contextValue = useMemo(() => viewModel, [viewModel]);
  
  return (
    <ExerciseContext.Provider value={contextValue}>
      {children}
    </ExerciseContext.Provider>
  );
}
```

### 分层目录结构约定

```
app/
├── models/           # 数据模型 (SWR Hooks, Zod Schemas)
├── viewmodels/       # 视图模型 (独立业务逻辑)
├── contexts/         # 状态协调 (跨组件状态管理)
├── views/           # 业务视图 (MVVM 实现)
├── components/      # 纯 UI 组件
└── utils/           # 工具函数
```

**核心原则：Context 细粒度拆分，ViewModel 预计算优化**

  - **Context层:**
      - 应用内: `app/contexts/` (e.g., `app/contexts/homework-management-context.tsx`)
      - 共享包: `packages/[packageName]/contexts/`
      - **职责：** 协调多个 ViewModel，管理跨组件状态
  - **ViewModel层:**
      - 应用内: `app/viewmodels/` (e.g., `app/viewmodels/use-search-viewmodel.ts`)
      - 共享包: `packages/[packageName]/viewmodels/`
      - **职责：** 处理独立业务逻辑，简单功能实现
  - **Model层:**
      - 应用内: `app/models/` (e.g., `app/models/user-model.ts`)
      - 共享包: `packages/[packageName]/models/`
      - **职责：** 数据获取、转换、校验
  - **View层:**
      - 业务视图: `app/views/` (e.g., `app/views/homework-management/`)
      - 纯UI组件: `app/components/` (e.g., `app/components/user-card.tsx`)
      - 共享UI库: `@repo/ui/components/`

### 组件命名与文件命名

  - **组件/Hooks命名 (PascalCase):** `UserList`, `useUserListViewModel`, `useUsers`
  - **文件名 (kebab-case):** `user-list.tsx`, `use-user-list-viewmodel.ts`, `user-model.ts`

### 可复用性与可测试性

`model` 层请参考 [.cursor/rules/generate/gen-model-guide.mdc]

`viewmodel` 层请参考 [.cursor/rules/generate/gen-vm_context-guide.mdc]

`view` 层请参考 [.cursor/rules/generate/gen-view-guide.mdc]

  - **Mode Hooks (`useUsers`) 封装了所有数据获取、校验和转换逻辑，可被多个ViewModel复用。
      - 其内部的纯函数（如`transformRawUsersToTransformedUsers`）易于进行单元测试。
      - SWR Hooks本身可以通过Mock Service Worker (MSW) 或其他Mocking库进行集成测试。
  - **ViewModel层:**
      - 作为纯自定义Hooks (`useUserListViewModel`)，其业务逻辑可以通过Mock其依赖的Model层Hooks (`useUsers`)来进行单元测试。
      - 不包含直接的副作用（如API调用），使得测试更简单可靠。
  - **View层:**
      - 作为纯UI组件 (`UserList`, `UserListView`)，接收props并渲染，易于通过Storybook进行可视化展示和测试。
      - 可以使用React Testing Library (RTL) 进行交互测试，验证其是否正确调用ViewModel提供的回调。
  - 这种严格的分层和职责划分，极大地提高了代码的模块化程度、可理解性、可维护性和可测试性，从而提升整体开发质量和效率。



