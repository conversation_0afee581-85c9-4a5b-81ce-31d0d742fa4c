---
type: "agent_requested"
---

# API 层测试完整指南

以下是API层测试的完整指南，包含测试标准规范和工具链使用说明。

## 注意
- 必须使用 `pnpm` 命令运行测试，不要使用 `npm` 命令。
- 必须使用 `pnpm test:api` 命令运行测试，使用环境变量自动切换配置。
- 完成测试用例编写后必须使用下面运行测试指南中的命令运行测试，确保测试通过后才能提交代码。

## 🛠️ 工具链概览

API测试工具链基于Vitest，提供简单、高效的测试环境：

### 核心理念
- **智能配置**: 单一配置文件自动适配不同测试类型
- **纯Vitest**: 直接使用Vitest原生API，无需学习额外工具
- **即开即用**: 新项目只需复制标准配置即可

### 必需文件（仅3个）
- **智能配置**: `vitest.config.ts` - 自动检测测试类型的智能配置
- **环境Setup**: `vitest.setup.ts` - 仅在浏览器测试时生效
- **脚本配置**: `package.json` - 包含简单的测试脚本

## 🚀 快速开始

### 为新应用配置API测试

只需要复制3个文件即可：

#### 步骤1: 复制智能配置文件
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import path from 'path';

// 检测是否为API测试模式
const isApiTest = process.env.VITEST_API === 'true' || process.argv.includes('--api');

export default defineConfig({
  test: {
    globals: true,
    // API测试使用node环境，其他使用jsdom
    environment: isApiTest ? 'node' : 'jsdom',
    include: isApiTest 
      ? ['app/api/**/*.test.ts'] // API测试只包含API文件
      : [
          '**/__tests__/**/*.{js,ts,tsx}',
          '**/*.test.{js,ts,tsx}'
        ],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/.next/**',
      // 非API测试时排除API测试
      ...(isApiTest ? [] : ['**/app/api/**/*.test.{js,ts}'])
    ],
    // API测试不需要浏览器setup，其他需要
    setupFiles: isApiTest ? [] : ['./vitest.setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage'
    },
    testTimeout: 10000,
    hookTimeout: 5000
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './app'),
    }
  }
});
```

#### 步骤2: 添加Package.json脚本
```json
{
  "scripts": {
    "test": "vitest run",
    "test:watch": "vitest",
    "test:coverage": "vitest run --coverage",
    "test:api": "VITEST_API=true vitest run",
    "test:api:coverage": "VITEST_API=true vitest run --coverage",
    "test:api:watch": "VITEST_API=true vitest --watch"
  }
}
```

#### 步骤3: 复制Setup文件（如果需要）
```typescript
// vitest.setup.ts - 只在浏览器环境生效
import { vi } from 'vitest';

const isBrowserEnvironment = typeof window !== 'undefined';

if (isBrowserEnvironment) {
  // Mock Next.js router
  vi.mock('next/navigation', () => ({
    useRouter: () => ({
      push: vi.fn(),
      replace: vi.fn(),
      // ... 其他方法
    }),
  }));
  
  // 其他浏览器Mock...
}
```

### 运行API测试
```bash
# 在应用目录下（推荐）
pnpm test:api                    # 所有API测试
pnpm test:api:coverage          # 带覆盖率
pnpm test:api:watch             # 监听模式

# 从根目录
pnpm -F=<app> test:api
```

## 📁 目录结构

```
apps/<app-name>/
├── vitest.config.ts             # 智能配置（唯一配置文件）
├── vitest.setup.ts              # 浏览器环境Setup
├── package.json                 # 简单的测试脚本
└── app/api/
    ├── v1/
    │   └── <api-name>/
    │       └── __tests__/
    │           ├── route.test.ts      # 使用纯Vitest API
    │           └── integration.test.ts
    └── v2/
        └── <api-name>/
            ├── store.ts
            └── __tests__/
                ├── route.test.ts      # 使用纯Vitest API
                ├── store.test.ts
                └── integration.test.ts
```

## 🔧 智能配置原理

### 自动环境切换
```typescript
// 配置文件根据环境变量自动切换
const isApiTest = process.env.VITEST_API === 'true';

// API测试 -> Node.js环境，无setup
// 组件测试 -> jsdom环境，有setup
environment: isApiTest ? 'node' : 'jsdom',
setupFiles: isApiTest ? [] : ['./vitest.setup.ts'],
```

### 智能文件包含
```typescript
// API测试只运行API目录下的测试
// 组件测试排除API测试，避免冲突
include: isApiTest 
  ? ['app/api/**/*.test.ts'] 
  : ['**/__tests__/**/*.{js,ts,tsx}', '**/*.test.{js,ts,tsx}'],
exclude: [
  ...(isApiTest ? [] : ['**/app/api/**/*.test.{js,ts}'])
]
```

## 🧪 测试文件编写指南

### 必需测试文件
* `route.test.ts`: API路由基础功能测试。
* `integration.test.ts`: API集成测试 (如果需要测试多个API的协作)。

### 可选测试文件
* `middleware.test.ts`: 中间件测试 (如果API包含自定义中间件)。
* `validation.test.ts`: 请求参数校验测试 (如果有复杂的参数校验逻辑)。

### 文件命名规范
测试文件必须符合以下命名规范：
* ✅ `route.test.ts`
* ✅ `integration.test.ts`
* ✅ `middleware.test.ts`
* ✅ `validation.test.ts`

## 🎭 Mock 规范

### 🚨 Mock顺序规则（违反导致测试失败）
1. **vi.mock()** 必须在文件顶部，所有import之前
2. **import** 必须在所有vi.mock()之后
3. **禁止** 在测试用例内使用vi.doMock()

### 核心原则
1. **简化Mock**: 使用 `vi.mock('module-path', () => ({ default: vi.fn() }))`
2. **类型规避**: 使用 `(module as any).mockReturnValue()` 设置返回值
3. **全局清理**: 在 `beforeEach` 中调用 `vi.clearAllMocks()`
4. **避免引用**: Mock工厂函数内不引用外部变量

### API 测试模板（推荐）

```typescript
// ✅ 推荐的 API 测试写法
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { NextRequest } from 'next/server';

// Mock 外部依赖
vi.mock('../../../store', () => ({
  exerciseSessionStore: {
    createSession: vi.fn(),
    getSession: vi.fn(),
    submitAnswer: vi.fn(),
    getSessionProgress: vi.fn(),
    // ... 其他需要的方法
  }
}));

vi.mock('../../../../models/exercise-session/types', () => ({
  ExerciseType: {
    IN_CLASS: 'in_class',
    HOMEWORK: 'homework',
    PRACTICE: 'practice',
  },
  ExerciseSessionStatus: {
    NOT_STARTED: 'not_started',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    PAUSED: 'paused'
  }
}));

// 【重要】在所有 vi.mock 调用之后再 import 被测试的模块
import { GET } from '../route';

describe('API Route Tests', () => {
  let mockStore: any;

  beforeEach(async () => {
    vi.clearAllMocks();

    // 动态导入Mock模块
    const store = await import('../../../store');
    mockStore = store.exerciseSessionStore;
  });

  it('应该正确处理GET请求', async () => {
    // 设置Mock返回值
    mockStore.createSession.mockReturnValue({
      id: 'session_123',
      currentQuestionIndex: 0,
      total: 10
    });

    // 创建测试请求
    const request = new NextRequest('http://localhost:3000/api/v1/study_session/enter?learningType=2');

    // 执行API
    const response = await GET(request);
    const data = await response.json();

    // 断言结果
    expect(response.status).toBe(200);
    expect(data.code).toBe(0);
    expect(data.data).toBeDefined();
    expect(mockStore.createSession).toHaveBeenCalled();
  });

  it('应该正确处理错误情况', async () => {
    // 设置Mock抛出错误
    mockStore.createSession.mockImplementation(() => {
      throw new Error('Database error');
    });

    const request = new NextRequest('http://localhost:3000/api/v1/study_session/enter?learningType=2');

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.code).toBe(1);
    expect(data.message).toContain('Failed');
  });
});
```

## 📝 详细测试模板

### 路由测试模板（route.test.ts）
```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { NextRequest } from 'next/server';

// Mock依赖
vi.mock('../../store', () => ({
  submitAnswer: vi.fn(),
  getSession: vi.fn(),
}));

// Import被测试的路由
import { POST } from '../route';

describe('API Route Tests', () => {
  let mockStore: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    const store = await import('../../store');
    mockStore = {
      submitAnswer: store.submitAnswer,
      getSession: store.getSession,
    };
  });

  it('应该正确处理POST请求', async () => {
    // 设置Mock
    mockStore.submitAnswer.mockReturnValue({
      isCorrect: true,
      session: { id: 'test' }
    });

    // 创建请求
    const request = new NextRequest('http://localhost:3000/api/test', {
      method: 'POST',
      body: JSON.stringify({ data: 'test' }),
      headers: { 'Content-Type': 'application/json' }
    });

    // 执行API
    const response = await POST(request);
    const data = await response.json();

    // 断言
    expect(response.status).toBe(200);
    expect(data.code).toBe(0);
    expect(mockStore.submitAnswer).toHaveBeenCalled();
  });
});
```

### Store测试模板（store.test.ts）
```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { 
  createSession, 
  getSession, 
  submitAnswer 
} from '../store';

describe('Store Tests', () => {
  beforeEach(() => {
    // 清理状态
  });

  it('应该创建新会话', () => {
    const session = createSession(2001, 2);
    
    expect(session).toBeDefined();
    expect(session.courseId).toBe(2001);
    expect(session.learningType).toBe(2);
  });

  it('应该获取已创建的会话', () => {
    const session = createSession(2001, 2);
    const retrieved = getSession(session.studySessionId);
    
    expect(retrieved).toEqual(session);
  });
});
```

## 🔄 HTTP 请求测试规范

### 🚨 必须测试的场景
1. **正常请求流程**：正确的参数和预期的响应
2. **参数验证**：缺少必需参数、无效参数格式
3. **错误处理**：服务器错误、业务逻辑错误
4. **边界条件**：空值、极值、特殊字符

### ✅ HTTP 测试标准写法模板

```typescript
// 🎯 GET 请求测试
it('应该正确处理GET请求', async () => {
  const mockData = { success: true, data: 'result' };
  mockStore.getData.mockReturnValue(mockData);

  const request = new NextRequest('http://localhost:3000/api/endpoint?param=value');
  const response = await GET(request);
  const data = await response.json();

  expect(response.status).toBe(200);
  expect(data.code).toBe(0);
  expect(data.data).toEqual(mockData);
});

// 🎯 POST 请求测试
it('应该正确处理POST请求', async () => {
  const requestBody = { key: 'value' };
  const mockResponse = { success: true };
  mockStore.createData.mockReturnValue(mockResponse);

  const request = new NextRequest('http://localhost:3000/api/endpoint', {
    method: 'POST',
    body: JSON.stringify(requestBody),
    headers: { 'Content-Type': 'application/json' }
  });

  const response = await POST(request);
  const data = await response.json();

  expect(response.status).toBe(200);
  expect(data.code).toBe(0);
  expect(mockStore.createData).toHaveBeenCalledWith(requestBody);
});

// 🎯 参数验证测试
it('应该验证必需参数', async () => {
  const request = new NextRequest('http://localhost:3000/api/endpoint'); // 缺少参数

  const response = await GET(request);
  const data = await response.json();

  expect(response.status).toBe(400);
  expect(data.code).toBe(1);
  expect(data.message).toContain('Missing required');
});

// 🎯 错误处理测试
it('应该正确处理服务器错误', async () => {
  mockStore.getData.mockImplementation(() => {
    throw new Error('Internal server error');
  });

  const request = new NextRequest('http://localhost:3000/api/endpoint?param=value');
  const response = await GET(request);
  const data = await response.json();

  expect(response.status).toBe(500);
  expect(data.code).toBe(1);
  expect(data.message).toContain('Failed');
});
```

### ❌ 常见错误写法

```typescript
// ❌ 错误：没有Mock外部依赖
const response = await GET(request); // 可能调用真实的数据库

// ❌ 错误：没有验证HTTP状态码
expect(data.success).toBe(true); // 应该先检查response.status

// ❌ 错误：没有测试错误情况
// 只测试成功情况，忽略错误处理

// ❌ 错误：Mock设置不完整
mockStore.getData.mockReturnValue(undefined); // 应该返回完整的数据结构
```

## 🎯 标准测试模板（必须复制使用）

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { NextRequest } from 'next/server';

// ⚠️ 步骤1: 文件顶部Mock所有依赖
vi.mock('../../../store', () => ({
  exerciseSessionStore: {
    createSession: vi.fn(),
    getSession: vi.fn(),
    // ... 其他方法
  }
}));

// ⚠️ 步骤2: 所有Mock之后才import
import { GET, POST } from '../route';

describe('API Route Tests', () => {
  let mockStore: any;

  beforeEach(async () => {
    vi.clearAllMocks(); // ⚠️ 步骤3: 清理Mock状态

    const store = await import('../../../store');
    mockStore = store.exerciseSessionStore;
  });

  it('should handle requests correctly', async () => {
    // ⚠️ 步骤4: 设置Mock返回值
    mockStore.createSession.mockReturnValue({
      id: 'test_session',
      data: 'test_data'
    });

    const request = new NextRequest('http://localhost:3000/api/test');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.code).toBe(0);
    expect(mockStore.createSession).toHaveBeenCalled();
  });
});
```

## 🚨 常见测试错误预防清单

**重要：以下是开发过程中最容易出现的API测试问题，必须在编写测试时主动预防**

### 📋 问题类型与解决方案

| 错误类型 | 常见症状 | 根本原因 | 解决方案 |
|---|---|----|----|
| **Mock 依赖缺失** | API调用真实服务导致测试失败 | 没有Mock外部依赖 | Mock所有store、model、外部服务 |
| **HTTP状态码检查缺失** | 只检查响应数据不检查状态码 | 忽略HTTP协议规范 | 先检查 `response.status` 再检查数据 |
| **错误处理测试缺失** | 只测试成功情况 | 测试覆盖不完整 | 必须测试各种错误场景 |
| **参数验证测试缺失** | 生产环境参数错误导致崩溃 | 没有测试边界条件 | 测试缺少参数、无效参数等情况 |
| **异步操作处理错误** | 测试结果不稳定 | 没有正确等待异步操作 | 使用 `await` 等待所有异步操作 |

### 🔧 预防性检查清单

**编写API测试前必须检查：**
- [ ] 所有外部依赖是否已Mock？
- [ ] 是否测试了所有HTTP方法（GET、POST、PUT、DELETE）？
- [ ] 是否测试了参数验证逻辑？
- [ ] 是否测试了错误处理逻辑？
- [ ] 是否检查了HTTP状态码？
- [ ] 是否测试了边界条件？

## 🎯 测试覆盖率与质量要求

* **API路由**：100% 测试覆盖率。
* **参数验证**：100% 测试覆盖率。
* **错误处理**：100% 测试覆盖率。
* **边界条件**：必须覆盖空值、零值、无效值及极限值的处理逻辑。
* **HTTP状态码**：必须验证所有可能的状态码返回。

## 🚨 核心强制规范（必读）

**以下规范是最基本且最容易出错的地方，必须严格遵守，违反将导致测试无法被识别或产生预期外错误。**

1. **测试配置要求**：
   * **智能配置文件**：每个应用必须包含 `vitest.config.ts` 文件，支持环境变量切换
   * **环境自动切换**：API测试使用 `VITEST_API=true` 自动切换到Node.js环境
   * **正确示例**：
     ```typescript
     const isApiTest = process.env.VITEST_API === 'true';
     export default defineConfig({
       test: {
         environment: isApiTest ? 'node' : 'jsdom',
         setupFiles: isApiTest ? [] : ['./vitest.setup.ts'],
       }
     });
     ```

2. **测试文件命名与后缀**：
   * **强制要求**：所有测试文件必须使用 `.test.ts` 后缀，并放置在API目录下的 `__tests__/` 目录中。
   * **特定文件名**：
       * API路由基础功能测试：`route.test.ts`
       * API集成测试：`integration.test.ts`
       * 中间件测试：`middleware.test.ts`
       * 参数校验测试：`validation.test.ts`
   * **绝对禁止**：使用 `-test.js`、`.spec.ts` 或其他非规定格式及命名。
   * **正确示例**：`app/api/v1/study_session/enter/__tests__/route.test.ts`

3. **Mock 配置**：
   * **强制顺序**：`vi.mock()` 必须在文件顶部，所有 `import` 必须在 `vi.mock()` 之后。
   * **必用模板**：直接复制【Mock 规范】章节的完整模板，禁止自创Mock写法。
   * **类型规避**：使用 `(module as any).mockReturnValue()` 设置返回值。
   * **绝对禁止**：
       - 禁止在测试用例内使用 `vi.doMock()`
       - 禁止使用 `vi.mocked()` 进行类型转换
       - 禁止在 Mock 工厂函数内引用外部变量
       - 禁止不Mock外部依赖就直接测试API

4. **HTTP 请求测试**：
   * **强制使用 NextRequest**：必须使用 `NextRequest` 创建测试请求
   * **强制验证状态码**：必须先验证 `response.status` 再验证响应数据
   * **绝对禁止**：
       - 禁止不测试错误情况
       - 禁止不验证HTTP状态码
       - 禁止不测试参数验证逻辑
       - 禁止调用真实的外部服务

5. **测试失败处理**：
   * **立即修复**：任何测试失败都必须视为高优先级问题，并立即着手修复
   * **重新测试**：代码修复后，必须重新运行完整的相关测试套件
   * **确保通过**：必须确保所有测试 100% 通过后，才能提交代码

## 🏃‍♂️ 测试运行指南

### 运行测试命令

**必须且只能使用 pnpm**

#### 在具体包下执行测试（优先使用）：
```bash
# 使用环境变量自动切换配置（推荐）
pnpm test:api                     # 运行所有API测试
pnpm test:api:coverage           # 所有API测试+覆盖率
pnpm test:api:watch              # 监听模式

# 运行普通测试（非API）
pnpm test                        # 组件、Model等测试
pnpm test:coverage              # 组件测试+覆盖率
pnpm test:watch                 # 监听模式
```

#### 根目录 pnpm 命令执行：
```bash
# 测试指定应用的所有 API
pnpm -F=<app> test:api
# 运行覆盖率测试
pnpm -F=<app> test:api:coverage
```

### 配置检查命令
```bash
# 检查配置文件语法
npx tsc --noEmit vitest.config.ts

# 验证测试环境
node -e "console.log('Node.js:', process.version)"

# 检查vitest版本
npx vitest --version
```

### 故障排除
如果测试无法运行，按以下步骤检查：

1. **检查配置文件**
   ```bash
   # 确保存在智能配置文件
   ls -la vitest.config.ts

   # 检查配置语法
   npx tsc --noEmit vitest.config.ts
   ```

2. **检查依赖安装**
   ```bash
   # 重新安装依赖
   pnpm install

   # 检查vitest是否正确安装
   npx vitest --version
   ```

3. **检查测试文件**
   ```bash
   # 确保测试文件存在且命名正确
   find app/api -name "*.test.ts" -type f
   ```

4. **运行单个测试文件**
   ```bash
   # 直接运行单个测试文件进行调试
   VITEST_API=true npx vitest run app/api/v2/study_session/__tests__/route.test.ts
   ```

## 📋 最佳实践

### 1. 配置管理
- ✅ 一个应用只需要一个`vitest.config.ts`
- ✅ 使用环境变量切换，不要创建多个配置文件
- ✅ 保持配置简单，避免过度配置

### 2. 测试编写
- ✅ 使用纯Vitest API，无需学习额外工具
- ✅ 复制标准模板，确保一致性
- ✅ 遵循Mock顺序规则

### 3. 命令使用
- ✅ 优先使用`pnpm test:api`简短命令
- ✅ 使用环境变量而不是配置文件参数
- ✅ 开发时使用watch模式提高效率

### 4. 维护方式
- ✅ 配置更新时同步更新所有应用
- ✅ 保持测试模板最新
- ✅ 定期清理不需要的依赖

## 📊 测试报告模板

**重要：测试完成后，你必须直接输出 Markdown 格式的测试报告，使用以下模板：**

```markdown
# [API名称] API 测试报告

## 📊 测试统计

| 项目         | 数量   | 百分比    |
| :----- | :----- | :----- |
| 总测试数     | [数量] | 100%      |
| 通过         | [数量] | [百分比]% |
| 失败         | [数量] | [百分比]% |
| **测试状态** | **🎉 所有测试通过！ / ⚠️ 有测试失败** | -         |

## 📋 测试覆盖范围

| 测试类型         | 状态 | 具体内容 |
| :--- | :--- | :---- |
| 配置文件检查     | ✅/❌ | vitest.config.ts智能配置正确 |
| 环境配置验证     | ✅/❌ | 环境变量自动切换功能 |
| 路由功能测试     | ✅/❌ | GET/POST/PUT/DELETE |
| 参数验证测试     | ✅/❌ | 必需参数、格式验证 |
| 错误处理测试     | ✅/❌ | 4xx/5xx错误场景 |
| 边界条件测试     | ✅/❌ | 空值、极值、特殊字符 |
| 集成测试         | ✅/❌ | 多API协作场景 |

## 🔧 核心功能点测试详情

| API端点 | HTTP方法 | 功能描述 | 测试状态 |
| :--- | :---- | :---- | :---- |
| [路径]  | [方法]   | [描述]   | ✅/❌    |

---
**测试完成时间**: [YYYY-MM-DD HH:mm:ss]
**代码质量评估**: ✅ 符合 API 层标准规范 / ⚠️ 部分不符合
```

通过这套完整的指南，开发者可以用最少的配置获得最大的灵活性和效率。