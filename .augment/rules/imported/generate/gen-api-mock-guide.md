---
type: "agent_requested"
---

# API Mock 层代码编写指南

## 🎯 核心原则

1. **快速实现优先** - 能跑就行，不追求代码质量
2. **简单易懂** - 代码可读性 > 代码复杂度
3. **数据一致性** - 通过 store.ts 处理数据共享和状态管理
4. **格式统一** - 返回格式与后端 Go API 保持一致 `{ code, message, data }`
5. **工作流程**
   - 编写 Mock API 代码
   - 编写文档
   - 运行测试（必须使用指南 test-api-guide.mdc）
   - 修复测试（必须基于指南中的说明 test-api-guide.mdc）
   - 输出 API Mock 层自查清单

## 📂 目录结构

```
apps/(tch|stu|admin)/app/api/
├── v1/                         # API 版本目录
│   ├── study_session/          # 学习会话模块
│   │   ├── enter/
│   │   │   └── route.ts        # 进入会话 API
│   │   ├── submit_answer/
│   │   │   └── route.ts        # 提交答案 API
│   │   ├── next_question/
│   │   │   └── route.ts        # 获取下一题 API
│   │   └── exit/
│   │       └── route.ts        # 退出会话 API
│   └── users/                  # 用户模块
│       ├── route.ts            # /api/v1/users
│       ├── [id]/
│       │   └── route.ts        # /api/v1/users/[id]
│       └── store.ts            # 用户数据存储
├── courses/                    # 课程相关 API
│   ├── route.ts
│   └── store.ts
├── store.ts                    # 全局共享状态管理
└── README.md                   # API 层文档索引
```

### 目录结构规范
- **模块化组织** - 每个业务模块独立目录，便于维护和扩展
- **版本控制** - 使用 v1、v2 等版本目录管理 API 版本
- **RESTful 设计** - 遵循 REST API 设计规范
- **数据存储** - 每个模块可选择性创建 store.ts 进行数据管理
- **统一格式** - 所有 API 返回格式保持一致

## 🔄 标准模式

### 基础 Mock API
```typescript
// app/api/v1/users/route.ts
export async function GET() {
  // 模拟延迟（可选）
  await new Promise(resolve => setTimeout(resolve, 300));

  return Response.json({
    code: 0,
    message: 'Success',
    data: [
      { user_id: 1, user_name: 'John', status: 'active' },
      { user_id: 2, user_name: 'Jane', status: 'inactive' }
    ]
  });
}

export async function POST(request: Request) {
  const body = await request.json();

  // 简单的参数验证
  if (!body.user_name) {
    return Response.json({
      code: 1,
      message: 'Missing required parameter: user_name',
      data: null
    }, { status: 400 });
  }

  return Response.json({
    code: 0,
    message: 'User created',
    data: { user_id: Date.now(), ...body }
  });
}
```

### 带参数的 Mock API
```typescript
// app/api/v1/users/[id]/route.ts
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const userId = parseInt(params.id);

  // 简单的参数验证
  if (isNaN(userId) || userId <= 0) {
    return Response.json({
      code: 1,
      message: 'Invalid user ID',
      data: null
    }, { status: 400 });
  }

  // 简单的模拟逻辑
  if (userId > 100) {
    return Response.json({
      code: 1,
      message: 'User not found',
      data: null
    }, { status: 404 });
  }

  return Response.json({
    code: 0,
    message: 'Success',
    data: { user_id: userId, user_name: `User${userId}`, status: 'active' }
  });
}
```

### 带查询参数的 Mock API
```typescript
// app/api/v1/study_session/enter/route.ts
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const learningType = searchParams.get('learningType');
  const courseId = searchParams.get('courseId');

  // 参数验证
  if (!learningType) {
    return Response.json({
      code: 1,
      message: 'Missing required parameter: learningType',
      data: null
    }, { status: 400 });
  }

  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  return Response.json({
    code: 0,
    message: 'Success',
    data: {
      studySessionId: Date.now(),
      sessionStatus: 1,
      courseId: parseInt(courseId || '2001'),
      welcomeConfig: {
        welcomeTitle: '开始练习',
        welcomeMessage: '欢迎来到巩固练习！'
      }
    }
  });
}
```

## 📦 数据共享 (store.ts)

### 模块级数据存储
```typescript
// app/api/v1/users/store.ts
interface User {
  user_id: number;
  user_name: string;
  status: 'active' | 'inactive';
}

// 简单的内存存储
let users: User[] = [
  { user_id: 1, user_name: 'John', status: 'active' },
  { user_id: 2, user_name: 'Jane', status: 'inactive' }
];

let nextId = 3;

// 导出操作函数
export function getAllUsers() {
  return users;
}

export function getUserById(id: number) {
  return users.find(user => user.user_id === id);
}

export function createUser(userData: Omit<User, 'user_id'>) {
  const newUser = { ...userData, user_id: nextId++ };
  users.push(newUser);
  return newUser;
}

export function updateUser(id: number, updates: Partial<User>) {
  const index = users.findIndex(user => user.user_id === id);
  if (index !== -1) {
    users[index] = { ...users[index], ...updates };
    return users[index];
  }
  return null;
}

export function deleteUser(id: number) {
  const index = users.findIndex(user => user.user_id === id);
  if (index !== -1) {
    return users.splice(index, 1)[0];
  }
  return null;
}
```

### 使用 store 的 API
```typescript
// app/api/v1/users/route.ts
import { getAllUsers, createUser } from './store';

export async function GET() {
  return Response.json({
    code: 0,
    message: 'Success',
    data: getAllUsers()
  });
}

export async function POST(request: Request) {
  const userData = await request.json();

  // 简单验证
  if (!userData.user_name) {
    return Response.json({
      code: 1,
      message: 'Missing required field: user_name',
      data: null
    }, { status: 400 });
  }

  const newUser = createUser(userData);

  return Response.json({
    code: 0,
    message: 'User created',
    data: newUser
  });
}
```

## 🗄️ 状态持久化规范

### 核心原则：模拟真实后端状态管理

Mock API必须模拟真实后端的状态持久化，确保前端调用时获得与真实API一致的体验。

## 🌳 状态持久化决策树

### 第一步：是否需要store？

```
开始编写API
    ↓
是否有多个API端点？ ──No──→ 单个API，无需store
    ↓ Yes
是否需要共享数据？ ──No──→ 独立API，无需store
    ↓ Yes
是否有状态变化？ ──No──→ 静态数据，可选store
    ↓ Yes
需要创建store.ts ✅
```

### 第二步：哪些状态需要持久化？

```
识别到一个状态
    ↓
这个状态会跨请求使用吗？
    ↓ No → 请求级变量，无需持久化
    ↓ Yes
用户刷新页面后还需要这个状态吗？
    ↓ No → 会话级状态，可选持久化
    ↓ Yes
这个状态影响业务逻辑判断吗？
    ↓ No → 缓存数据，可选持久化
    ↓ Yes
必须持久化 ✅
```

### 第三步：持久化级别判断

```
确定需要持久化的状态
    ↓
状态丢失会导致功能异常吗？
    ↓ Yes → 🔴 核心状态（必须持久化）
    ↓ No
状态丢失会影响用户体验吗？
    ↓ Yes → 🟡 体验状态（建议持久化）
    ↓ No
状态丢失只是重新计算吗？
    ↓ Yes → 🟢 缓存状态（可选持久化）
```

## 📋 通用状态分类决策表

### 🔴 必须持久化（核心业务状态）

| 判断条件 | 示例场景 | 典型状态 |
|----------|----------|----------|
| 用户操作结果 | 提交表单、答题、支付 | 操作记录、结果状态 |
| 业务流程进度 | 多步骤流程、考试进度 | 当前步骤、完成状态 |
| 权限和身份 | 登录状态、角色权限 | 用户信息、权限标识 |
| 重要配置 | 用户设置、系统配置 | 配置项、偏好设置 |
| 关键计数器 | 尝试次数、积分、余额 | 计数值、累计数据 |

### 🟡 建议持久化（用户体验状态）

| 判断条件 | 示例场景 | 典型状态 |
|----------|----------|----------|
| 用户输入内容 | 草稿、临时数据 | 表单数据、编辑内容 |
| 界面状态 | 选中项、展开状态 | UI状态、视图模式 |
| 操作历史 | 浏览记录、搜索历史 | 历史记录、最近使用 |
| 性能优化 | 常用数据缓存 | 缓存数据、预加载 |

### 🟢 可选持久化（辅助状态）

| 判断条件 | 示例场景 | 典型状态 |
|----------|----------|----------|
| 统计信息 | 访问次数、使用统计 | 计数器、分析数据 |
| 临时标识 | 请求ID、临时token | 标识符、临时数据 |
| 计算结果 | 排序结果、过滤结果 | 计算值、派生数据 |

### 状态持久化判断规则

## 🔍 快速判断工具

### 状态持久化自检清单

对每个状态问自己以下问题：

#### 🔴 核心判断（必须回答）
- [ ] **跨请求使用**：这个状态会在多个API调用中使用吗？
- [ ] **业务关键性**：这个状态丢失会导致业务流程中断吗？
- [ ] **用户期望**：用户期望刷新页面后这个状态还在吗？
- [ ] **数据完整性**：这个状态是业务数据的一部分吗？

#### 🟡 体验判断（建议考虑）
- [ ] **操作连续性**：用户正在进行的操作需要这个状态吗？
- [ ] **性能优化**：保存这个状态能避免重复计算/请求吗？
- [ ] **错误恢复**：这个状态能帮助用户从错误中恢复吗？

#### 🟢 实用判断（可选考虑）
- [ ] **分析价值**：这个状态对后续分析有价值吗？
- [ ] **调试帮助**：这个状态能帮助调试问题吗？

### 判断结果指导

```
🔴 核心判断：4个都是Yes → 必须持久化
🔴 核心判断：3个Yes → 强烈建议持久化
🔴 核心判断：2个Yes → 建议持久化
🟡 体验判断：2个以上Yes → 可选持久化
🟢 实用判断：有Yes → 根据资源情况决定
全部No → 无需持久化
```

## 💡 决策树实际应用示例

### 示例1：用户登录状态

```
状态：currentUser = { id: 123, name: "张三", role: "student" }

🌳 决策过程：
1. 是否有多个API端点？ → Yes（登录、获取用户信息、权限检查）
2. 是否需要共享数据？ → Yes（多个API都需要用户信息）
3. 是否有状态变化？ → Yes（登录/登出状态变化）
   → 需要创建store.ts ✅

🔍 持久化判断：
- 跨请求使用？ → Yes（每个需要权限的API都要检查）
- 业务关键性？ → Yes（影响权限判断和数据访问）
- 用户期望？ → Yes（用户期望保持登录状态）
- 数据完整性？ → Yes（用户身份是核心业务数据）

结果：🔴 必须持久化
```

### 示例2：搜索关键词

```
状态：searchKeyword = "JavaScript教程"

🌳 决策过程：
1. 是否有多个API端点？ → No（只有搜索API使用）
   → 单个API，无需store

🔍 但如果有搜索历史功能：
- 跨请求使用？ → Yes（搜索历史API需要）
- 业务关键性？ → No（丢失不影响核心功能）
- 用户期望？ → Yes（用户期望看到搜索历史）
- 数据完整性？ → No（不是核心业务数据）

结果：🟡 建议持久化（用户体验）
```

### 示例3：当前页面滚动位置

```
状态：scrollPosition = 1250

🔍 持久化判断：
- 跨请求使用？ → No（只在当前页面使用）
- 业务关键性？ → No（不影响业务功能）
- 用户期望？ → Maybe（部分用户期望保持位置）
- 数据完整性？ → No（不是业务数据）

结果：🟢 无需持久化（或可选持久化）
```

### 示例4：表单草稿数据

```
状态：draftData = { title: "未完成的文章", content: "..." }

🔍 持久化判断：
- 跨请求使用？ → Yes（保存、预览API可能需要）
- 业务关键性？ → Maybe（丢失会让用户重新输入）
- 用户期望？ → Yes（用户期望草稿被保存）
- 数据完整性？ → Maybe（是用户创建的数据）

结果：🟡 建议持久化（防止数据丢失）
```

### 状态存储结构设计

#### 通用存储容器模板
```typescript
interface MockStore {
  // 🔴 核心业务状态（必须持久化）
  [entityName]: Map<string, EntityData>;       // 主要业务实体
  [entityName]Records: Map<string, Record[]>;  // 操作记录
  [entityName]Progress: Map<string, Progress>; // 进度状态

  // 🟡 体验优化状态（建议持久化）
  [feature]Cache: Map<string, CacheData>;      // 缓存数据
  [feature]History: Map<string, HistoryItem[]>; // 历史记录
  [feature]Drafts: Map<string, DraftData>;     // 草稿数据

  // 🟢 辅助状态（可选持久化）
  [feature]Stats: Map<string, StatData>;       // 统计数据
  [feature]Temp: Map<string, TempData>;        // 临时数据
}
```

## 🎯 业务流程模拟

### 完整状态持久化示例

```typescript
// app/api/v2/study_session/store.ts
interface StudySession {
  studySessionId: string;
  userId: string;
  courseId: string;
  sessionStatus: number;
  sessionType: number;
  currentQuestionIndex: number;
  totalQuestions: number;
  correctCount: number;
  streakCount: number;
  answerCount: number;
  startTime: number;
  lastAnswerTime?: number;
  completedAt?: number;
}

interface UserAnswerRecord {
  questionId: string;
  sessionId: string;
  userAnswer: string[];
  isCorrect: boolean;
  submittedAt: number;
  timeSpent: number;
  attemptCount: number; // 重试次数
}

interface SessionProgress {
  sessionId: string;
  currentProgress: number;
  totalEstimated: number;
  progressPercentage: number;
  currentPosition: number;
  correctCount: number;
  streakCount: number;
  timeSpent: number;
}

// 持久化存储容器
class MockStore {
  // 🔴 必须持久化 - 会话状态
  private sessions = new Map<string, StudySession>();

  // 🔴 必须持久化 - 用户答案记录
  private userAnswers = new Map<string, UserAnswerRecord[]>();

  // 🔴 必须持久化 - 进度状态
  private sessionProgress = new Map<string, SessionProgress>();

  // 🔴 必须持久化 - 题目数据
  private questions: Question[] = [];
  private questionIds: string[] = [];

  // 🟡 可选持久化 - 重试状态
  private retryAttempts = new Map<string, Map<string, number>>(); // sessionId -> questionId -> count

  // 🟡 可选持久化 - 用户配置
  private userConfigs = new Map<string, any>();

  // 会话管理
  createSession(userId: string, courseId: string, learningType: number): StudySession {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const session: StudySession = {
      studySessionId: sessionId,
      userId,
      courseId,
      sessionStatus: 1, // 练习中
      sessionType: 1,   // 正常练习
      currentQuestionIndex: 0,
      totalQuestions: this.questions.length,
      correctCount: 0,
      streakCount: 0,
      answerCount: 0,
      startTime: Date.now()
    };

    // 🔴 持久化会话状态
    this.sessions.set(sessionId, session);

    // 🔴 初始化答案记录
    this.userAnswers.set(sessionId, []);

    // 🔴 初始化进度状态
    this.sessionProgress.set(sessionId, {
      sessionId,
      currentProgress: 0,
      totalEstimated: this.questions.length,
      progressPercentage: 0,
      currentPosition: 1,
      correctCount: 0,
      streakCount: 0,
      timeSpent: 0
    });

    // 🟡 初始化重试记录
    this.retryAttempts.set(sessionId, new Map());

    return session;
  }

  submitAnswer(sessionId: string, questionId: string, userAnswer: string[], timeSpent: number) {
    // 🔴 获取持久化状态
    const session = this.sessions.get(sessionId);
    const answers = this.userAnswers.get(sessionId) || [];
    const progress = this.sessionProgress.get(sessionId);
    const retries = this.retryAttempts.get(sessionId) || new Map();

    if (!session || !progress) {
      throw new Error('Session not found');
    }

    // 检查答案正确性
    const isCorrect = this.checkAnswer(questionId, userAnswer);

    // 🟡 更新重试次数
    const currentAttempts = retries.get(questionId) || 0;
    retries.set(questionId, currentAttempts + 1);
    this.retryAttempts.set(sessionId, retries);

    // 🔴 记录答案（持久化）
    const answerRecord: UserAnswerRecord = {
      questionId,
      sessionId,
      userAnswer,
      isCorrect,
      submittedAt: Date.now(),
      timeSpent,
      attemptCount: currentAttempts + 1
    };
    answers.push(answerRecord);
    this.userAnswers.set(sessionId, answers);

    // 🔴 更新会话状态（持久化）
    if (isCorrect) {
      session.correctCount++;
      session.streakCount++;
      session.currentQuestionIndex++;
    } else {
      session.streakCount = 0;
      // 根据业务规则决定是否前进到下一题
    }
    session.answerCount++;
    session.lastAnswerTime = Date.now();
    this.sessions.set(sessionId, session);

    // 🔴 更新进度状态（持久化）
    progress.correctCount = session.correctCount;
    progress.streakCount = session.streakCount;
    progress.currentProgress = session.currentQuestionIndex;
    progress.progressPercentage = Math.round((session.currentQuestionIndex / session.totalQuestions) * 100);
    progress.timeSpent += timeSpent;
    this.sessionProgress.set(sessionId, progress);

    return {
      isCorrect,
      session,
      progress,
      nextQuestion: this.getQuestionByIndex(session.currentQuestionIndex),
      isCompleted: session.currentQuestionIndex >= session.totalQuestions
    };
  }

  // 🔴 状态恢复 - 模拟用户重新进入
  resumeSession(sessionId: string) {
    const session = this.sessions.get(sessionId);
    const answers = this.userAnswers.get(sessionId) || [];
    const progress = this.sessionProgress.get(sessionId);

    if (!session) return null;

    return {
      session,
      answers,
      progress,
      currentQuestion: this.getQuestionByIndex(session.currentQuestionIndex)
    };
  }
}

// 全局实例 - 模拟后端数据库
const mockStore = new MockStore();
```

## 📋 状态持久化检查清单

### 🔍 开发时自查

编写Mock API时，对每个状态问自己：

#### 状态分类检查
- [ ] **会话相关**：用户退出后重新进入，这个状态还需要吗？
- [ ] **操作记录**：需要查看历史记录或做统计分析吗？
- [ ] **业务进度**：影响后续API调用的判断逻辑吗？
- [ ] **配置数据**：多个API端点会共享这个数据吗？
- [ ] **临时状态**：只在单次请求中使用，还是跨请求保持？

#### 持久化实现检查
- [ ] 使用了Map或类似结构存储状态？
- [ ] 状态有唯一标识符（如sessionId, userId）？
- [ ] 实现了状态的增删改查操作？
- [ ] 考虑了状态的初始化和清理？
- [ ] 处理了状态不存在的异常情况？

#### 业务逻辑检查
- [ ] 状态变更遵循业务规则？
- [ ] 实现了状态之间的关联更新？
- [ ] 处理了并发访问的情况？
- [ ] 模拟了真实的状态转换流程？

### 🚨 常见遗漏状态

根据原有store分析，容易遗漏的状态：

#### 用户操作历史
```typescript
// ❌ 只记录当前状态
currentAnswer: string;

// ✅ 记录完整历史
answerHistory: Array<{
  questionId: string;
  answer: string;
  isCorrect: boolean;
  timestamp: number;
  attemptCount: number;
}>;
```

#### 重试和错误状态
```typescript
// ❌ 简单的对错判断
isCorrect: boolean;

// ✅ 完整的重试机制
retryState: {
  questionId: string;
  attemptCount: number;
  maxAttempts: number;
  canRetry: boolean;
  lastError?: string;
};
```

#### 时间和进度追踪
```typescript
// ❌ 只记录开始时间
startTime: number;

// ✅ 完整的时间追踪
timeTracking: {
  sessionStartTime: number;
  questionStartTime: number;
  totalTimeSpent: number;
  questionTimeSpent: number;
  pausedTime: number;
};
```

#### 会话状态管理
```typescript
// ❌ 简单的状态标识
status: 'active' | 'completed';

// ✅ 完整的状态机
sessionState: {
  status: 'not_started' | 'in_progress' | 'paused' | 'completed' | 'expired';
  canResume: boolean;
  lastActivity: number;
  stateHistory: Array<{
    from: string;
    to: string;
    timestamp: number;
    reason?: string;
  }>;
};
```

## 🔧 状态管理工具函数

```typescript
// 状态持久化基础类
class StateManager<T> {
  private store = new Map<string, T>();

  set(key: string, value: T): void {
    this.store.set(key, value);
  }

  get(key: string): T | null {
    return this.store.get(key) || null;
  }

  update(key: string, updates: Partial<T>): T | null {
    const current = this.get(key);
    if (!current) return null;

    const updated = { ...current, ...updates };
    this.set(key, updated);
    return updated;
  }

  delete(key: string): boolean {
    return this.store.delete(key);
  }

  exists(key: string): boolean {
    return this.store.has(key);
  }

  clear(): void {
    this.store.clear();
  }

  size(): number {
    return this.store.size;
  }
}

// 生成唯一ID
const generateId = (prefix: string = '') =>
  `${prefix}${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// 状态验证
function validateState<T>(state: T, requiredFields: (keyof T)[]): string | null {
  for (const field of requiredFields) {
    if (state[field] === undefined || state[field] === null) {
      return `Missing required field: ${String(field)}`;
    }
  }
  return null;
}

// 状态深拷贝（避免引用问题）
function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj));
}

// 状态变更日志
function logStateChange(entityType: string, entityId: string, action: string, changes?: any) {
  console.log(`[StateChange] ${entityType}:${entityId} - ${action}`, changes);
}
```

## 🚫 禁止操作

```typescript
// ❌ 过度复杂的逻辑
const complexValidation = (data) => {
  // 不要写复杂的校验逻辑
};

// ❌ 引入额外的库
import { z } from 'zod';
import { v4 as uuid } from 'uuid';

// ❌ 复杂的错误处理
try {
  // 复杂的异常处理
} catch (error) {
  // 详细的错误分类
}
```

## ✅ 推荐做法

```typescript
// ✅ 简单直接
export async function POST(request: Request) {
  const data = await request.json();

  // 简单的检查
  if (!data.name) {
    return Response.json({
      code: 1,
      message: 'Name is required',
      data: null
    }, { status: 400 });
  }

  // 直接返回
  return Response.json({
    code: 0,
    message: 'Success',
    data: { id: Date.now(), ...data }
  });
}
```

## 🚫 禁止操作

```typescript
// ❌ 过度复杂的逻辑
const complexValidation = (data) => {
  // 不要写复杂的校验逻辑
};

// ❌ 引入额外的库
import { z } from 'zod';
import { v4 as uuid } from 'uuid';

// ❌ 复杂的错误处理
try {
  // 复杂的异常处理
} catch (error) {
  // 详细的错误分类
}
```

## ✅ 推荐做法

```typescript
// ✅ 简单直接
export async function POST(request: Request) {
  const data = await request.json();
  
  // 简单的检查
  if (!data.name) {
    return Response.json({
      code: 400,
      message: 'Name is required',
      data: null
    });
  }
  
  // 直接返回
  return Response.json({
    code: 0,
    message: 'Success',
    data: { id: Date.now(), ...data }
  });
}
```

## ✅ 检查清单

### 基础功能检查
- [ ] 返回格式是否符合 `{ code, message, data }` ？
- [ ] 是否处理了必要的参数验证？
- [ ] 是否模拟了成功和失败场景？
- [ ] 逻辑够简单吗？（能跑就行）
- [ ] 是否添加了适当的延迟模拟？
- [ ] 错误状态码是否正确设置？
- [ ] 是否遵循了 RESTful API 设计规范？

### 🔴 状态持久化检查（重要）
- [ ] **决策树应用**：是否使用决策树判断了store的必要性？
- [ ] **状态分类**：是否对每个状态进行了🔴🟡🟢分类？
- [ ] **核心状态识别**：业务关键状态是否都被持久化？
- [ ] **跨请求状态**：多个API共享的状态是否正确管理？
- [ ] **状态关联**：相关状态是否同步更新？
- [ ] **状态恢复**：用户刷新/重进时状态是否正确恢复？
- [ ] **状态清理**：是否有适当的状态清理机制？
- [ ] **存储结构**：是否使用了合理的存储容器设计？

### 🟡 业务逻辑检查
- [ ] **状态转换**：业务状态流转是否符合真实场景？
- [ ] **数据一致性**：多个状态之间是否保持一致？
- [ ] **边界处理**：首题、末题、异常情况是否正确处理？
- [ ] **并发安全**：多次快速调用是否会导致状态错乱？

## 📊 API Mock 层自查清单

**重要：API Mock 层代码完成后，必须使用以下模板输出自查清单报告**

```markdown
# 📋 API Mock 层自查清单报告

## 🎯 核心原则检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 快速实现优先 | ✅ | 代码简单直接，能跑就行 |
| 简单易懂 | ✅ | 代码可读性良好，逻辑清晰 |
| 数据一致性 | ✅ | 使用 store.ts 管理共享数据 |
| 格式统一 | ✅ | 所有 API 返回 { code, message, data } 格式 |

## 🔴 状态持久化检查（关键）

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 决策树应用 | ✅ | 使用决策树正确判断store必要性 |
| 状态分类完整 | ✅ | 所有状态都进行了🔴🟡🟢分类 |
| 核心状态识别 | ✅ | 业务关键状态正确识别并持久化 |
| 跨请求状态管理 | ✅ | 多API共享状态正确管理 |
| 状态关联更新 | ✅ | 相关状态同步更新，保持一致性 |
| 状态恢复能力 | ✅ | 用户刷新/重进时状态正确恢复 |
| 状态清理机制 | ✅ | 适当的状态清理和内存管理 |
| 存储结构设计 | ✅ | 使用合理的存储容器设计 |

## 📝 API 实现检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 参数验证 | ✅ | 处理必要的参数验证和错误返回 |
| 成功场景 | ✅ | 正常情况下返回正确的数据格式 |
| 失败场景 | ✅ | 错误情况下返回合适的错误码和信息 |
| HTTP 状态码 | ✅ | 正确设置 HTTP 状态码 |
| 延迟模拟 | ✅ | 添加适当的延迟模拟真实网络请求 |

## 🎯 业务逻辑检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 状态转换流程 | ✅ | 业务状态流转符合真实场景 |
| 数据一致性 | ✅ | 多个状态之间保持一致 |
| 边界条件处理 | ✅ | 首题、末题、异常情况正确处理 |
| 并发安全性 | ✅ | 多次快速调用不会导致状态错乱 |

## 📊 代码质量检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 目录结构规范 | ✅ | 遵循项目目录结构规范 |
| 命名规范 | ✅ | 文件和函数命名清晰易懂 |
| 代码简洁性 | ✅ | 避免过度复杂的逻辑 |
| 状态管理设计 | ✅ | 合理的状态存储和管理架构 |

## 📚 文档完成检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| API 文档 | ✅ | 在 README.md 中记录 API 接口 |
| 状态说明 | ✅ | 说明哪些状态被持久化及原因 |
| 使用示例 | ✅ | 提供清晰的使用示例 |
| 错误处理说明 | ✅ | 说明各种错误情况的处理 |

---
**自查完成时间**: [时间]
**整体状态**: 🎉 所有检查项通过 / ⚠️ 存在问题需要修复

**状态持久化总结**:
- 决策树应用: [说明如何使用决策树判断store必要性]
- 状态分类结果: [🔴核心状态X个, 🟡体验状态Y个, 🟢辅助状态Z个]
- 存储结构: [简述存储容器设计，如Map<string, EntityData>]
- 关键业务逻辑: [说明重要的状态转换和关联更新逻辑]
- 状态恢复机制: [说明用户重进时如何恢复状态]
```

### 使用说明
- ✅ 表示已完成的项目
- ❌ 表示未完成或有问题的项目
- ⚠️ 表示需要注意的项目
- 🎉 表示全部完成

## 📚 文档化规范 (README.md)

### API 层 README 位置
- `app/api/README.md` - 应用内 API 层文档
- `packages/[package]/api/README.md` - 共享包 API 层文档

### README 内容规范

```markdown
# API Mock 层

## 可用 API 接口

| 接口路径 | 方法 | 描述 | 参数 | 返回格式 |
|---------|------|------|------|----------|
| `/api/v1/users` | GET | 获取用户列表 | 无 | `{ code, message, data: User[] }` |
| `/api/v1/users` | POST | 创建用户 | `{ user_name, status }` | `{ code, message, data: User }` |
| `/api/v1/users/[id]` | GET | 获取用户详情 | `id: string` | `{ code, message, data: User }` |

## 核心数据结构

- `User` - 用户数据类型
- `Session` - 会话数据类型
- `Question` - 题目数据类型

## Store 数据管理

- `userStore` - 用户数据管理
- `sessionStore` - 会话数据管理
```

### 维护要求
- **新增 API 时**：必须同步更新 README.md
- **开发前检查**：查阅 README.md 了解可复用接口
- **Code Review**：检查文档更新情况

## 🔧 命名规范

- **API 路径**：kebab-case (`/api/v1/study-session/enter`)
- **文件名**：kebab-case (`route.ts`, `store.ts`)
- **函数名**：camelCase (`createUser`, `getSession`)
- **接口名**：PascalCase (`User`, `SessionProgress`)

## 🧪 下一步操作

完成 API Mock 层代码编写后必须输出自查清单报告，并准备好进入测试流程

测试流程准备工作：
1. 必须主动说明你将使用 API 测试指南 test-api-guide.mdc 处理测试流程
2. 必须主动向用户询问是否需要进入测试流程，用户确认后你必须读取 test-api-guide.mdc 然后开始处理测试流程
3. 测试用例编写和运行测试方式都在测试指南中说明，你必须完全严格遵守
4. 有任何问题你可以终端任务并向用户提问
