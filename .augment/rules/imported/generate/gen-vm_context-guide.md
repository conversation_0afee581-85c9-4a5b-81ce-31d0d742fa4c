---
type: "agent_requested"
---

# ViewModel & Context 层规范文档

## 🎯 概述

ViewModel 和 Context 层是 MVVM 架构的业务逻辑层，负责业务逻辑处理、状态协调和跨组件通信。ViewModel 处理具体业务逻辑，Context 负责状态协调和共享。

**重要说明：数据转换由 Model 层的 transformer 处理，ViewModel 层不负责数据格式转换。**

## 📋 层级职责对比

| 职责类别 | Context 层 | ViewModel 层 |
|---------|------------|--------------|
| **使用场景** | 多组件协作业务 | 独立简单功能 |
| **作用范围** | 跨组件状态协调 | 单一业务功能模块 |
| **状态管理** | 共享业务状态 | 具体业务状态 |
| **生命周期** | 应用级或功能级 | 随组件创建/销毁 |
| **复用性** | 特定场景复用 | 高度可复用 |

## 🗺️ 架构决策流程

### 快速决策树

```
开始新功能开发
    ↓
是否涉及多个组件协作？
    ├─ 是 → 使用 Context 协调模式 ✅
    │        │
    │        ├─ 应用级状态？
    │        │   ├─ 是 → 全局 Context
    │        │   └─ 否 → 功能级 Context + ViewModel
    │        │
    │        └─ 实现: Context Provider + 多个 ViewModel
    │
    └─ 否 → 独立简单功能？
            ├─ 是 → 直接 ViewModel ✅
            │       └─ 实现: 单个 useXXXViewModel Hook
            │
            └─ 否 → 重新评估需求复杂度
```

### 判断标准

| 判断维度 | Context 协调模式 | 独立 ViewModel | 全局 Context |
|---------|-----------------|---------------|-------------|
| **组件数量** | 2+ 个相关组件 | 单个组件 | 整个应用 |
| **状态共享** | 需要跨组件共享 | 组件内状态 | 全局应用状态 |
| **典型场景** | 作业管理、课程编辑 | 表单处理、搜索 | 用户状态、主题 |

## 🔀 Context拆分决策框架

### 核心原则：80/20简化决策

**默认策略：不拆分** - 除非有明确的拆分理由，保持Context的完整性。

#### 快速判断路径（80%场景）

**唯一关键问题**：*"我是否在混合不同更新频率的状态？"*

```typescript
// ❌ 混合了不同更新频率 → 必须拆分
interface MixedContext {
  timer: number;           // 高频：每秒更新
  userProfile: User;       // 静态：几乎不变
  currentQuestion: Question; // 事务性：偶尔更新
}

// ✅ 按更新频率拆分
interface TimerContext {
  seconds: number;         // 高频状态独立
  isRunning: boolean;
}

interface ExerciseContext {
  userProfile: User;       // 静态+事务性状态聚合
  currentQuestion: Question;
  answers: Answer[];
}
```

**判断标准**：
- **YES（必须拆分）**：混合了高频状态与静态/事务性状态
- **NO（保持聚合）**：所有状态更新频率相似

#### 详细分析路径（20%复杂场景）

仅在以下情况使用完整的三步分析：
- 快速路径判断不清晰
- 性能关键的功能模块
- 重构遗留代码时有明确性能问题
- Context代表多个独立业务域

### 状态更新频率量化标准

#### 1. 高频状态 (High-Frequency State)
- **定义**：更新频率 > 1次/秒 或 绑定连续用户输入
- **典型来源**：
  - WebSocket实时数据
  - 定时器/动画状态
  - 鼠标移动、滚动事件
  - 频繁轮询数据（间隔<5秒）
- **规则**：**必须独立为单独Context** - 这是不可协商的性能要求

#### 2. 事务性状态 (Transactional State)
- **定义**：响应离散用户操作的状态变化
- **典型场景**：
  - 表单字段输入（带防抖）
  - 按钮点击、选择操作
  - API调用的loading/error状态
- **特征**：更新频率与用户行为直接相关

#### 3. 静态状态 (Static State)
- **定义**：每会话设置一次或变化极少的状态
- **典型例子**：
  - 用户认证信息
  - 权限配置、主题设置
  - 应用配置、特性开关
- **边界标准**：应用启动或登录时加载，下次会话前基本不变

### Context拆分监控工具

#### 开发时性能监控
```typescript
// utils/context-monitor.ts
import { useRef, useEffect } from 'react';

export function useContextUpdateMonitor(
  value: any, 
  contextName: string, 
  threshold: number = 5
) {
  const updateCount = useRef(0);
  const lastResetTime = useRef(Date.now());

  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;
    
    updateCount.current++;
    const now = Date.now();
    
    if (now - lastResetTime.current > 1000) {
      if (updateCount.current > threshold) {
        console.warn(
          `⚠️ Context "${contextName}" 高频更新: ${updateCount.current} 次/秒. 考虑拆分高频状态.`
        );
      }
      updateCount.current = 0;
      lastResetTime.current = now;
    }
  }, [value, contextName, threshold]);
}

// 在Provider中使用
function MyContextProvider({ children }) {
  const [value, setValue] = useState(...);
  
  useContextUpdateMonitor(value, 'MyContext', 5);
  
  return <MyContext.Provider value={value}>{children}</MyContext.Provider>;
}
```

#### 性能诊断工具链
1. **React DevTools Profiler** - 主要诊断工具
2. **@welldone-software/why-did-you-render** - 实时检测不必要重渲染
3. **自定义监控Hook** - 开发时更新频率告警

## 🏗️ Context 层规范

### ✅ 核心职责

#### 1. 跨组件状态协调

```typescript
// ✅ 练习会话 Context - 协调多个 ViewModel
export interface ExerciseSessionContextValue {
  // 协调的 ViewModels
  exerciseViewModel: ExerciseViewModel;
  progressViewModel: ProgressViewModel;
  timerViewModel: TimerViewModel;
  
  // 跨组件状态
  sessionStatus: 'idle' | 'active' | 'paused' | 'completed';
  totalTimeSpent: number;
  
  // 协调操作
  handleAnswerSubmit: (answer: string) => Promise<void>;
  handleSessionComplete: () => void;
  handleSessionPause: () => void;
}

export function ExerciseSessionProvider({ children }: { children: React.ReactNode }) {
  // 协调多个 ViewModel
  const exerciseViewModel = useExerciseViewModel();
  const progressViewModel = useProgressViewModel();
  const timerViewModel = useTimerViewModel();
  
  // Context 特有的协调状态
  const [sessionStatus, setSessionStatus] = useState<'idle' | 'active' | 'paused' | 'completed'>('idle');
  
  // 跨 ViewModel 的协调逻辑
  const handleAnswerSubmit = useCallback(async (answer: string) => {
    // 协调多个 ViewModel 的操作
    const result = await exerciseViewModel.submitAnswer(answer);
    progressViewModel.updateProgress(result);
    
    if (result.isSessionComplete) {
      setSessionStatus('completed');
      timerViewModel.pause();
    }
  }, [exerciseViewModel, progressViewModel, timerViewModel]);
  
  const contextValue = useMemo<ExerciseSessionContextValue>(() => ({
    exerciseViewModel,
    progressViewModel,
    timerViewModel,
    handleAnswerSubmit,
    handleSessionComplete,
    handleSessionPause,
    sessionStatus,
    totalTimeSpent: timerViewModel.totalTime
  }), [
    exerciseViewModel,
    progressViewModel,
    timerViewModel,
    handleAnswerSubmit,
    handleSessionComplete,
    handleSessionPause,
    sessionStatus,
    timerViewModel.totalTime
  ]);
  
  return (
    <ExerciseSessionContext.Provider value={contextValue}>
      {children}
    </ExerciseSessionContext.Provider>
  );
}
```

#### 2. 全局应用状态管理
```typescript
interface AppContextValue {
  user: User | null;
  theme: 'light' | 'dark';
  notifications: Notification[];
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  addNotification: (notification: Notification) => void;
}
```

### ❌ 禁止事项

- 复杂的业务计算逻辑（应在 ViewModel）
- 直接数据获取（应在 Model 层）
- UI 特定状态（应在 View 层）
- 数据格式转换（应在 Model 层 transformer）

## 🧠 ViewModel 层规范

### ✅ 核心职责

#### 1. 业务状态管理
```typescript
// ✅ 管理具体业务相关的状态
export function useExerciseViewModel() {
  const { submitAnswer: submitAnswerApi, isSubmitting: isApiSubmitting } = useSubmitAnswer(); // Model层
  const { questions, currentQuestion } = useQuestions(); // Model层
  
  // ViewModel 特有的业务状态
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [submitResult, setSubmitResult] = useState<SubmitResult | null>(null);
  const [timeSpent, setTimeSpent] = useState(0);
  
  // 业务逻辑处理
  const submitAnswer = useCallback(async (answer: string) => {
    try {
      const result = await submitAnswerApi({
        questionId: currentQuestion?.id,
        answer,
        timeSpent
      });
      
      setSubmitResult(result);
      setUserAnswer('');
      
      // 业务规则：答对了才能进入下一题
      if (result.isCorrect) {
        setCurrentQuestionIndex(prev => prev + 1);
      }
    } catch (error) {
      console.error('Submit failed:', error);
    }
  }, [submitAnswerApi, currentQuestion, timeSpent]);
  
  // 计算属性
  const progress = useMemo(() => {
    if (!questions?.length) return 0;
    return Math.round((currentQuestionIndex / questions.length) * 100);
  }, [currentQuestionIndex, questions?.length]);
  
  const isLastQuestion = useMemo(() => {
    return currentQuestionIndex >= (questions?.length || 0) - 1;
  }, [currentQuestionIndex, questions?.length]);
  
  return {
    // 状态
    currentQuestionIndex,
    currentQuestion,
    userAnswer,
    submitResult,
    timeSpent,
    progress,
    isLastQuestion,
    isSubmitting: isApiSubmitting,
    
    // 操作方法
    setUserAnswer,
    submitAnswer,
    setTimeSpent,
    
    // 导航方法
    goToNextQuestion: () => setCurrentQuestionIndex(prev => prev + 1),
    goToPreviousQuestion: () => setCurrentQuestionIndex(prev => Math.max(0, prev - 1))
  };
}
```

#### 2. 数据聚合和业务逻辑
```typescript
export function useUserListViewModel() {
  const { users, isLoading, deleteUser } = useUsers(); // Model层已处理数据转换
  
  // ViewModel 状态
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  
  // 数据聚合（非格式转换）
  const filteredUsers = useMemo(() => {
    return users?.filter(user => 
      user.displayName.toLowerCase().includes(searchTerm.toLowerCase())
    ) || [];
  }, [users, searchTerm]);
  
  // 业务操作
  const handleDeleteSelected = useCallback(async () => {
    for (const userId of selectedUsers) {
      await deleteUser(userId);
    }
    setSelectedUsers([]);
  }, [selectedUsers, deleteUser]);
  
  return {
    users: filteredUsers,
    isLoading,
    searchTerm,
    selectedUsers,
    setSearchTerm,
    handleDeleteSelected
  };
}
```

#### 3. 表单状态管理
```typescript
export function useUserFormViewModel(initialUser?: User) {
  const { createUser, updateUser } = useUserMutations(); // Model层
  
  const [formData, setFormData] = useState({
    name: initialUser?.displayName || '',
    email: initialUser?.emailAddress || '',
    role: initialUser?.role || 'user'
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const updateField = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 验证逻辑...
  }, []);
  
  const handleSubmit = useCallback(async () => {
    // 提交逻辑...
  }, [formData, errors]);
  
  return { formData, errors, isSubmitting, updateField, handleSubmit };
}
```

### ❌ 禁止事项

- 直接操作 DOM（应在 View 层）
- 直接调用数据获取 API（应在 Model 层）
- UI 相关状态（应在 View 层）
- 数据格式转换（应在 Model 层 transformer）

## 🔄 ViewModel 与 Context 协作模式

### Context与ViewModel协调机制

#### 核心原则：ViewModel作为门面模式

**规则**：View消费ViewModel → ViewModel消费Context → Context管理状态

```typescript
// ❌ View直接消费Context
function Component() {
  const exercise = useContext(ExerciseContext);
  const timer = useContext(TimerContext);
  // 组件需要了解多个Context的内部结构
}

// ✅ View通过ViewModel消费
function Component() {
  const { exercises, currentExercise, timeRemaining } = useExercisePageViewModel();
  // 组件只需要了解业务逻辑接口
}
```

#### ViewModel性能优化：防止Context瓶颈

**问题**：ViewModel消费多个Context时，任一Context更新都会重新执行ViewModel

**解决方案**：在ViewModel中激进使用缓存，阻断不必要的View重渲染

```typescript
// ✅ 正确的多Context消费模式
export function useExercisePageViewModel() {
  const session = useContext(UserSessionContext);    // 低频更新
  const timer = useContext(TimerContext);           // 高频更新
  const exercise = useContext(ExerciseContext);     // 中频更新

  // ViewModel会在timer每秒更新时重新执行
  // 但通过缓存确保View只在必要时重渲染

  // 缓存派生状态
  const canEditProfile = useMemo(() => {
    return session.permissions.includes('profile:edit');
  }, [session.permissions]);

  const progressPercentage = useMemo(() => {
    return Math.round((exercise.currentIndex / exercise.total) * 100);
  }, [exercise.currentIndex, exercise.total]);

  // 缓存返回对象 - 关键优化
  return useMemo(() => ({
    // 静态数据
    userName: session.name,
    canEditProfile,
    
    // 动态数据
    timeRemaining: timer.remaining,
    currentExercise: exercise.current,
    progressPercentage,
    
    // 稳定的操作方法
    onAnswerSubmit: exercise.submitAnswer,
    onTimerToggle: timer.toggle
  }), [
    session.name,
    canEditProfile,
    timer.remaining,
    timer.toggle,
    exercise.current,
    exercise.submitAnswer,
    progressPercentage
  ]);
}
```

**关键规则**：
- **必须缓存返回值**：使用`useMemo`包装整个返回对象
- **必须缓存派生状态**：复杂计算用`useMemo`预计算
- **稳定引用**：确保依赖数组精确，避免无效重渲染

#### Context依赖关系管理

**规则**：依赖关系必须通过Provider嵌套顺序体现

```typescript
// ❌ 错误：ExerciseProvider无法访问UserContext
<ExerciseProvider>
  <UserProvider>
    <App />
  </UserProvider>
</ExerciseProvider>

// ✅ 正确：依赖者嵌套在被依赖者内部
<UserProvider>           // 被依赖的Context
  <ExerciseProvider>     // 依赖UserContext的Context
    <App />
  </ExerciseProvider>
</UserProvider>
```

**Provider组合模式**：
```typescript
// contexts/ExerciseProviders.tsx
export function ExerciseProviders({ children }: { children: ReactNode }) {
  return (
    <UserSessionProvider>      {/* 最稳定 */}
      <ExerciseConfigProvider> {/* 中等稳定 */}
        <ExerciseStateProvider> {/* 事务性 */}
          <TimerProvider>       {/* 高频更新 */}
            {children}
          </TimerProvider>
        </ExerciseStateProvider>
      </ExerciseConfigProvider>
    </UserSessionProvider>
  );
}
```

**推荐嵌套顺序**：稳定性从外到内递减
1. 全局静态Context（用户会话、主题）
2. 功能配置Context（设置、权限）
3. 业务状态Context（表单、数据）
4. 高频Context（计时器、动画）

### 边缘情况处理规范

#### 1. Context间通信

**问题**：ContextA需要触发ContextB的操作

**❌ 反模式：直接Context通信**
```typescript
// 这会创建紧耦合，违反了职责分离
const ExerciseContext = createContext({
  // ❌ 不要这样做
  notifyProgress: (data) => progressContextActions.update(data)
});
```

**✅ 解决方案：ViewModel协调者模式**
```typescript
// ViewModel作为协调者，消费多个Context并协调操作
export function useExerciseCoordinatorViewModel() {
  const exercise = useContext(ExerciseContext);
  const progress = useContext(ProgressContext);
  const timer = useContext(TimerContext);

  const handleAnswerSubmit = useCallback(async (answer: string) => {
    // 协调多个Context的操作
    timer.pause();                          // 暂停计时
    const result = await exercise.submitAnswer(answer); // 提交答案
    progress.updateProgress(result);        // 更新进度
    
    if (result.isSessionComplete) {
      timer.stop();                         // 结束计时
    } else {
      timer.resume();                       // 恢复计时
    }
  }, [exercise, progress, timer]);

  return { handleAnswerSubmit };
}
```

#### 2. 多个高频Context的处理

**问题**：ViewModel需要消费多个高频Context

**策略**：激进缓存 + 必要时降级处理

```typescript
export function useRealtimeDashboardViewModel() {
  const liveData = useContext(LiveDataContext);     // 实时数据流
  const animations = useContext(AnimationContext);  // 动画状态
  const interactions = useContext(InteractionContext); // 用户交互

  // 这个ViewModel会非常频繁地重新执行
  // 关键：通过精确的缓存控制View的重渲染频率

  const displayData = useMemo(() => {
    // 只有当数据真正发生变化时才重新计算
    return processLiveData(liveData.values);
  }, [liveData.values]); // 注意：不依赖liveData的时间戳等元数据

  const isInteracting = useMemo(() => {
    return interactions.isActive;
  }, [interactions.isActive]);

  // 如果性能仍然有问题，考虑降频策略
  const throttledDisplayData = useMemo(() => {
    // 可以在这里实现降频逻辑
    return displayData;
  }, [displayData]);

  return useMemo(() => ({
    data: throttledDisplayData,
    isInteracting,
    // 动画状态通常不需要暴露给View
    // View应该通过CSS动画处理，而不是React状态
  }), [throttledDisplayData, isInteracting]);
}
```

**降级策略**：如果多高频Context不可避免地导致性能问题：
1. **局部状态管理**：考虑对该特定区域使用Zustand/Jotai
2. **组件分离**：将高频部分独立为子组件，限制重渲染范围
3. **虚拟化**：使用react-window等工具处理大量数据

#### 3. 动态Context管理

**场景**：需要运行时创建/销毁Context（如插件系统）

**❌ 避免：大多数情况下不需要动态Context**

**✅ 推荐方案：Context管理器模式**
```typescript
// 只在确实需要时使用
interface DynamicContextManager {
  contexts: Record<string, any>;
  registerContext: (id: string, context: any) => void;
  unregisterContext: (id: string) => void;
  getContext: (id: string) => any;
}

const DynamicContextManagerContext = createContext<DynamicContextManager | null>(null);

// 使用时
function useDynamicContext(contextId: string) {
  const manager = useContext(DynamicContextManagerContext);
  return manager?.getContext(contextId);
}
```

**警告**：动态Context增加了显著的复杂性，应该被限制在应用的特定区域，不要泄露到整个架构中。

### 模式一：Context 包装 ViewModel
```typescript
// ViewModel 处理具体业务逻辑
const useShoppingCartViewModel = () => {
  const [items, setItems] = useState([]);
  
  const addItem = (item) => setItems(prev => [...prev, item]);
  const removeItem = (id) => setItems(prev => prev.filter(item => item.id !== id));
  
  return { items, addItem, removeItem };
};

// Context 提供全局访问
const ShoppingCartProvider = ({ children }) => {
  const cartViewModel = useShoppingCartViewModel();
  
  return (
    <ShoppingCartContext.Provider value={cartViewModel}>
      {children}
    </ShoppingCartContext.Provider>
  );
};
```

### 模式二：Context 协调多个 ViewModel
```typescript
const CheckoutProvider = ({ children }) => {
  const cartViewModel = useShoppingCartViewModel();
  const paymentViewModel = usePaymentViewModel();
  
  // Context 层协调逻辑
  const handleCheckout = useCallback(async () => {
    await paymentViewModel.processPayment(cartViewModel.items);
    cartViewModel.clearCart();
  }, [cartViewModel, paymentViewModel]);
  
  return (
    <CheckoutContext.Provider value={{
      cart: cartViewModel,
      payment: paymentViewModel,
      handleCheckout
    }}>
      {children}
    </CheckoutContext.Provider>
  );
};
```


## ⚡ Context性能优化最佳实践

### Context 性能优化

```typescript
// ✅ useMemo 优化Context value
export function ExerciseProvider({ children }) {
  const viewModel = useExerciseViewModel();
  
  // 关键：确保value引用稳定性
  const contextValue = useMemo(() => ({
    ...viewModel,
    // 确保actions对象引用稳定
    actions: viewModel.actions
  }), [viewModel]);
  
  return (
    <ExerciseContext.Provider value={contextValue}>
      {children}
    </ExerciseContext.Provider>
  );
}
```

### Context 细粒度拆分策略

```typescript
// ✅ 推荐：按变更频率拆分Context
// 高频变更：练习状态
const ExerciseStateContext = createContext<{
  currentQuestion: Question | null;
  userAnswer: string;
  timeRemaining: number;
}>();

// 低频变更：练习配置
const ExerciseConfigContext = createContext<{
  questions: Question[];
  settings: ExerciseSettings;
  totalQuestions: number;
}>();

// 中频变更：用户操作
const ExerciseActionsContext = createContext<{
  submitAnswer: (answer: string) => void;
  goToNext: () => void;
  pauseExercise: () => void;
}>();
```

### 组合多个Context的最佳实践

```typescript
// ✅ Context组合器模式
export function ExerciseContextProvider({ children }: { children: React.ReactNode }) {
  const stateViewModel = useExerciseStateViewModel();
  const configViewModel = useExerciseConfigViewModel();
  const actionsViewModel = useExerciseActionsViewModel();
  
  return (
    <ExerciseConfigContext.Provider value={configViewModel}>
      <ExerciseStateContext.Provider value={stateViewModel}>
        <ExerciseActionsContext.Provider value={actionsViewModel}>
          {children}
        </ExerciseActionsContext.Provider>
      </ExerciseStateContext.Provider>
    </ExerciseConfigContext.Provider>
  );
}

// 自定义Hook简化消费
export function useExerciseState() {
  const context = useContext(ExerciseStateContext);
  if (!context) throw new Error('useExerciseState must be used within ExerciseContextProvider');
  return context;
}

export function useExerciseActions() {
  const context = useContext(ExerciseActionsContext);
  if (!context) throw new Error('useExerciseActions must be used within ExerciseContextProvider');
  return context;
}

export function useExerciseConfig() {
  const context = useContext(ExerciseConfigContext);
  if (!context) throw new Error('useExerciseConfig must be used within ExerciseContextProvider');
  return context;
}
```

### ViewModel 性能优化

```typescript
// ✅ 使用 useCallback 优化事件处理
export function useUserListViewModel() {
  const [users, setUsers] = useState([]);
  const [filter, setFilter] = useState('');
  
  const handleUserUpdate = useCallback((userId: string, updates: Partial<User>) => {
    setUsers(prev => prev.map(user => 
      user.id === userId ? { ...user, ...updates } : user
    ));
  }, []);
  
  const filteredUsers = useMemo(() => {
    return users.filter(user => 
      user.name.toLowerCase().includes(filter.toLowerCase())
    );
  }, [users, filter]);
  
  return {
    users: filteredUsers,
    onUserUpdate: handleUserUpdate,
    onFilterChange: setFilter
  };
}
```


## 📝 代码生成模板

### Context Provider 标准模板

#### 单一Context模板
```typescript
// app/contexts/[feature]-context.tsx
import { createContext, useContext, useMemo, type ReactNode } from 'react';
import { use[Feature]ViewModel } from '../viewmodels/use-[feature]-viewmodel';
import { useContextUpdateMonitor } from '../utils/context-monitor';

// Context定义
interface [Feature]ContextValue {
  // 从ViewModel获取的状态和方法
  state: [Feature]State;
  actions: [Feature]Actions;
  derivedState: Derived[Feature]State;
}

const [Feature]Context = createContext<[Feature]ContextValue | null>(null);

// Provider组件
export function [Feature]Provider({ children }: { children: ReactNode }) {
  const viewModel = use[Feature]ViewModel();
  
  // 开发时监控更新频率
  useContextUpdateMonitor(viewModel, '[Feature]Context', 5);
  
  // 确保Context value稳定性
  const contextValue = useMemo<[Feature]ContextValue>(() => ({
    state: viewModel.state,
    actions: viewModel.actions,
    derivedState: viewModel.derivedState
  }), [viewModel.state, viewModel.actions, viewModel.derivedState]);
  
  return (
    <[Feature]Context.Provider value={contextValue}>
      {children}
    </[Feature]Context.Provider>
  );
}

// 消费Hook
export function use[Feature]Context(): [Feature]ContextValue {
  const context = useContext([Feature]Context);
  if (!context) {
    throw new Error('use[Feature]Context must be used within [Feature]Provider');
  }
  return context;
}

// 类型导出
export type { [Feature]ContextValue };
```

#### 拆分Context模板（按更新频率）
```typescript
// app/contexts/[feature]-multi-context.tsx
import { createContext, useContext, useMemo, type ReactNode } from 'react';

// 高频状态Context
interface [Feature]HighFreqState {
  timer: number;
  inputValue: string;
  animationProgress: number;
}

const [Feature]HighFreqContext = createContext<[Feature]HighFreqState | null>(null);

// 静态/事务状态Context
interface [Feature]StableState {
  config: [Feature]Config;
  userPermissions: Permission[];
  cachedData: [Feature]Data[];
}

const [Feature]StableContext = createContext<[Feature]StableState | null>(null);

// 操作方法Context
interface [Feature]Actions {
  updateTimer: (time: number) => void;
  handleSubmit: (data: FormData) => Promise<void>;
  resetState: () => void;
}

const [Feature]ActionsContext = createContext<[Feature]Actions | null>(null);

// 组合Provider
export function [Feature]MultiProvider({ children }: { children: ReactNode }) {
  const highFreqViewModel = use[Feature]HighFreqViewModel();
  const stableViewModel = use[Feature]StableViewModel();
  const actionsViewModel = use[Feature]ActionsViewModel();

  // 分别缓存不同频率的状态
  const highFreqValue = useMemo(() => highFreqViewModel.state, [highFreqViewModel.state]);
  const stableValue = useMemo(() => stableViewModel.state, [stableViewModel.state]);
  const actionsValue = useMemo(() => actionsViewModel.actions, [actionsViewModel.actions]);

  return (
    <[Feature]StableContext.Provider value={stableValue}>
      <[Feature]ActionsContext.Provider value={actionsValue}>
        <[Feature]HighFreqContext.Provider value={highFreqValue}>
          {children}
        </[Feature]HighFreqContext.Provider>
      </[Feature]ActionsContext.Provider>
    </[Feature]StableContext.Provider>
  );
}

// 专用消费Hooks
export function use[Feature]HighFreq() {
  const context = useContext([Feature]HighFreqContext);
  if (!context) throw new Error('use[Feature]HighFreq must be used within [Feature]MultiProvider');
  return context;
}

export function use[Feature]Stable() {
  const context = useContext([Feature]StableContext);
  if (!context) throw new Error('use[Feature]Stable must be used within [Feature]MultiProvider');
  return context;
}

export function use[Feature]Actions() {
  const context = useContext([Feature]ActionsContext);
  if (!context) throw new Error('use[Feature]Actions must be used within [Feature]MultiProvider');
  return context;
}
```

### ViewModel 标准模板

#### 基础ViewModel模板（推荐新项目使用）
```typescript
// app/viewmodels/use-[feature]-viewmodel.ts
import { useState, useMemo, useCallback } from 'react';
import { use[Feature]Model } from '../models/[feature]-model';

export function use[Feature]ViewModel() {
  // Model层数据
  const { data, isLoading, error, mutate } = use[Feature]Model();
  
  // ViewModel状态
  const [selectedId, setSelectedId] = useState<string | null>(null);
  
  // 派生状态
  const selectedItem = useMemo(() => 
    data?.find(item => item.id === selectedId) || null, 
    [data, selectedId]
  );
  
  // 操作方法
  const handleSelect = useCallback((id: string) => {
    setSelectedId(id);
  }, []);
  
  const handleRefresh = useCallback(async () => {
    await mutate();
  }, [mutate]);
  
  return {
    // 数据
    data: data || [],
    selectedItem,
    selectedId,
    
    // 状态
    isLoading,
    error,
    hasSelection: selectedId !== null,
    totalCount: data?.length || 0,
    
    // 操作
    handleSelect,
    handleRefresh
  };
}
```

#### Context协调ViewModel模板
```typescript
// app/viewmodels/use-[feature]-coordinator-viewmodel.ts
import { useCallback } from 'react';
import { use[Feature]HighFreq, use[Feature]Stable, use[Feature]Actions } from '../contexts/[feature]-multi-context';

export function use[Feature]CoordinatorViewModel() {
  // 消费多个Context
  const highFreqState = use[Feature]HighFreq();
  const stableState = use[Feature]Stable();
  const actions = use[Feature]Actions();

  // 协调操作
  const handleComplexOperation = useCallback(async (data: ComplexData) => {
    await actions.updateTimer(0);
    await actions.handleSubmit(data);
  }, [actions]);

  const handleReset = useCallback(() => {
    actions.resetState();
  }, [actions]);

  return {
    // 聚合数据
    timer: highFreqState.timer,
    config: stableState.config,
    permissions: stableState.userPermissions,
    
    // 协调操作
    handleComplexOperation,
    handleReset
  };
}
```

#### 高级优化模板（性能敏感场景）
```typescript
// app/viewmodels/use-[feature]-optimized-viewmodel.ts  
import { useContext, useMemo, useCallback, useRef } from 'react';

// 适用于：高频更新+复杂计算的性能敏感场景
export function use[Feature]OptimizedViewModel() {
  const highFreqContext = useContext(HighFreqContext);
  const stableContext = useContext(StableContext);
  
  // 使用ref避免不必要的依赖
  const stableDataRef = useRef(stableContext.data);
  if (stableContext.data !== stableDataRef.current) {
    stableDataRef.current = stableContext.data;
  }

  // 分离高频和低频的计算
  const stableComputedData = useMemo(() => 
    processStableData(stableDataRef.current), 
    [stableDataRef.current]
  );

  const highFreqComputedData = useMemo(() => 
    simpleTransform(highFreqContext.value), 
    [highFreqContext.value]
  );

  return useMemo(() => ({
    stableData: stableComputedData,
    realTimeData: highFreqComputedData
  }), [stableComputedData, highFreqComputedData]);
}
```

## 📂 复用管理与目录规范

### 层级查找优先级
```
第一优先级: app/viewmodels（独立业务逻辑）
第二优先级: app/contexts（状态协调逻辑）  
第三优先级: packages/[shared]/viewmodels 或 contexts（跨应用复用）
```

### 目录结构
```
app/viewmodels/          # 独立 ViewModel Hooks
├── README.md            # 可复用 ViewModel 聚合索引
└── use-*-viewmodel.ts   # ViewModel文件

app/contexts/            # Context Provider
├── README.md            # 可复用 Context 聚合索引
└── *-context.tsx        # Context文件
```

### 命名约定
```typescript
// ViewModel文件
use-exercise-viewmodel.ts → useExerciseViewModel()

// Context文件
exercise-context.tsx → ExerciseProvider + useExerciseContext()

// 细粒度Context文件
exercise-multi-context.tsx → ExerciseMultiProvider + 多个use*Hooks
```

### 架构选择指导

- **多组件协作业务**：使用功能级 Context，如作业管理、课程编辑
- **领域数据协调**：使用领域级 Context，如用户数据、课程数据跨页面共享
- **应用级状态**：使用全局 Context，如用户登录状态、主题设置
- **简单独立功能**：直接使用 ViewModel，如表单处理、搜索功能

## 🎯 React 19 架构核心原则

### 1. 性能优先的Context设计
- **细粒度拆分**：按变更频率拆分Context，减少不必要的重渲染
- **预计算优化**：ViewModel中预计算派生状态，避免消费者重复计算
- **稳定引用**：使用useMemo确保Context value和actions的引用稳定性

### 2. React 19 新特性集成
- **简化Provider语法**：使用`<Context value={data}>`代替`<Context.Provider>`
- **条件性Context使用**：充分利用`use` API的条件调用能力
- **Client Component边界**：明确标记`"use client"`，确保状态管理在客户端

### 3. 职责分离与边界清晰
- **ViewModel职责**：业务逻辑处理、状态管理、Model层调用
- **Context职责**：跨组件状态协调、ViewModel组合、状态分发
- **严格分层**：禁止跨层调用，确保架构清晰可维护

### 4. 开发效率与代码质量
- **模板化开发**：使用标准模板快速生成Context和ViewModel
- **类型安全**：强制TypeScript类型定义，提供完整的类型推导
- **错误边界**：Context消费时提供清晰的错误提示

### 5. 团队协作与长期维护
- **统一命名**：遵循kebab-case文件名和PascalCase组件名约定
- **文档驱动**：README索引管理，便于复用和查找
- **决策简化**：基于场景的4级架构体系，80/20简化决策流程

## 🎯 实用架构决策指南

### 决策矩阵

基于业务场景快速选择架构模式：

| 维度 | 级别1 | 级别2 | 级别3 | 级别4 |
|------|-------|-------|-------|-------|
| **交互需求** | 简单点击 | 复杂表单 | 多组件协作 | 全局状态 |
| **状态复杂度** | 简单状态 | 业务状态 | 跨组件状态 | 应用状态 |
| **组件范围** | 单组件 | 单组件 | 多组件 | 全应用 |
| **数据流向** | 组件内部 | 组件计算 | 组件共享 | 全局共享 |
| **性能影响** | 轻量 | 中等 | 需优化 | 需精心设计 |

### 场景映射表

```typescript
// 典型业务场景 → 推荐架构级别
const 场景映射 = {
  // 级别1：Simple Interactions
  "点赞按钮": "级别1",
  "展开/折叠": "级别1",
  "模态框开关": "级别1",
  
  // 级别2：Business Logic
  "搜索功能": "级别2",
  "表单验证": "级别2", 
  "数据筛选": "级别2",
  
  // 级别3：State Coordination
  "练习系统": "级别3",
  "购物车": "级别3",
  "多步骤流程": "级别3",
  
  // 级别4：Global State
  "用户登录状态": "级别4",
  "主题设置": "级别4",
  "全局通知": "级别4"
};
```

### 🚨 架构反模式警告

**❌ 避免的反模式**：
- **Context滥用**：为简单状态创建Context
- **ViewModel膨胀**：单个ViewModel处理所有业务逻辑
- **盲目分层**：为分层而分层，忽略实际需求
- **过度抽象**：过度优化导致代码复杂难懂

**✅ 推荐的最佳实践**：
- **状态就近原则**：状态尽可能靠近使用者
- **组合优于继承**：小hooks组合成大功能
- **渐进式演进**：支持架构的平滑升级
- **明确边界**：清晰的职责划分和依赖关系

### 🔧 重构时机识别

**升级信号**：
- 组件内`useState`过多 → 考虑级别2
- props层层传递 → 考虑级别3
- 多处重复逻辑 → 提取ViewModel
- 全局状态散乱 → 考虑级别4

**降级信号**：
- Context只有一个消费者 → 降级为ViewModel
- 复杂状态只在一个组件用 → 降级为局部状态
- 过度抽象导致代码复杂 → 简化为更直接的实现

### 实战场景应用

#### 场景1：学生练习系统
```typescript
// 分析：多组件协作 + 复杂状态管理
// 推荐：级别3 (Context协调)

// 架构选择理由：
// 1. 练习、进度、计时器需要多组件协调 → Context
// 2. 状态复杂度高，需要专业的状态管理
// 3. 生命周期管理复杂

export function ExerciseProvider({ children }) {
  const exerciseVM = useExerciseViewModel();
  const progressVM = useProgressViewModel();
  const timerVM = useTimerViewModel();
  
  const contextValue = useMemo(() => ({
    exercise: exerciseVM,
    progress: progressVM,
    timer: timerVM,
    // 协调操作
    handleSubmit: async (answer) => {
      timerVM.pause();
      await exerciseVM.submitAnswer(answer);
      progressVM.updateProgress();
      timerVM.resume();
    }
  }), [exerciseVM, progressVM, timerVM]);
  
  return (
    <ExerciseContext.Provider value={contextValue}>
      {children}
    </ExerciseContext.Provider>
  );
}
```

#### 场景2：用户搜索功能  
```typescript
// 分析：单组件 + 中等复杂度 + 无需共享
// 推荐：级别2 (独立ViewModel)

// 架构选择理由：
// 1. 搜索功能相对独立 → ViewModel
// 2. 有一定业务逻辑（防抖、缓存） → 不用简单状态
// 3. 无需跨组件共享 → 不用Context

export function useSearchViewModel() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const debouncedSearch = useMemo(() => 
    debounce(async (searchTerm) => {
      setIsLoading(true);
      const data = await searchAPI(searchTerm);
      setResults(data);
      setIsLoading(false);
    }, 300), []
  );
  
  useEffect(() => {
    if (query.trim()) {
      debouncedSearch(query);
    }
  }, [query, debouncedSearch]);
  
  return { query, setQuery, results, isLoading };
}
```

#### 场景3：主题切换按钮
```typescript  
// 分析：简单交互 + 全局状态
// 推荐：级别1 (简单交互) + 级别4 (全局Context)

// 架构选择理由：
// 1. 按钮交互简单 → 简单交互
// 2. 主题是全局状态 → 全局Context
// 3. 不需要复杂的业务逻辑

export function ThemeToggle() {
  const { theme, toggleTheme } = useThemeContext();
  
  return (
    <button onClick={toggleTheme}>
      {theme === 'light' ? '🌙' : '☀️'}
    </button>
  );
}
```

## 🚀 总结：现代架构实践

### 关键优势
1. **零第三方依赖**：基于React原生API，稳定可靠
2. **性能优化**：细粒度Context拆分 + ViewModel预计算
3. **开发效率**：标准化模板 + 清晰的决策流程
4. **类型安全**：完整的TypeScript支持
5. **长期维护**：清晰的架构边界 + 完善的文档体系

## 📋 架构健康度与Code Review综合检查清单

### Context设计与实现
- [ ] **拆分理由**：PR描述是否使用快速路径或详细分析框架论证？
- [ ] **高频隔离**：是否正确隔离了高频状态（>1次/秒）？
- [ ] **不可变性**：Context值是否始终不可变（使用扩展运算符）？
- [ ] **Provider优化**：Context Provider是否使用useMemo优化value？
- [ ] **依赖顺序**：Provider嵌套顺序是否体现了正确的依赖关系？
- [ ] **用途合理**：是否用于真正需要跨组件共享的状态？

### ViewModel设计与实现
- [ ] **职责边界**：是否避免了直接DOM操作和UI相关状态？
- [ ] **Model层调用**：是否正确调用Model层接口，避免直接API调用？
- [ ] **数据转换**：是否避免了数据格式转换（应在Model层处理）？
- [ ] **性能优化**：计算属性是否使用useMemo？事件处理是否使用useCallback？
- [ ] **返回值缓存**：消费多个Context的ViewModel是否使用useMemo缓存返回值？

### 协作模式与架构
- [ ] **ViewModel门面**：组件是否通过ViewModel而非直接消费Context？
- [ ] **职责分离**：ViewModel和Context的职责边界是否清晰？
- [ ] **状态流向**：状态流向是否单向且可预测？
- [ ] **架构选择**：是否选择了合适的架构级别（1-4级）？

### 整体结构与维护性
- [ ] **Context数量**：Context数量合理，每个Context职责单一？
- [ ] **ViewModel粒度**：ViewModel粒度适中，不会过度膨胀？
- [ ] **组件层级**：是否避免了不必要的深层嵌套（建议≤3层）？
- [ ] **命名约定**：是否遵循kebab-case文件名和PascalCase组件名约定？
- [ ] **文档完整**：架构模式选择是否有明确理由和文档？

### 性能与质量
- [ ] **重渲染控制**：是否没有不必要的重渲染？
- [ ] **缓存策略**：大列表和复杂计算是否有适当的缓存策略？
- [ ] **代码分割**：是否有合理的代码分割和懒加载？
- [ ] **技术债务**：是否有明确的技术债务识别和处理机制？

### 最佳实践回顾
- 简单交互 → 级别1：Simple Interactions
- 独立功能 → 级别2：Business Logic  
- 复杂协作 → 级别3：State Coordination
- 全局状态 → 级别4：Global State

遵循这些规范，ViewModel & Context 层将成为高内聚、低耦合、易复用的业务逻辑层，为现代React应用提供稳定可靠的架构支撑。