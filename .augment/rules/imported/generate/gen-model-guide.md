---
type: "agent_requested"
---

# Model 层代码编写指南

## 🎯 核心原则

1. **只做数据相关操作** - API 调用、数据转换、缓存
2. **分离校验职责** - 后端数据用 Zod，前端传参用 TS 类型
3. **不处理业务逻辑** - 复杂业务逻辑放 ViewModel
4. **不处理任何 Mock 逻辑** - 不得包含 Mock API 的具体实现逻辑
5. **工作流程** 
  - 编写代码
  - 编写文档
  - 编写测试用例（必须使用指南 test-model-guide.mdc）
  - 运行测试（必须使用指南 test-model-guide.mdc），强制使用 `pnpm run test:model [model-name]` 命令执行测试
  - 修复测试（必须基于指南中的说明 test-model-guide.mdc）
  - 输出model 层自查清单和测试报告

## 📂 目录结构

```
apps/(tch|stu|admin)/app/models/
├── user/                   # 用户模块
│   ├── user-model.ts       # 主要 Model 文件，包含所有 Hooks
│   ├── types.ts            # TypeScript 类型定义（前端传参）
│   ├── schemas.ts          # Zod 类型定义（后端数据校验）
│   ├── transformers.ts     # 数据转换函数（可选）
│   ├── index.ts            # 统一导出
│   ├── README.md           # 模块文档
│   └── __tests__/          # 测试目录
│       ├── simple-vitest.test.ts    # 基础功能测试
│       ├── schemas.test.ts          # Schema 校验测试
│       └── transformers.test.ts     # 转换层测试（可选）
├── course/                 # 课程模块
│   ├── course-model.ts
│   ├── types.ts
│   ├── schemas.ts
│   ├── transformers.ts     # （可选）
│   ├── index.ts
│   ├── README.md
│   └── __tests__/
│       ├── simple-vitest.test.ts    # 基础功能测试
│       ├── schemas.test.ts          # Schema 校验测试
│       └── transformers.test.ts     # 转换层测试（可选）
├── [model-name]/          # 练习会话模块
│   ├── [model-name]-model.ts
│   ├── types.ts
│   ├── schemas.ts
│   ├── transformers.ts     # （可选）
│   ├── index.ts
│   ├── README.md
│   └── __tests__/
│       ├── simple-vitest.test.ts    # 基础功能测试
│       ├── schemas.test.ts          # Schema 校验测试
│       └── transformers.test.ts     # 转换层测试（可选）
├── index.ts               # 统一导出文件
└── README.md              # Model 层文档索引
```

### 目录结构规范
- [model-name]: 结合实际的 api 文档来定义 model-name
- **模块化组织** - 每个业务模块独立目录，便于维护和扩展
- **必需文件** - 每个模块必须包含 `[model-name]-model.ts`、`types.ts`、`schemas.ts`、`index.ts`、`README.md`
- **可选文件** - `transformers.ts` 仅在需要数据转换时创建
- **测试目录** - `__tests__/` 包含基础功能测试，转换测试仅在有 transformers.ts 时创建
- **清晰职责分离** - types 负责前端传参，schemas 负责后端校验，transformers 负责数据转换
- **统一导出** - 通过模块级和全局级 `index.ts` 统一导出接口


## 📝 类型定义规范

### 后端返回数据 - 用 Zod
```typescript
const ApiUserSchema = z.object({
  user_id: z.number(),
  user_name: z.string(),
  status: z.enum(['active', 'inactive'])
});
```

### 前端传参 - 用 TS 类型
```typescript
export interface CreateUserPayload {
  name: string;
  email: string;
  role: 'admin' | 'user';
}
```

## 📋 Schema 设计最佳实践

### 🎯 数值字段约束规范
**重要：所有数值字段必须根据业务逻辑设置合理的范围约束**

```typescript
// ✅ 正确的数值约束
const ApiProgressSchema = z.object({
  currentProgress: z.number().min(0),           // 进度不能为负
  totalEstimated: z.number().min(0),            // 总数不能为负
  progressPercentage: z.number().min(0).max(100), // 百分比 0-100
  currentPosition: z.number().min(0),           // 位置不能为负
});

const ApiTimingSchema = z.object({
  currentTime: z.number().min(0),               // 时间不能为负
  totalTime: z.number().min(0),                 // 总时间不能为负
});

// ❌ 错误的写法 - 缺少约束
const BadSchema = z.object({
  progress: z.number(),        // 允许负数，不符合业务逻辑
  percentage: z.number(),      // 可能超过 100%，不合理
});
```

### 🔍 业务逻辑约束要求
- **进度相关字段**：使用 `.min(0)` 确保非负，百分比使用 `.min(0).max(100)`
- **计数相关字段**：使用 `.min(0)` 确保非负
- **时间相关字段**：使用 `.min(0)` 确保非负
- **枚举值字段**：必须与 API 文档完全一致
- **ID 类字段**：通常使用 `.min(1)` 确保有效ID

### ⚠️ Schema 校验强制检查清单
写 Schema 时必须检查：
- [ ] 所有数值字段是否设置了合理的范围约束？
- [ ] 百分比字段是否限制在 0-100 范围内？
- [ ] 计数字段是否设置了非负约束？
- [ ] 枚举值是否与 API 文档完全匹配？
- [ ] 可选字段是否正确使用了 `.optional()` 或 `.nullable()`？
## 🌐 网络请求规范

### 统一 Fetcher 使用
**必须使用项目统一的 fetcher，禁止直接使用 fetch API**

- 如果 fetcher 中缺少所需的 HTTP 方法，必须停止任务并询问用户，如：
  ```
  大帅：我发现当前项目的 fetcher 工具中缺少 DELETE 方法。
  
  当前可用方法：GET, POST
  需要的方法：DELETE
  
  请问您希望我：
  1. 为 fetcher 添加 DELETE 方法
  2. 使用其他替代方案
  3. 暂停此任务
  ```

```typescript
// ✅ 正确：使用当前项目下的 fetcher
import { get, post } from '@/app/utils/fetcher';

// ❌ 错误：绝对禁止直接使用 fetch
const response = await fetch('/api/users');
```

### Fetcher 特性
- **自动添加设备ID** - 每个请求自动携带 `device_id` 头部
- **统一错误处理** - 自动处理 HTTP 错误和业务错误码
- **类型安全** - 支持泛型，确保返回数据类型安全
- **标准化响应** - 自动解析 `{ code, data, message }` 格式

### GET 请求规范
```typescript
// 基础 GET 请求
const users = await get<User[]>('/api/users', {});

// 带查询参数的 GET 请求
const users = await get<User[]>('/api/users', {
  query: { page: '1', limit: '10' }
});
```

### POST 请求规范
```typescript
// 基础 POST 请求
const result = await post<CreateUserResult>('/api/users', {
  arg: { name: 'John', email: '<EMAIL>' }
});

// 无参数 POST 请求
const result = await post<Result>('/api/action', {});
```

## 🔄 标准模式

### 数据获取 Hook
```typescript
import { get } from '@/app/utils/fetcher';

export function useUsers() {
  const { data, error, isLoading, mutate } = useSWR('/api/users', async (url) => {
    const response = await get<ApiUser[]>(url, {});
    return transformUsersData(response); // 内部用 Zod 校验
  });
  
  return { users: data, error, isLoading, refreshUsers: mutate };
}
```

### 数据提交 Hook
```typescript
import { post } from '@/app/utils/fetcher';

export function useSubmitAnswer() {
  const { trigger, isMutating, error } = useSWRMutation('/api/answers',
    async (url, { arg }: { arg: SubmitAnswerPayload }) => {
      const response = await post<ApiAnswerResult>(url, { arg }); // arg 用 TS 类型
      return transformAnswerResult(response); // response 用 Zod 校验
    }
  );
  
  return { submitAnswer: trigger, isSubmitting: isMutating, submitError: error };
}
```

### 带查询参数的数据获取 Hook
```typescript
import { get } from '@/app/utils/fetcher';

export function useUserList(params?: { page?: number; limit?: number }) {
  const shouldFetch = params?.page && params?.limit;
  const queryParams = shouldFetch
    ? { page: params.page.toString(), limit: params.limit.toString() }
    : undefined;

  const { data, error, isLoading, mutate } = useSWR(
    shouldFetch ? ['/api/users', queryParams] : null,
    async ([url, query]) => {
      const response = await get<ApiUser[]>(url, { query });
      return transformUsersData(response);
    }
  );

  return { users: data, error, isLoading, refreshUsers: mutate };
}
```
### 数据转换函数（按需使用）

**⚠️ 重要：数据转换通常都不是必须的，只在特定场景下使用**

#### 何时需要数据转换？
- **后端字段名与前端不一致** - 如 `user_id` → `id`
- **数据结构需要重组** - 如嵌套对象扁平化
- **数据类型需要转换** - 如字符串转数字、日期格式化
- **后端数据格式变更** - 兼容性处理

#### 何时不需要数据转换？
- **后端数据格式与前端期望一致** - 直接使用 Zod 校验即可
- **新建 Model 且 API 设计合理** - 通常不需要转换
- **简单的数据获取** - 如列表、详情等标准 CRUD

#### 转换函数示例
```typescript
// ✅ 需要转换的场景
function transformUsersData(apiData: unknown): User[] {
  const validated = z.array(ApiUserSchema).parse(apiData); // Zod 校验
  return validated.map(user => ({
    id: user.user_id,           // 字段名转换
    name: user.user_name,       // 字段名转换
    isActive: user.status === 'active'  // 数据类型转换
  }));
}

// ✅ 不需要转换的场景
function getUsersData(apiData: unknown): User[] {
  return ApiUserSchema.parse(apiData); // 直接校验返回
}
```

## ❌ 禁止操作

```typescript
// ❌ UI 状态和业务逻辑
const [isLoading, setIsLoading] = useState(false);
const handleUserClick = () => { /* 应在 ViewModel */ };

// ❌ 路由和组件生命周期
const router = useRouter();
useEffect(() => { /* 应在 ViewModel */ }, []);

// ❌ 直接使用 fetch API
const response = await fetch('/api/users');
const data = await fetch('/api/users', { method: 'POST' });

// ❌ 自定义 fetcher 函数
async function customFetcher(url: string) {
  return fetch(url).then(res => res.json());
}
```

## ✅ 检查清单

写 Model 层代码时问自己：
- [ ] 是否只做数据获取/转换？
- [ ] 后端数据用了 Zod 校验？
- [ ] 前端传参用了 TS 类型？
- [ ] 没有处理业务逻辑？
- [ ] 使用了项目统一的 fetcher？
- [ ] 正确导入了 `get`、`post` 方法？
- [ ] 生成的代码是否匹配用户给出的 api 文档中的数量
- [ ] 生成的代码是否匹配用户给出的 api 文档中的参数

## 🔍 Schema 设计检查清单

写 Schema 时必须检查：
- [ ] 所有数值字段是否设置了合理的范围约束？
- [ ] 进度相关字段是否使用了 `.min(0)` 确保非负？
- [ ] 百分比字段是否限制在 0-100 范围内（`.min(0).max(100)`）？
- [ ] 计数字段是否设置了非负约束（`.min(0)`）？
- [ ] 时间相关字段是否设置了非负约束（`.min(0)`）？
- [ ] 枚举值是否与 API 文档完全匹配？
- [ ] 可选字段是否正确使用了 `.optional()` 或 `.nullable()`？
- [ ] ID 类字段是否使用了合适的约束（通常 `.min(1)`）？
## � 自查清单模板

**重要：Model 层代码完成后，必须使用以下模板输出自查清单报告**

```markdown
# 📋 Model 层自查清单报告

## 🎯 核心原则检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 只做数据相关操作 | ✅ | 仅包含 API 调用、数据转换、缓存 |
| 分离校验职责 | ✅ | 后端数据用 Zod，前端传参用 TS 类型 |
| 不处理业务逻辑 | ✅ | 复杂业务逻辑留给 ViewModel |
| 不处理 Mock 逻辑 | ✅ | 无 Mock API 具体实现 |

## 📝 类型定义检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 后端数据 Zod 校验 | ✅ | 所有 API 响应都有对应的 Schema |
| 前端传参 TS 类型 | ✅ | 所有请求参数都有 TypeScript 类型定义 |
| 数据转换函数 | ✅ | 处理后端字段与前端期望的差异（如需要） |


## 📊 代码质量/规范检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| API 数量匹配 | ✅ | 生成的 Hook 数量与 API 文档一致 |
| 参数完全匹配 | ✅ | 所有参数与 API 文档规范一致 |
| 导入路径正确 | ✅ | 正确导入 `get`、`post` 方法 |
| 类型安全 | ✅ | 完整的 TypeScript 类型覆盖 |
| 使用项目统一 fetcher | ✅ | 导入并使用 `@/app/utils/fetcher` |

## 📚 文档完成检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 模块 README | ✅ | 包含使用示例和 API 说明 |
| 全局 README 更新 | ✅ | 在全局 models README 中添加新模块 |
| 统一导出更新 | ✅ | 在 models/index.ts 中导出新模块 |


## 🧪 测试完成检查

| 测试类型 | 状态 | 说明 |
|----------|------|------|
| 基础功能测试 | ✅ | 所有 Hook 的基本功能测试通过 |
| 转换函数测试 | ✅ | 数据转换逻辑测试通过（如有） |
| 错误处理测试 | ✅ | 异常情况处理测试通过 |
| Mock 规范遵循 | ✅ | 使用正确的 Vitest Mock 语法 |

---
**自查完成时间**: [时间]
**整体状态**: 🎉 所有检查项通过 / ⚠️ 存在问题需要修复
```

### 使用说明
- ✅ 表示已完成的项目
- ❌ 表示未完成或有问题的项目
- ⚠️ 表示需要注意的项目
- 🎉 表示全部完成
- 🎉 表示全部完成


##  文档化规范 (README.md)

### Model 层 README 位置
- `app/models/README.md` - 应用内 Model 层文档
- `packages/[package]/models/README.md` - 共享包 Model 层文档

### README 内容规范

```markdown
# Models

## 可用 Model Hooks

| Hook 名称 | 描述 | 主要接口 | 依赖 API |
|-----------|------|----------|----------|
| `useUsers` | 获取用户列表数据 | `users, isLoading, error, refreshUsers` | GET `/api/users` |
| `useSubmitAnswer` | 提交答案数据 | `submitAnswer, isSubmitting, submitError` | POST `/api/answers` |

## 核心数据结构

- `ApiUserSchema`, `User` - 用户数据类型
- `ApiAnswerSchema`, `Answer` - 答案数据类型
```

### 维护要求
- **新增模块时**：必须同步更新 README.md
- **开发前检查**：查阅 README.md 了解可复用模块
- **Code Review**：检查文档更新情况

## 🔧 命名规范

- **组件/Hooks**：PascalCase (`useUsers`, `UserList`)
- **文件名**：kebab-case (`user-model.ts`, `use-user-viewmodel.ts`)
- **类型定义**：PascalCase (`CreateUserPayload`, `ApiUserSchema`) 


## 🎭 数据 Mocking 策略

**Model 层和 ViewModel 层严禁包含 Mock 数据实现。**

### 1. API 中心化 Mock (首选)
- 使用 ApiFox、Postman Mock Servers 等专业工具
- 通过环境变量 `NEXT_PUBLIC_API_HOST` 指向 Mock 服务器
- 团队共享一致的 Mock API，避免数据不一致

### 2. 前端项目级 Mock API

#### 使用边界判断
**✅ 适合使用前端 Mock API 的场景：**
- **数据流转性强的业务**：数据状态有强依赖关系，随机 Mock 无法匹配真实业务流程
- **复杂业务流程**：多步骤操作，需要模拟状态变化和流程推进
- **前端先行开发**：不依赖后端进度，独立验证前端逻辑
- **特殊边缘场景**：模拟特定错误状态、异常流程等外部 Mock 工具难以支持的情况

**❌ 不适合使用前端 Mock API 的场景：**
- **简单数据展示**：静态列表、基础 CRUD 操作
- **外部 Mock 工具已满足**：ApiFox 等工具能完全覆盖的场景
- **无状态依赖**：数据间无关联关系的场景



## 🧪 下一步操作
完成 model 层代码编写后必须输出自查清单报告，并准备好进入测试流程

测试流程准备工作：
1. 必须主动说明你将使用model 测试指南 [test-model-guide.mdc](../test/test-model-guide.mdc)处理测试流程
2. 必须主动像用户询问是否需要进入测试流程，用户确认后你必须读取test-model-guide.mdc 然后开始处理测试流程
3. 测试用例编写和运行测试方式都在测试指南中说明，你必须完全严格遵守
4. 有任何问题你可以终端任务并向用户提问