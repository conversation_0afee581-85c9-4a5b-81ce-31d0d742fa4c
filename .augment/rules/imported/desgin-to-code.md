---
type: "agent_requested"
---

# 用于将figma中工具得到的react-tailwind.css片段实现为组件

## 布局调整
- 遇到inline-flex, 改为flex, flex-row
- 最外层元素, 如果是固定宽度则改为w-full
- 如果是左右布局, 如果其中一部分是可以确定为固定宽度的元素, 保持图片宽度, 另一部分使用flex-1确保最大宽度
- 如果是上下局部, 如果其中一部分是可以确定为固定高度的元素, 保持图片高度, 另一部分使用flex-1确保最大高度
- 尽量不用position,禁用float,html层级尽量少

## 优化tailwind.css

- 将字体设置font-['Resource_Han_Rounded_SC'] 替换为 font-resource-han-rounded 
- 如果重复定义的className, 去重, 如: outline outline-[1.20px], 去掉outline
- 除了 1px 0.5px 之外，不要出现 px 单位
- 每个 dom 元素都需要根据其功能，定义一个语义化 class, 如: homework_detail_container, 方便调试查找元素
