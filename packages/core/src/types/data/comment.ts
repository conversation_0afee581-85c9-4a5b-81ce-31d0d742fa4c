export type Reference = {
  referenceId: number;
  /** 引用类型 */
  referenceType: number;
  /** 引用内容的文案 */
  referenceContent: string;
  /** 引用位置信息 */
  referencePosition: {
    data: (
      | {
          lineId: string;
          textureId: string;
          start: string;
          end: string;
          type: "text";
        }
      | {
          lineId: string;
          textureId: undefined;
          start: undefined;
          end: undefined;
          type: "pic";
        }
    )[];
  } /** 引用位置MD5 */;
  referencePositionMd5: string;
  /** 一级评论的数量 */
  commentCount: number;
};

export type MergedReference = {
  [lineId: string]: {
    [textureId: string]: {
      start: number;
      end: number;
      isLast: boolean; // 是最后一个
      referenceIds: {
        [referenceId: string]: string; // 值是referencePositionMd5
      };
      /** 引用类型 */
      referenceType: number;
      /** 一级评论的数量 */
      commentCount: number;
      referencePosition: Reference["referencePosition"]["data"];
    }[];
  };
};
