export type DeviceInfo = {
  deviceId: string;
  deviceName: string;
  deviceType: string;
  deviceVersion: string;
  screenWidth: number;
  screenHeight: number;
  fullScreenWidth: number;
  fullScreenHeight: number;
  deviceScreenDensity: number;
  deviceScreenDensityDpi: number;
  navigationBarHeight: number;
  statusBarHeight: number;
  statusBarColor: string;
  screenOrientation: "portrait" | "landscape";
};

export type PermissionInfo = {
  granted: boolean;
};

export type AppInfo = {
  versionName: string;
  versionCode: string;
  channel: string;
};

export type NetworkHeaderParams = {
  "X-Device-Brand": string;
  "X-Device-Model": string;
  "X-Screen-Width": string;
  "X-Screen-Height": string;
  "X-OS-Type": string;
  "X-OS-Version": string;
  "X-Version-Name": string;
  "X-Version-Code": string;
  "X-Channel": string;
  "X-Timestamp": string;
  "X-Device-Id": string;
  "X-Network-Type": string;
  Authorization: string;
  userTypeId: string;
  organizationId: string;
  "X-App-Name": string;
};
