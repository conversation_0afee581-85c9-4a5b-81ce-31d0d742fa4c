// apps/stu/app/ui-kit/progress-bar-demo2/ProgressBar.tsx
"use client";

import { computed } from "@preact-signals/safe-react";
import { AnimatePresence, motion } from "framer-motion";
import Lottie, { LottieComponentProps } from "lottie-react";
import Image from "next/image";
import React, { useEffect, useMemo, useState } from "react";

// 音频文件 CDN 路径配置
const getBaseUrl = () => {
  const env = process.env.NODE_ENV;
  if (env === "development") {
    return "";
  } else if (env === "production") {
    // 生产环境使用 CDN 路径
    return "https://static.xiaoluxue.com/stu/_next/public";
  } else {
    // 其他所有环境使用 CDN 路径
    return "https://static.test.xiaoluxue.cn/stu/_next/public";
  }
};

// 音频文件路径配置 - 使用字符串路径而不是直接导入
const audioFiles = {
  correct: `${getBaseUrl()}/audios/single-correct.wav`,
  incorrect: `${getBaseUrl()}/audios/incorrect.wav`,
  continuous: `${getBaseUrl()}/audios/continuous-correct.wav`,
};

// NOTE: The following two imports are placeholders.
// You should replace them with your actual project paths.
import { EnhancedCanvasText } from "@repo/core/components/EnhancedCanvasText";
import ProgressBarTextBackground from "@repo/core/public/assets/stu-exercise/images/progress-bar-text-background.png";

// Lottie animation imports
import allCorrectAnimation from "@repo/core/public/assets/stu-exercise/lottie/all-correct-blocks.json";
import continuousCorrectAnimation from "@repo/core/public/assets/stu-exercise/lottie/continuous-correct-blocks.json";
import fullScreenAnimation from "@repo/core/public/assets/stu-exercise/lottie/full-screen-block.json";
import singleCorrectAnimation from "@repo/core/public/assets/stu-exercise/lottie/single-correct-circle.json";
import { cn } from "@repo/ui/lib/utils";
import {
  ANIMATION_CONFIG,
  animationQueue,
  ProgressAnimationMode,
} from "./data";

//================================================================
// 1. Type Definitions
//================================================================
// 定义进度显示模式类型
export type ProgressDisplayMode = "number" | "progress";

//================================================================
// 2. Audio Player
//================================================================
const audioCache: Map<string, HTMLAudioElement> = new Map();
const defaultAudioFiles = {
  continuous_2_4: audioFiles.correct,
  continuous_4_8: audioFiles.continuous,
  continuous_8_plus: audioFiles.continuous,
  all_correct: audioFiles.continuous,
  incorrect: audioFiles.incorrect,
};

const playAudioEffect = (type: keyof typeof defaultAudioFiles) => {
  const audioUrl = defaultAudioFiles[type];
  if (!audioUrl) return;
  try {
    let audio = audioCache.get(audioUrl);
    if (!audio) {
      audio = new Audio(audioUrl);
      audio.volume = 0.7;
      audioCache.set(audioUrl, audio);
    }
    audio.currentTime = 0;
    audio
      .play()
      .catch((error) => console.warn(`[Audio] Play failed: ${type}`, error));
  } catch (error) {
    console.warn(`[Audio] Load failed: ${type}`, error);
  }
};

const handleAnimationComplete = () => {
  const state = animationQueue.value;
  if (!state.currentItem) return;
  state.currentItem.onComplete?.();
  animationQueue.value = { ...state, currentItem: null };
  processQueue();
};

const processQueue = () => {
  const state = animationQueue.value;
  if (state.currentItem || state.queue.length === 0) return;

  const [nextItem] = state.queue;
  const remainingQueue = state.queue.slice(1);
  animationQueue.value = {
    queue: remainingQueue,
    currentItem: nextItem || null,
  };

  const config = nextItem?.mode && ANIMATION_CONFIG[nextItem.mode];
  if (config?.audioType) playAudioEffect(config.audioType);

  setTimeout(handleAnimationComplete, config?.duration || 0);
};

const enqueueAnimation = (
  mode: ProgressAnimationMode,
  text?: string,
  onComplete?: () => void
) => {
  const id = `anim_${Date.now()}`;
  animationQueue.value = {
    ...animationQueue.value,
    queue: [...animationQueue.value.queue, { id, mode, text, onComplete }],
  };

  processQueue();
};

//================================================================
// 4. Reactive State Signals
//================================================================
const currentAnimation = computed(() => animationQueue.value.currentItem);
const currentEffects = computed(() => {
  const mode = currentAnimation.value?.mode;
  return mode ? ANIMATION_CONFIG[mode].effects : [];
});

const showEndCircleBurst = computed(() =>
  currentEffects.value.includes("endCircleBurst")
);
const showLightningWrap = computed(() =>
  currentEffects.value.includes("lightningWrap")
);
const showFullBarBlast = computed(() =>
  currentEffects.value.includes("fullBarBlast")
);
const showFullScreenConfetti = computed(() =>
  currentEffects.value.includes("fullScreenConfetti")
);
const showStreakBlocks = computed(() =>
  currentEffects.value.includes("streakBlocks")
);

const explosionTextToShow = computed(() => currentAnimation.value?.text || "");
const showExplosionText = computed(() => !!explosionTextToShow.value);

//================================================================
// 5. Internal View Components
//================================================================
const DelayLottie = React.memo(
  ({
    animationData,
    loop,
    className,
    rendererSettings,
  }: {
    animationData: Record<string, unknown>;
    loop: boolean;
    className: string;
    rendererSettings?: LottieComponentProps["rendererSettings"];
  }) => {
    const [play, setPlay] = useState(false);
    useEffect(() => {
      const timer = setTimeout(() => setPlay(true), 0);
      return () => clearTimeout(timer);
    }, []);

    if (!play) return null;
    return (
      <Lottie
        animationData={animationData}
        loop={loop}
        className={className}
        rendererSettings={rendererSettings}
      />
    );
  }
);
DelayLottie.displayName = "DelayLottie";

const BaseProgressBar = React.memo(
  ({ progress, isIncorrect }: { progress: number; isIncorrect: boolean }) => {
    const normalizedProgress = Math.min(Math.max(0, progress), 100);
    const progressWidth = Math.max(20, 280 * (normalizedProgress / 100));
    const textToRender = explosionTextToShow.value;
    const isAnimating = !!currentAnimation.value;

    return (
      <div className="relative h-4 w-[17.5rem] flex-shrink-0 overflow-visible rounded-[1.25rem] bg-[rgba(31,29,27,0.08)]">
        <motion.div
          className="absolute -top-0.5 left-0 z-20 h-5"
          initial={{ width: 0 }}
          animate={{ width: progressWidth }}
          transition={{
            type: "spring",
            stiffness: 800,
            damping: 40,
            mass: 0.5,
            velocity: 0,
          }}
          style={{
            willChange: "width, transform",
            transform: "translateZ(0)",
            backfaceVisibility: "hidden",
          }}
        >
          <motion.div
            className="absolute inset-0 z-10 h-5 rounded-[10px] border-2 border-white/60"
            style={{ borderColor: "rgba(255, 255, 255, 0.6)" }}
            animate={{ opacity: isAnimating ? 1 : 0.4 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          >
            <div
              className="z-1 absolute left-[6px] right-[6px] top-[3px] h-[3px] rounded-full opacity-50 mix-blend-plus-lighter"
              style={{ backgroundColor: "rgba(255, 255, 255, 0.3)" }}
            />
            <motion.div
              className="relative mt-0 h-4 w-full rounded-lg"
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              style={{
                background: `linear-gradient(90deg, #3E98FF 0%, #3EDD72 ${100}%)`,
              }}
            />
          </motion.div>
          <AnimatePresence>
            {showEndCircleBurst.value && (
              <DelayLottie
                animationData={singleCorrectAnimation}
                loop={false}
                className="pointer-events-none absolute right-0 top-1/2 h-20 w-20 -translate-y-1/2 translate-x-[48%]"
              />
            )}
            {showStreakBlocks.value && (
              <DelayLottie
                animationData={continuousCorrectAnimation}
                loop={false}
                className="continuousCorrectAnimation z-15 pointer-events-none absolute left-[-25%] top-1/2 !h-[90px] w-[150%] -translate-y-1/2"
                rendererSettings={{ preserveAspectRatio: "none" }}
              />
            )}
            {showLightningWrap.value && (
              <motion.div
                key="lightningWrap"
                className="pointer-events-none absolute left-[-10%] top-[-60%] z-[-1] h-[220%] w-[120%]"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: [0, 1, 0], scale: 1 }}
                transition={{
                  duration: 1.0,
                  times: [0, 0.5, 1],
                  ease: "easeInOut",
                }}
              >
                <Image
                  src="/images/lightning.webp"
                  alt="Lightning Wrap"
                  width={50}
                  height={50}
                  unoptimized
                  className="h-full w-full"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
        {showFullBarBlast.value && (
          <DelayLottie
            animationData={allCorrectAnimation}
            loop={false}
            className="z-15 pointer-events-none absolute left-[-25%] top-1/2 w-[150%] -translate-y-1/2"
          />
        )}
        {showFullScreenConfetti.value && (
          <DelayLottie
            animationData={fullScreenAnimation}
            loop={false}
            className="z-15 pointer-events-none fixed inset-0 flex items-center justify-center"
          />
        )}
        <AnimatePresence>
          {showExplosionText.value && (
            <motion.div
              className="absolute right-0 top-1/2 -translate-y-[70%] translate-x-[100%] rotate-[-10deg]"
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: [0, 1.3, 1, 1, 0], opacity: [0, 1, 1, 1, 0] }}
              transition={{
                duration: 1.0,
                times: [0, 0.2, 0.3, 0.8, 1.0],
                ease: "easeInOut",
              }}
            >
              {/* 如果是错误图片透明度为 0 */}
              <div
                className={cn(
                  "absolute left-1/2 top-1/2 z-[-1] h-[110%] w-[110%] shrink-0 -translate-x-[47%] -translate-y-[45%] opacity-100",
                  isIncorrect ? "opacity-0" : ""
                )}
              >
                <Image
                  className="h-full w-full"
                  src={ProgressBarTextBackground}
                  alt="ProgressBarTextBackground"
                />
              </div>

              <EnhancedCanvasText
                className="-skew-x-[5deg]"
                text={textToRender}
                fontSize={20}
                fontWeight={900}
                color="#0DA7D6"
                strokeColor="rgba(255, 255, 255, 0.60)"
                strokeWidth={4}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }
);
BaseProgressBar.displayName = "BaseProgressBar";

//================================================================
// 6. Main Component
//================================================================
interface ProgressBarProps {
  progress: number;
  isCorrect?: boolean;
  isIncorrect?: boolean;
  correctComboCount?: number;
  isAllCorrect?: boolean;
  explosionText?: string;
  onAnimationComplete?: () => void;

  // 预览模式
  isPreviewMode?: boolean;
  displayMode?: "number" | "progress";
  currentIndex?: number;
  totalCount?: number;
  className?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  isCorrect = false,
  isIncorrect = false,
  correctComboCount = 0,
  isAllCorrect = false,
  explosionText,
  onAnimationComplete,

  // 预览模式
  isPreviewMode = false,
  displayMode = "number",
  currentIndex = 0,
  totalCount = 1,
  className = "",
}) => {
  // 🔥 根据显示模式决定渲染方式
  // 预览模式下：根据 displayMode 决定显示进度条还是数字
  // 非预览模式下：始终显示进度条
  const shouldShowNumber = isPreviewMode && displayMode === "number";

  const animationMode: ProgressAnimationMode = useMemo(() => {
    if (isAllCorrect) return "all_correct";
    if (correctComboCount >= 8) return "continuous_8_plus";
    if (correctComboCount >= 4) return "continuous_4_8";
    if (correctComboCount >= 2) return "continuous_2_4";
    if (correctComboCount >= 1 || isCorrect) return "single_correct";
    if (isIncorrect) return "incorrect";
    return "default";
  }, [isCorrect, isIncorrect, correctComboCount, isAllCorrect]);

  console.log(`animationMode`, {
    animationMode,
    isCorrect,
    isIncorrect,
    correctComboCount,
    isAllCorrect,
  });

  useEffect(() => {
    if (animationMode === "default") return;
    if (isIncorrect || isCorrect) {
      enqueueAnimation(animationMode, explosionText, onAnimationComplete);
    }
  }, [
    isCorrect,
    isIncorrect,
    animationMode,
    explosionText,
    onAnimationComplete,
  ]);

  // 数字模式：显示 "当前/总数" 格式
  if (shouldShowNumber) {
    const displayCurrent =
      typeof currentIndex === "number" ? currentIndex + 1 : 1;
    const displayTotal = totalCount || 1;

    return (
      <div
        className={`preview-progress-display flex items-center ${className}`}
      >
        <span className="text-text-4 text-sm font-medium">
          {displayCurrent} / {displayTotal}
        </span>
      </div>
    );
  }

  return <BaseProgressBar progress={progress} isIncorrect={isIncorrect} />;
};
