import { signal } from "@preact-signals/safe-react";

export type EffectType =
  | "endCircleBurst"
  | "lightningWrap"
  | "fullBarBlast"
  | "fullScreenConfetti"
  | "streakBlocks";

export type AudioEffectType =
  | "continuous_2_4"
  | "continuous_4_8"
  | "continuous_8_plus"
  | "all_correct"
  | "incorrect";

export type ProgressAnimationMode =
  | "static"
  | "default"
  | "single_correct"
  | "continuous_2_4"
  | "continuous_4_8"
  | "continuous_8_plus"
  | "all_correct"
  | "incorrect";

export interface AnimationItem {
  id: string;
  mode: ProgressAnimationMode;
  text?: string;
  onComplete?: () => void;
}
export const ANIMATION_CONFIG: Record<
  ProgressAnimationMode,
  { effects: EffectType[]; duration: number; audioType: AudioEffectType | null }
> = {
  static: { effects: [], duration: 0, audioType: null },
  default: { effects: [], duration: 0, audioType: null },
  single_correct: {
    effects: ["endCircleBurst"],
    duration: 1200,
    audioType: "continuous_2_4",
  },
  continuous_2_4: {
    effects: ["endCircleBurst"],
    duration: 1200,
    audioType: "continuous_2_4",
  },
  continuous_4_8: {
    effects: ["endCircleBurst", "lightningWrap"],
    duration: 1300,
    audioType: "continuous_4_8",
  },
  continuous_8_plus: {
    effects: ["endCircleBurst", "lightningWrap", "streakBlocks"],
    duration: 1400,
    audioType: "continuous_8_plus",
  },
  all_correct: {
    effects: ["lightningWrap", "fullBarBlast", "fullScreenConfetti"],
    duration: 1400,
    audioType: "all_correct",
  },
  incorrect: { effects: [], duration: 1200, audioType: "incorrect" },
};


export const animationQueue = signal<{
  queue: AnimationItem[];
  currentItem: AnimationItem | null;
}>({ queue: [], currentItem: null });