// packages/core/src/exercise/components/ProgressBar/useProgressBar.ts
import React, { useCallback, useState } from "react";
import { ANIMATION_CONFIG } from "./data";

interface HandleProgressParams {
    type: "correct" | "incorrect";
    text: string;
    progress: number;
    correctComboCount: number;
    isAllCorrect: boolean;
}

export const useProgressBar = () => {
    const [progress, setProgress] = useState(0);
    const [correctComboCount, setCorrectComboCount] = useState(0);
    const [isCorrect, setIsCorrect] = useState(false);
    const [isIncorrect, setIsIncorrect] = useState(false);
    const [isAllCorrect, setIsAllCorrect] = useState(false);
    const [explosionText, setExplosionText] = useState("");
    const [isAnimating, setIsAnimating] = useState(false);

    // ✨ 核心变更：创建一个统一的 handleProgress 函数
    const handleProgress = useCallback(
        ({
            type,
            text,
            progress,
            correctComboCount,
            isAllCorrect,
        }: HandleProgressParams) => {
            if (isAnimating) return;
            setIsAnimating(true);

            // 1. 更新所有核心数据状态
            React.startTransition(() => {
                setProgress(progress);
                setCorrectComboCount(correctComboCount);
                setIsAllCorrect(isAllCorrect);
                setExplosionText(text);

                // 2. 根据类型设置动画触发器（isCorrect 或 isIncorrect）
                if (type === "correct") {
                    setIsCorrect(true);
                } else {
                    setIsIncorrect(true);
                }
            });

            // 3. 根据类型计算动画时长，并设置延时来重置状态
            const animationDuration =
                type === "correct"
                    ? isAllCorrect
                        ? ANIMATION_CONFIG.all_correct.duration
                        : ANIMATION_CONFIG.single_correct.duration
                    : ANIMATION_CONFIG.incorrect.duration;

            setTimeout(() => {
                // 重置对应的动画触发器
                if (type === "correct") {
                    React.startTransition(() => setIsCorrect(false));
                } else {
                    React.startTransition(() => setIsIncorrect(false));
                }
                setIsAnimating(false);
            }, animationDuration);
        },
        [isAnimating] // 依赖项现在只关心 isAnimating
    );

    const reset = useCallback(() => {
        React.startTransition(() => {
            setProgress(0);
            setCorrectComboCount(0);
            setIsCorrect(false);
            setIsIncorrect(false);
            setIsAllCorrect(false);
            setExplosionText("");
            setIsAnimating(false);
        });
    }, []);

    const progressBarProps = {
        progress,
        isCorrect,
        isIncorrect,
        correctComboCount,
        isAllCorrect,
        explosionText,
    };

    // ✨ 控制器现在只暴露 handleProgress 和 reset，更加简洁
    const controllers = {
        handleProgress,
        reset,
        isAnimating,
    };

    return { progressBarProps, ...controllers } as const;
};