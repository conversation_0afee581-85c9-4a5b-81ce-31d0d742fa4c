import { FC } from "react";
// import { MathJaxConfig } from "../components/math-jax-config";
import { GuideCore } from "./components/guide-core";
import {
  GuideViewProvider,
  GuideViewProviderProps,
} from "./context/guide-view-context";

export const GuideView: FC<Omit<GuideViewProviderProps, "children">> = (
  props
) => {
  return (
    // <MathJaxConfig> MathContent使用katex渲染了
    <GuideViewProvider {...props}>
      <GuideCore />
    </GuideViewProvider>
    // </MathJaxConfig>
  );
};
