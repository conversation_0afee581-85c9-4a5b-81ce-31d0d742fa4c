"use client";
import {
  createContext,
  FC,
  ReactNode,
  RefObject,
  useCallback,
  useContext,
  useRef,
} from "react";

import {
  signal,
  Signal,
  useComputed,
  useSignal,
} from "@preact-signals/safe-react";
import { MergedReference, Reference } from "@repo/core/types/data/comment";
import {
  GuideMode,
  GuideTheme,
  GuideWidgetData,
} from "@repo/core/types/data/widget-guide";
import { useGuideThemeViewmodel } from "../viewmodels/guide-theme-viewmodel";

type SelectedLine = {
  id: string;
  frame: number;
  x: number;
  y: number;
};

type GuideViewContextType = {
  client?: "stu" | "aipt" | "";
  guideMode: GuideMode;
  title?: string;
  index: number;
  totalGuideCount: number;
  data: GuideWidgetData;
  theme: GuideTheme;
  showSubtitle: boolean;
  onPan?: () => void;
  selectedLineId: string;
  selectedLine: Signal<SelectedLine | null>;
  onLineSelected: (frame: number) => void;
  handleLineClick: (
    e: React.MouseEvent<HTMLDivElement>,
    lineId: string,
    frame: number
  ) => void;
  refContainer: RefObject<HTMLDivElement | null>;
  selectable: boolean;
  scrollable: Signal<boolean>;
  commentRef?: Signal<HTMLDivElement | null>;
  lineIdInRange?: string;
  referenceList?: Reference[];
  onClickReference?: (
    reference: MergedReference[string][string][number]
  ) => void;
};

const GuideViewContext = createContext<GuideViewContextType>(
  {} as GuideViewContextType
);

const useGuideViewContext = () => useContext(GuideViewContext);

interface GuideViewProviderProps {
  client?: "stu" | "aipt" | "";
  guideMode?: GuideMode;
  title?: string;
  index?: number;
  totalGuideCount?: number;
  data: GuideWidgetData;
  theme?: GuideTheme;
  showSubtitle?: boolean;
  onPan?: () => void;
  onLineClick?: (frame: number) => void;
  children: ReactNode;
  selectable?: boolean;
  scrollable?: Signal<boolean>;
  commentRef?: Signal<HTMLDivElement | null>;
  lineIdInRange?: string;
  referenceList?: Reference[];
  onClickReference?: (
    reference: MergedReference[string][string][number]
  ) => void;
}

const GuideViewProvider: FC<GuideViewProviderProps> = ({
  children,
  ...props
}) => {
  const {
    client = "",
    guideMode = GuideMode.follow,
    title,
    index = 0,
    totalGuideCount = 0,
    data,
    theme: themeConfig,
    showSubtitle,
    onPan,
    onLineClick,
    selectable = true,
    scrollable = signal(true),
    commentRef,
    lineIdInRange,
    referenceList,
    onClickReference,
  } = props;

  const theme = useGuideThemeViewmodel(themeConfig);
  const refContainer = useRef<HTMLDivElement>(null);
  const selectedLine = useSignal<SelectedLine | null>(null);
  const selectedLineId = useComputed(() => selectedLine.value?.id || "");

  const handleLineClick = useCallback(
    (e: React.MouseEvent<HTMLDivElement>, id: string, frame: number) => {
      if (!selectable) return;
      selectedLine.value = {
        id,
        frame,
        x: e.clientX,
        y: e.clientY,
      };
    },
    [selectable, selectedLine]
  );

  const onLineSelected = useCallback(
    (frame: number) => {
      onLineClick?.(frame);
    },
    [onLineClick]
  );

  const value = {
    client,
    guideMode,
    title,
    index,
    totalGuideCount,
    data,
    theme,
    showSubtitle: showSubtitle ?? true,
    onPan,
    refContainer,
    selectedLine,
    selectedLineId: selectedLineId.value,
    onLineSelected,
    handleLineClick,
    selectable,
    scrollable,
    commentRef,
    lineIdInRange,
    referenceList,
    onClickReference,
  };
  return <GuideViewContext value={value}>{children}</GuideViewContext>;
};

export {
  GuideViewContext,
  GuideViewProvider,
  useGuideViewContext,
  type GuideViewContextType,
  type GuideViewProviderProps,
};
