import Style1Bg from "@repo/core/assets/guide-line/style1-bg.svg";
import Style1Point from "@repo/core/assets/guide-line/style1-point.svg";
import H3Icon1 from "@repo/core/assets/guide-theme/h3-icons/1.svg";
import H3Icon10 from "@repo/core/assets/guide-theme/h3-icons/10.svg";
import H3Icon2 from "@repo/core/assets/guide-theme/h3-icons/2.svg";
import H3Icon3 from "@repo/core/assets/guide-theme/h3-icons/3.svg";
import H3Icon4 from "@repo/core/assets/guide-theme/h3-icons/4.svg";
import H3Icon5 from "@repo/core/assets/guide-theme/h3-icons/5.svg";
import H3Icon6 from "@repo/core/assets/guide-theme/h3-icons/6.svg";
import H3Icon7 from "@repo/core/assets/guide-theme/h3-icons/7.svg";
import H3Icon8 from "@repo/core/assets/guide-theme/h3-icons/8.svg";
import H3Icon9 from "@repo/core/assets/guide-theme/h3-icons/9.svg";

import playingData from "@repo/core/assets/guide-theme/playing.json";
import IconUL from "@repo/core/assets/images/line-ul-dot.svg";
import { MathContent } from "@repo/core/components/math-content/math-content";
import RoughNotion from "@repo/core/components/notion/rough-notion";
import { Picture as PictureData } from "@repo/core/types/data/base";
import { MergedReference } from "@repo/core/types/data/comment";
import { LineTexture as LineTextureData } from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import Lottie from "lottie-react";
import Image, { StaticImageData } from "next/image";
import { FC, ReactNode, useEffect, useMemo, useState } from "react";
import { useCurrentFrame } from "remotion";

// 定义各类 LaTeX 匹配规则
const rules: { regex: RegExp; type: string }[] = [
  { regex: /(\\\[[\s\S]*?\\\])/, type: "block" }, // 匹配 \[ ... \]
  { regex: /(\\\([\s\S]*?\\\))/, type: "line" }, // 匹配 \( ... \)
  { regex: /(\$\$[\s\S]*?\$\$)/, type: "block" }, // 匹配 $$ ... $$
  { regex: /(\$[\s\S]*?\$)/, type: "block" }, // 匹配 $ ... $
];

const getIsLatex = (content: string, type: "block" | "line") => {
  const contentStr = content.trim();
  let isLatex = false;
  for (const rule of rules) {
    if (rule.regex.test(contentStr) && rule.type === type) {
      isLatex = true;
      break;
    }
  }
  return isLatex;
};

/**
 * 用于封面和封底的通栏长图
 */
export const Cover: FC<{ src: string | StaticImageData | undefined }> = ({
  src,
}) => {
  if (!src) {
    return null;
  }

  return (
    <Image
      src={src}
      alt="last-plate"
      priority
      className="h-auto"
      width={1000}
      height={261}
    />
  );
};

export const TextureWithOutFrame: FC<{
  lineId: string;
  data: LineTextureData;
  className?: string;
  styleText?: number;
}> = ({ data, className, styleText }) => {
  const { tag, content, notation } = data;

  const inner = <MathContent>{content}</MathContent>;
  const isBlockLatex = useMemo(() => {
    return getIsLatex(content, "block");
  }, [content]);
  const isLineLatex = useMemo(() => {
    return getIsLatex(content, "line");
  }, [content]);

  return (
    <span
      key={`texture-${content}-${styleText || 0}`}
      // onClick={handleClick}
      className={cn(
        "font-resource-han-rounded inline",
        tag === "bold" && "font-bold",
        className
      )}
    >
      {notation && notation.type ? (
        <RoughNotion
          isLatex={isBlockLatex}
          type={notation.type}
          color={notation.color}
          strokeWidth={notation.strokeWidth}
          show={true}
          padding={isLineLatex ? [8, 4] : [2, 4]}
        >
          {inner}
        </RoughNotion>
      ) : (
        inner
      )}
    </span>
  );
};

export const Texture: FC<{
  rootId?: string;
  lineId?: string;
  id?: string;
  data: LineTextureData;
  className?: string;
  references?: MergedReference[string][string];
  onClick?: (ref: MergedReference[string][string][number]) => void;
}> = ({ data, className, id, lineId, rootId, references, onClick }) => {
  const { tag, content, notation } = data;
  const frame = useCurrentFrame();

  const show = useMemo(() => {
    if (!notation) return false;
    const { type, inFrame } = notation;
    if (["highlight", "underline"].includes(type)) {
      return true;
    }
    return frame >= (inFrame ?? 0);
  }, [frame, notation]);

  const inner = (
    <MathContent
      lineId={lineId}
      textureId={id}
      rootId={rootId}
      references={references}
      onClick={onClick}
    >
      {content.replace(/\/\//g, "/")}
    </MathContent>
  );

  const isBlockLatex = useMemo(() => {
    return getIsLatex(content, "block");
  }, [content]);
  const isLineLatex = useMemo(() => {
    return getIsLatex(content, "line");
  }, [content]);

  const padding: [number, number] = useMemo(() => {
    return isLineLatex ? [8, 4] : [2, 4]
  }, [isLineLatex]);
  return (
    <span
      // onClick={handleClick}
      className={cn(
        "font-resource-han-rounded relative inline",
        tag === "bold" && "font-bold",
        className
      )}
    >
      {notation && notation.type ? (
        <RoughNotion
          key={notation.type}
          isLatex={isBlockLatex}
          type={notation.type}
          color={notation.color}
          strokeWidth={notation.strokeWidth}
          show={show}
          animationDuration={100}
          padding={padding}
        >
          {inner}
        </RoughNotion>
      ) : (
        inner
      )}
    </span>
  );
};

export const H2: FC<{ content: ReactNode }> = ({ content }) => {
  return (
    <div
      data-name="h2"
      className="mt-2 flex h-12 w-max max-w-80 flex-row items-center justify-end rounded-[43px] bg-amber-200 pl-[70px] pr-[26px]"
    >
      <div className="text-[28px] font-bold leading-9 tracking-widest text-stone-700">
        {content}
      </div>
    </div>
  );
};

// 随机选择一个H3Icon
const icons = [
  H3Icon1,
  H3Icon2,
  H3Icon3,
  H3Icon4,
  H3Icon5,
  H3Icon6,
  H3Icon7,
  H3Icon8,
  H3Icon9,
  H3Icon10,
];
export const H3: FC<{ content: ReactNode }> = ({ content }) => {
  const [idx, setIdx] = useState(0);
  const Icon = useMemo(() => icons[idx], [idx]);
  useEffect(() => {
    setIdx(Math.floor(Math.random() * icons.length));
  }, []);

  return (
    <h3
      data-name="h3"
      className="text-h3 tracking-guide relative pl-9 font-bold leading-tight text-stone-900"
    >
      <Icon className="absolute left-0 top-1 size-7 min-w-7" />
      {content}
    </h3>
  );
};

export const H4: FC<{
  content: ReactNode;
  iconName?: string;
}> = ({ content }) => {
  return (
    <h4
      data-name="h4"
      className="flex w-auto flex-row items-center justify-start"
    >
      <div className="text-h4 tracking-guide font-bold leading-normal text-stone-900">
        {content}
      </div>
    </h4>
  );
};

const style2ClassNames = [
  {
    marker: "bg-[#FFAA79]",
    border: "from-[#FFAA79] to-[#FFAA79]/0",
    text: "text-[#BE591F]",
  },
  {
    marker: "bg-[#FFBE4D]",
    border: "from-[#FFBE4D] to-[#FFBE4D]/0",
    text: "text-[#906213]",
  },
  {
    marker: "bg-[#80D0FF]",
    border: "from-[#80D0FF] to-[#80D0FF]/0",
    text: "text-[#5087A7]",
  },
];
export const OL: FC<{
  index: number;
  content: ReactNode;
  styleText?: number;
  userSelectable?: boolean;
  isSelected?: boolean;
}> = ({
  index: idx,
  content,
  styleText = 0,
  userSelectable,
  isSelected = false,
}) => {
  if (styleText === 1) {
    return (
      <div data-name="ol" className="flex items-center gap-5">
        <div className="relative flex-[0_0_auto]">
          <Style1Bg className="w-18 h-30" />
          {idx > 1 && (
            <Style1Point className="h-6.25 absolute left-1/2 top-0 w-2 -translate-x-1/2" />
          )}
          <div className="-translate-1/2 absolute left-1/2 top-1/2 text-xl text-yellow-950">
            {idx}
          </div>
        </div>
        <P
          content={content}
          isSelected={isSelected}
          userSelectable={userSelectable}
        />
      </div>
    );
  }
  if (styleText === 2) {
    const classNames = style2ClassNames[(idx - 1) % style2ClassNames.length]!;
    return (
      <div
        data-name="ol"
        className={cn(
          "flex w-full items-center gap-5",
          idx % 2 === 0 ? "flex-row-reverse" : "flex-row"
        )}
      >
        <div
          className={cn(
            "relative flex size-32 flex-[0_0_auto] items-center justify-center rounded-full text-2xl text-white",
            classNames.marker
          )}
        >
          {idx}
        </div>
        <div
          className={cn(
            "relative flex h-32 flex-1 flex-wrap items-center rounded-full border-2 border-solid border-transparent bg-[#FAF8F6] bg-clip-padding px-14",
            classNames.text
          )}
        >
          <div
            className={cn(
              "-z-1 absolute -bottom-0.5 -left-0.5 -right-0.5 -top-0.5 rounded-full to-70%",
              classNames.border,
              idx % 2 === 1 ? "bg-linear-90" : "bg-linear-270"
            )}
          ></div>
          <div
            className={cn(
              "relative",
              userSelectable && "select-text",
              isSelected &&
                "before:bg-text-4/20 before:absolute before:-inset-1 before:rounded-lg"
            )}
          >
            {content}
          </div>
        </div>
      </div>
    );
  }
  return (
    <div
      data-name="ol"
      className="flex w-auto flex-row items-baseline justify-start gap-2"
    >
      <div className="border-1 flex size-6 flex-[0_0_auto] items-center justify-center rounded-sm border-solid border-[#1f1d1b]/10 bg-white text-center">
        {idx}
      </div>
      <P
        content={content}
        isSelected={isSelected}
        userSelectable={userSelectable}
      />
    </div>
  );
};

export const UL: FC<{
  content: ReactNode;
  userSelectable?: boolean;
}> = ({ content, userSelectable = false }) => {
  return (
    <div className="flex w-auto flex-row items-baseline justify-start gap-2">
      <div className="flex size-5 items-center justify-center">
        <IconUL className="text-[8px]" />
      </div>
      <P content={content} userSelectable={userSelectable} />
    </div>
  );
};

export const P: FC<{
  content: ReactNode;
  isSelected?: boolean;
  userSelectable?: boolean;
}> = ({ content, isSelected = false, userSelectable = false }) => {
  return (
    <div
      className={cn(
        "leading-guide tracking-guide relative w-fit text-lg font-medium text-yellow-950",
        userSelectable && "select-text",
        isSelected &&
          "before:absolute before:-inset-1 before:rounded-lg before:bg-[#33302d1c]"
      )}
    >
      {content}
    </div>
  );
};

export const Picture: FC<{
  data: PictureData | undefined;
  userSelectable?: boolean;
  lineId?: string;
  rootId?: string;
  mergedReference?: MergedReference[string][string][number];
  onClick?: (ref: MergedReference[string][string][number]) => void;
  layout?: "vertical" | "horizontal";
  imageRatio?: "16:9" | "1:1" | "9:16";
  styleType?: "style1" | "style2" | "style3" | "style4" | "style5";
}> = ({
  userSelectable = false,
  lineId,
  rootId,
  mergedReference,
  onClick,
  data,
  layout,
  imageRatio,
  styleType,
}) => {
  if (!data || !data.url) {
    return null;
  }

  const { url, width, height, fileType } = data;

  // 如果是 JS 或 HTML 文件，统一使用缩放自适应iframe处理
  if (fileType === "js" || fileType === "html") {
    let iframeSrc = url;

    // 如果是 JS 文件，创建HTML包装器
    if (fileType === "js") {
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <style>
            body {
              margin: 0;
              padding: 20px;
              font-family: Arial, sans-serif;
              background: #f5f5f5;
            }
            .loading {
              text-align: center;
              color: #666;
              padding: 40px;
            }
          </style>
        </head>
        <body>
          <div class="loading">正在加载JS组件...</div>
          <script src="${url}"></script>
          <script>
            // 监听自定义元素注册
            const originalDefine = customElements.define;
            let registeredElements = [];

            customElements.define = function(name, constructor, options) {
              registeredElements.push(name);
              return originalDefine.call(this, name, constructor, options);
            };

            // 多次尝试检测和创建元素
            function tryCreateElement() {
              const loadingDiv = document.querySelector('.loading');

              // 首先尝试新注册的元素
              if (registeredElements.length > 0) {
                const elementName = registeredElements[registeredElements.length - 1];
                const element = document.createElement(elementName);
                document.body.appendChild(element);
                if (loadingDiv) loadingDiv.remove();
                return true;
              }

              // 然后尝试预设名称和常见模式
              const possibleNames = [
                'web-component-class4-subject1',
                'test-interactive',
                'interactive-component',
                'custom-element'
              ];

              for (const name of possibleNames) {
                if (customElements.get(name)) {
                  const element = document.createElement(name);
                  document.body.appendChild(element);
                  if (loadingDiv) loadingDiv.remove();
                  return true;
                }
              }

              return false;
            }

            // 多次尝试，因为JS加载可能需要时间
            let attempts = 0;
            const maxAttempts = 10;

            function attemptCreation() {
              attempts++;

              if (tryCreateElement()) {
                return;
              }

              if (attempts < maxAttempts) {
                setTimeout(attemptCreation, 200);
              } else {
                const loadingDiv = document.querySelector('.loading');
                if (loadingDiv) {
                  loadingDiv.innerHTML = 'JS文件已加载，但未找到自定义元素。请确保JS文件中调用了 customElements.define()。<br>已注册的元素: ' + registeredElements.join(', ');
                }
              }
            }

            // 开始尝试创建元素
            setTimeout(attemptCreation, 100);
          </script>
        </body>
        </html>
      `;

      const blob = new Blob([htmlContent], { type: "text/html" });
      iframeSrc = URL.createObjectURL(blob);
    }
    const isStyle2or3 = styleType === "style2" || styleType === "style3";
    const scale = isStyle2or3 ? 0.25 : 0.33;
    const percent = isStyle2or3 ? "400%" : "300%";

    // 统一的iframe渲染逻辑，适用于所有样式
    return (
      <div className="relative h-full w-full overflow-hidden rounded-lg">
        <iframe
          src={iframeSrc}
          className="absolute inset-0 border-0"
          title={fileType === "js" ? "JS预览" : "HTML预览"}
          sandbox="allow-scripts allow-same-origin"
          style={{
            width: percent,
            height: percent,
            transform: `scale(${scale})`,
            transformOrigin: "0 0",
            overflow: "hidden",
          }}
        />
      </div>
    );
  }

  // 判断是否应该使用 object-cover（样式1和样式5的条件）
  const shouldUseCover =
    styleType === "style1" ||
    styleType === "style5" ||
    (layout === "vertical" && imageRatio === "16:9" && !styleType);

  // 默认图片渲染
  return (
    <div
      className={cn(
        "relative flex h-full w-full flex-col items-center justify-center",
        mergedReference && "underline-offset-7 underline decoration-dotted"
      )}
      onClick={() => mergedReference && onClick?.(mergedReference)}
    >
      <img
        className={cn(
          "h-full w-full",
          shouldUseCover ? "object-cover object-center" : "object-contain",
          userSelectable && "select-all"
        )}
        data-line-id={lineId}
        data-content={url}
        data-root-id={rootId}
        data-type="pic"
        src={url}
        alt="picture"
        width={width}
        height={height}
        // priority
        style={{
          objectPosition: shouldUseCover ? "center" : "top",
        }}
      />

      {mergedReference && (
        <span className="text-xxs absolute bottom-0 right-0 flex items-center justify-center rounded-sm bg-[#1F1D1B66] px-1 text-white">
          {mergedReference.commentCount}
        </span>
      )}
    </div>
  );
};

export const PlayingLine: FC<{ className?: string }> = ({ className }) => {
  return (
    <div
      className={cn(
        "absolute bottom-0 left-0 flex h-0 w-full flex-row items-center gap-0.5",
        className
      )}
    >
      <Lottie className="h-6 w-6" animationData={playingData} loop={true} />
      <p className="h-0 flex-1 opacity-70 outline-[1px] outline-orange-300" />
    </div>
  );
};
