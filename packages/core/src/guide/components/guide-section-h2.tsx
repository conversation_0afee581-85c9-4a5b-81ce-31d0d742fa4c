import { cn } from "@repo/ui/lib/utils";
import { useMemo } from "react";
import { useGuideViewContext } from "../context/guide-view-context";
import { toH3Parts } from "../utils/h3-parts";
import { Cover, GuideTitle } from "./guide-line";
import { GuideSectionH3 } from "./guide-section-h3";

export const GuideSectionH2 = ({ sketchProps = {} }) => {
  const { title, index, totalGuideCount, data, theme } = useGuideViewContext();
  const { content } = data;
  const h3Parts = useMemo(() => toH3Parts(content), [content]);

  return (
    <section
      data-name="section::h2"
      className="relative flex h-max w-4/5 flex-col items-center"
    >
      <div className="min-w-(--width-guide)">
        <GuideTitle title={title} />
        <div className="pb-18 pl-8">
          <div
            className={cn(
              "gap-18 w-section-h3 flex flex-col rounded-[20px] pb-10 pt-[60px]",
              index == 0 && "bg-[#FFEDD8] px-8",
              index == totalGuideCount - 1 && "bg-[#FFEDD8] px-8"
            )}
          >
            {/* 改为H3Part */}
            {h3Parts.map((part, index) => (
              <GuideSectionH3
                data={part}
                key={index}
                sketchProps={sketchProps}
              />
            ))}
          </div>
        </div>
      </div>
      {index === totalGuideCount - 1 && (
        <Cover className="w-full" name={theme.backCover ?? ""} />
      )}
    </section>
  );
};
