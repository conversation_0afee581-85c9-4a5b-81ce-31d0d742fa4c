import { DrawElement } from "@repo/core/types/data/widget-guide";
import { Point, path2D } from "@repo/react-sketch-canvas";
import React, { FC, useMemo } from "react";
import { v4 as uuidv4 } from "uuid";
import { useGuideViewContext } from "../context/guide-view-context";
// 荧光笔完全显示后延迟2秒后开始淡出
const highlighterDelayTime = 2;
// 荧光笔淡出时间
const highlighterFadeoutTime = 0.3;

// 为每个DrawElement添加随机ID的类型
interface DrawElementWithId extends DrawElement {
  id: string;
  maskId?: string;
}

interface PartFrames {
  inFrame: number;
  outFrame: number;
}

const splitPathWithFrame = (
  paths: Point[],
  frame: number,
  inFrame: number,
  outFrame: number
) => {
  const totalFrames = outFrame - inFrame || 1;
  const currentFrames = frame - inFrame || 1;
  const pointCount = Math.floor(
    (paths?.length ? paths.length : 0) * (currentFrames / totalFrames)
  );
  return paths.slice(0, pointCount);
};
// 使用二分查找优化查找效率，从 O(n) 降低到 O(log n)
const findPreOutFrame = (
  rightFrame: number,
  leftFrame: number,
  list: DrawElement[]
): number | null => {
  if (list.length === 0) return rightFrame;

  let left = 0;
  let right = list.length - 1;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const targetOutFrame = list[mid]?.outFrame!;

    if (targetOutFrame > leftFrame && targetOutFrame < rightFrame) {
      return targetOutFrame;
    } else {
      if (targetOutFrame < leftFrame) {
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }
  }

  return null;
};
const processDrawElements = (
  data: DrawElement[],
  fps: number = 30,
  partFrames: PartFrames
): DrawElementWithId[] => {
  let result: DrawElementWithId[] = [];
  let preIndex = 0;
  const { inFrame: minInFrame, outFrame: maxOutFrame } = partFrames;
  const frameMap = new Map<number, DrawElementWithId[]>();

  // 预处理：创建按outFrame排序的数组，用于优化查找
  const sortedByOutFrame = data.sort((a, b) => a.outFrame - b.outFrame);

  data.forEach((item, i) => {
    const newItem: DrawElementWithId = {
      ...item,
      id: item?.id || uuidv4(),
    };

    // 暂停场景下画的普通画笔播放时间调整逻辑
    if (
      newItem.drawMode &&
      !newItem.endFrame &&
      newItem.inFrame === newItem.outFrame
    ) {
      const { inFrame } = newItem;
      if (inFrame) {
        if (frameMap.has(inFrame)) {
          frameMap.get(inFrame)!.push(newItem);
        } else {
          frameMap.set(inFrame, [newItem]);
        }
      }
    }

    result.push(newItem);
    if (!newItem.drawMode) {
      for (; preIndex < i; preIndex++) {
        // 荧光笔路径不受橡皮擦影响
        if (result[preIndex]?.endFrame) continue;
        if (!result[preIndex]?.drawMode) continue;
        result[preIndex]!.maskId = newItem.id;
      }
    }
  });
  for (let [frame, items] of frameMap) {
    const pathsLength = items.reduce(
      (total: number, item: DrawElementWithId) => {
        return total + (item.paths?.length || 0);
      },
      0
    );

    // 固定往前1s，所以是1*fps
    let frameOffset = 1 * fps;
    const preOutFrame =
      findPreOutFrame(frame, frame - frameOffset, sortedByOutFrame) ||
      frame - frameOffset;
    frameOffset = Math.min(frameOffset, frame - preOutFrame);
    if (frameOffset > 0) {
      const step = pathsLength / frameOffset;
      let preFrame = Math.max(minInFrame, frame - frameOffset);
      items.forEach((item: DrawElementWithId) => {
        item.inFrame = Math.min(preFrame, frame);
        item.outFrame = Math.min(
          frame,
          maxOutFrame,
          preFrame + Math.floor((item.paths?.length || 0) / step)
        );
        preFrame = item.outFrame + 1;
      });
    }
  }

  frameMap.clear();
  return result;
};
// 0709优化：
// 1、增加 memo 相关逻辑，frame 大于 outFrame 不再更新
// 2、svg path 渲染逻辑改用计算当前渲染需要的d内容，而不是计算偏移量
const HandDrawn: FC<{
  data: DrawElement[];
  frame: number;
  partFrames: PartFrames;
}> = ({ data, frame, partFrames }) => {
  const { data: guideWidgetData } = useGuideViewContext();
  const { avatar } = guideWidgetData;
  const { fps } = avatar;

  // 为每个数据项生成随机ID
  const dataWithIds: DrawElementWithId[] = useMemo(() => {
    return processDrawElements(data, fps, partFrames);
  }, [data, fps, partFrames]);

  const realDataWithIds = dataWithIds.filter((item) => {
    const { endFrame, inFrame, svgPath } = item;
    if (!svgPath) return false;
    if (frame < inFrame) return false;
    if (endFrame !== undefined && frame > endFrame) return false;
    return true;
  });

  const eraserPaths = realDataWithIds.filter((path) => !path.drawMode);

  let currentGroup: number = 0;
  const pathGroups = realDataWithIds.reduce<DrawElementWithId[][]>(
    (arrayGroup: DrawElementWithId[][], path: DrawElementWithId) => {
      if (!path.drawMode) {
        currentGroup += 1;
        return arrayGroup;
      }

      // 跳过荧光笔路径，它们会被单独渲染
      if ((path as any).endFrame !== undefined) {
        return arrayGroup;
      }

      if (!arrayGroup[currentGroup]) {
        arrayGroup[currentGroup] = [];
      }

      arrayGroup[currentGroup]!.push(path);
      return arrayGroup;
    },
    [[]]
  );

  // 分离荧光笔路径和普通绘制路径，用于特殊渲染
  const highlighterPaths = realDataWithIds.filter(
    (path) => path.drawMode && (path as any).endFrame !== undefined
  );

  return (
    <div
      data-name="hand-drawn"
      className="z-100 pointer-events-none absolute left-0 top-0 h-full w-full"
    >
      <svg
        width="100%"
        height="100%"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        id="react-sketch-canvas"
        style={{ width: "100%", height: "100%" }}
      >
        {/* 橡皮擦 path 只在 defs 里生成，不直接渲染 */}
        <g id="react-sketch-canvas__eraser-stroke-group" display="none">
          <rect
            id="react-sketch-canvas__mask-background"
            x="0"
            y="0"
            width="100%"
            height="100%"
            fill="white"
          />
          {eraserPaths.map((item) => (
            <EraserPath key={item.id} data={item} frame={frame} />
          ))}
        </g>
        <defs>
          {/* 为每个正常线条生成独立的 mask，只包含该线条绘制时间之后的橡皮擦路径 */}
          {eraserPaths.map((_, i) => (
            <mask
              id={`react-sketch-canvas__eraser-mask-${_.id}`}
              key={`react-sketch-canvas__eraser-mask-${_.id}`}
              maskUnits="userSpaceOnUse"
            >
              <use href={`#react-sketch-canvas__mask-background`} />
              {Array.from(
                { length: eraserPaths.length - i },
                (_, j) => j + i
              ).map((k) => (
                <use
                  key={eraserPaths[k]?.id}
                  href={`#react-sketch-canvas__eraser-${eraserPaths[k]?.id}`}
                />
              ))}
            </mask>
          ))}
        </defs>
        {/* 每条正常线都用自己的 mask 包裹 */}
        {pathGroups.map((pathGroup, i) => (
          <g
            id={`react-sketch-canvas__stroke-group-${i}`}
            key={`react-sketch-canvas__stroke-group-${i}`}
            mask={`url(#react-sketch-canvas__eraser-mask-${pathGroup?.[0]?.maskId || ""})`}
          >
            <NormalLineItems paths={pathGroup} frame={frame} />
          </g>
        ))}
        {/* 荧光笔路径特殊渲染：不受橡皮擦影响 */}
        {highlighterPaths.length > 0 && (
          <g id={`react-sketch-canvas__highlighter-group`}>
            {highlighterPaths.map((path) => (
              <NormalLineItem
                key={`react-sketch-canvas__highlighter-${path.id}`}
                data={path}
                frame={frame}
                fps={fps}
              />
            ))}
          </g>
        )}
      </svg>
    </div>
  );
};

// 正常线条 path
// const NormalLineItem: FC<{
//   data: DrawElementWithId;
//   frame: number;
//   fps?: number;
// }> = memo(
//   ({ data, frame, fps = 30 }) => {
//     const { strokeColor, strokeWidth, id, inFrame, outFrame, endFrame, paths } =
//       data;

//     const progressPaths = splitPathWithFrame(paths, frame, inFrame, outFrame);
//     const progressD = path2D(progressPaths);

//     let opacity = 1;
//     if (endFrame) {
//       const startFadeoutFrame = endFrame - highlighterDelayTime * fps;
//       if (frame > startFadeoutFrame) {
//         opacity = (endFrame - frame) / Math.floor(fps * highlighterFadeoutTime);
//       }
//     }

//     return (
//       <path
//         id={`react-sketch-canvas__${id}`}
//         d={progressD}
//         fill="transparent"
//         stroke={strokeColor}
//         strokeWidth={strokeWidth}
//         strokeLinecap="round"
//         opacity={opacity}
//       />
//     );
//   },
//   (_, nextData) => {
//     // 普通画笔&当前frame超过outFrame不再更新render
//     if (!nextData.data.endFrame && nextData.frame > nextData.data.outFrame + 50)
//       return true;

//     return false;
//   }
// );
const NormalLineItem: FC<{
  data: DrawElementWithId;
  frame: number;
  fps?: number;
}> = ({ data, frame, fps = 30 }) => {
  const { strokeColor, strokeWidth, id, inFrame, outFrame, endFrame, paths } =
    data;

  const progressPaths = splitPathWithFrame(paths, frame, inFrame, outFrame);
  const progressD = path2D(progressPaths);

  let opacity = 1;
  if (endFrame) {
    const startFadeoutFrame = endFrame - highlighterDelayTime * fps;
    if (frame > startFadeoutFrame) {
      opacity = (endFrame - frame) / Math.floor(fps * highlighterFadeoutTime);
    }
  }

  return (
    <path
      id={`react-sketch-canvas__${id}`}
      d={progressD}
      fill="transparent"
      stroke={strokeColor}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      opacity={opacity}
    />
  );
};

NormalLineItem.displayName = "NormalLineItem";

const NormalLineItems: FC<{ paths: DrawElementWithId[]; frame: number }> = ({
  paths,
  frame,
}) => {
  return (
    <React.Fragment>
      {paths.map((item) => (
        <NormalLineItem key={item.id} data={item} frame={frame} />
      ))}
    </React.Fragment>
  );
};

// 橡皮擦 path 只在 defs 里生成
const EraserPath: FC<{ data: DrawElementWithId; frame: number }> = ({ data, frame }) => {
  const { strokeWidth, inFrame, outFrame, id, paths } = data;
  const progressPaths = splitPathWithFrame(paths, frame, inFrame, outFrame);
  const progressD = path2D(progressPaths);
  return (
    <path
      id={`react-sketch-canvas__eraser-${id}`}
      d={progressD}
      fill="none"
      stroke="#000000"
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  );
};
// const EraserPath: FC<{ data: DrawElementWithId; frame: number }> = memo(
//   ({ data, frame }) => {
//     const { strokeWidth, inFrame, outFrame, id, paths } = data;
//     const progressPaths = splitPathWithFrame(paths, frame, inFrame, outFrame);
//     const progressD = path2D(progressPaths);
//     return (
//       <path
//         id={`react-sketch-canvas__eraser-${id}`}
//         d={progressD}
//         fill="none"
//         stroke="#000000"
//         strokeWidth={strokeWidth}
//         strokeLinecap="round"
//         strokeLinejoin="round"
//       />
//     );
//   },
//   (_, nextData) => {
//     // 橡皮擦&当前frame超过outFrame不再更新render
//     if (nextData.frame > nextData.data.outFrame + 50) return true;

//     return false;
//   }
// );

EraserPath.displayName = "EraserPath";

export { HandDrawn };
