import { Layer, LayerItem } from "@repo/core/components/layer";
import { Subtitle } from "@repo/core/guide/components/subtitle";
import { ComponentProps, FC, useEffect, useMemo } from "react";
import { AbsoluteFill, Video } from "remotion";
import { useGuideViewContext } from "../context/guide-view-context";

import { batch, signal, useSignal } from "@preact-signals/safe-react";
import { cn } from "@repo/ui/lib/utils";
import { useThrottledCallback } from "use-debounce";
import { BtnGoto } from "./guide-line";
import { GuideSectionH2 } from "./guide-section-h2";

export const GuideCore: FC<ComponentProps<"div">> = () => {
  const { data, onPan, showSubtitle, refContainer, scrollable, selectedLine } =
    useGuideViewContext();
  const { avatar, subtitles } = data;

  // const tree = useGuideTreeViewmodel(content);

  const isTriggerByUser = useSignal(false);
  const scrollTopOffset = useSignal(0);
  const hasTriggerOnPan = useSignal(false);

  const scrollTop = useMemo(
    () => signal(refContainer.current ? refContainer.current.scrollTop : 0),
    [refContainer]
  );

  const throttledScroll = useThrottledCallback(() => {
    const container = refContainer.current;
    if (!container) return;

    scrollTopOffset.value = container.scrollTop - scrollTop.value;
    // 先处理原有onPan逻辑
    if (
      hasTriggerOnPan.value === false &&
      isTriggerByUser.value === true &&
      Math.abs(scrollTopOffset.value) > 100
    ) {
      onPan?.();
      hasTriggerOnPan.value = true;
    }
  }, 40);

  useEffect(() => {
    if (!refContainer.current) {
      return;
    }
    const container = refContainer.current;

    const handleUserAction = () => {
      isTriggerByUser.value = true;
    };
    const handleTouchStart = () => {
      isTriggerByUser.value = true;
    };
    const handleTouchEnd = () => {
      batch(() => {
        scrollTop.value = container.scrollTop;
        scrollTopOffset.value = 0;
        isTriggerByUser.value = false;
        hasTriggerOnPan.value = false;
      });
    };

    container.addEventListener("wheel", handleUserAction, { passive: true });
    container.addEventListener("touchstart", handleTouchStart, {
      passive: true,
    });
    container.addEventListener("touchend", handleTouchEnd, { passive: true });
    container.addEventListener("scroll", throttledScroll);
    return () => {
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("wheel", handleUserAction);
      container.removeEventListener("touchend", handleTouchEnd);
      container.removeEventListener("scroll", throttledScroll);
    };
  }, [
    onPan,
    scrollTop,
    refContainer,
    isTriggerByUser,
    scrollTopOffset,
    throttledScroll,
    hasTriggerOnPan,
  ]);

  return (
    <AbsoluteFill>
      <div className="guide-view relative flex h-full w-full bg-[#FAF8F6]">
        <Layer className="font-resource-han-rounded">
          <LayerItem index={2} className="w-full">
            <div
              ref={refContainer}
              onClick={() => {
                selectedLine.value = null;
              }}
              data-name="guide-container"
              className={cn(
                "relative h-full w-full touch-none overflow-y-auto scroll-smooth",
                scrollable.value ? "touch-auto" : "touch-none"
              )}
            >
              <GuideSectionH2 />
              <BtnGoto />
            </div>
          </LayerItem>
          <LayerItem
            index={1}
            className="max-w-1/5 right-0 w-[calc(100%-var(--width-guide))]"
          >
            <div className="relative flex h-full w-full flex-col items-center justify-end text-blue-800">
              {avatar.url && (
                <Video
                  src={data.blobUrl || avatar.url}
                  pauseWhenBuffering
                  crossOrigin="anonymous"
                  onError={(e) => {
                    console.log(e);
                  }}
                />
              )}
            </div>
          </LayerItem>
        </Layer>
        {subtitles && showSubtitle && (
          <div className="right-30 fixed bottom-8 left-1/2 z-20 -translate-x-[50%]">
            <Subtitle subtitles={subtitles} />
          </div>
        )}
      </div>
    </AbsoluteFill>
  );
};
