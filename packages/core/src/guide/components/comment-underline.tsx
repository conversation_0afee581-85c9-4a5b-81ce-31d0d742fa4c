import { MergedReference } from "@repo/core/types/data/comment";
import { cn } from "@repo/ui/lib/utils";
import { FC, PropsWithChildren } from "react";

export const CommentUnderline: FC<
  PropsWithChildren<{
    mergedReference?: MergedReference[string][string][number];
    onClick?: (ref: MergedReference[string][string][number]) => void;
    className?: string;
    lineId?: string;
    textureId?: string;
    rootId?: string;
    charId?: number;
    content?: string;
  }>
> = ({
  className,
  children,
  onClick,
  mergedReference,
  lineId,
  textureId,
  rootId,
  charId,
  content,
}) => (
  <span
    className={cn("relative", className)}
    onClick={() => mergedReference && onClick?.(mergedReference)}
    data-line-id={lineId}
    data-texture-id={textureId}
    data-char-id={charId}
    data-content={content}
    data-root-id={rootId}
  >
    {children}
    {mergedReference && (
      <span className="pointer-events-none absolute -bottom-1 left-0 h-0.5 w-full bg-[repeating-linear-gradient(to_right,var(--color-text-5)_-1px,var(--color-text-5)_4px,transparent_4px,transparent_6px)]" />
    )}
    {mergedReference?.isLast && mergedReference.end === charId && (
      <span className="z-1 absolute -bottom-2.5 left-full flex translate-x-0.5 items-center justify-center rounded-sm bg-[#A5A5A4] px-1 text-[8px] text-white">
        {mergedReference.commentCount}
      </span>
    )}
  </span>
);
