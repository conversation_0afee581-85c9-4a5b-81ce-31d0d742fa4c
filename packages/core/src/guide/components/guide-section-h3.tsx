import { DrawElement, Line } from "@repo/core/types/data/widget-guide";
import { FC } from "react";
import { useCurrentFrame } from "remotion";
import {
  SectionH3Provider,
  useSectionH3Context,
} from "../context/section-h3-context";
import { useSketchCanvasRef } from "../context/sketch-canvas-context";
import { HandDrawn } from "./guide-hand-drawn";
import { GuideSectionH4 } from "./guide-section-h4";
import { SketchBoard } from "./sketch-board";

const Draw = () => {
  const { h3Line, sketchProps, partFrames } = useSectionH3Context();
  const { onDrawChange, eraserMode, highlighter } = sketchProps || {};
  const { canvasRef, strokeColor, strokeWidth } = useSketchCanvasRef();
  const frame = useCurrentFrame();
  if (!h3Line) {
    return null;
  }

  const { id, draw } = h3Line;

  if (sketchProps?.mode !== "draw" && draw) {
    return <HandDrawn data={draw} frame={frame} partFrames={partFrames} />;
  }

  if (sketchProps?.mode === "draw") {
    return (
      <SketchBoard
        startFrame={sketchProps?.startFrame || 0}
        outFrame={sketchProps?.outFrame || 0}
        itemId={id ?? ""}
        className="z-100 absolute left-0 top-0 h-full w-full"
        svgData={""}
        // readOnly={isReadOnly}
        onChange={onDrawChange}
        ref={canvasRef}
        strokeColor={strokeColor}
        strokeWidth={strokeWidth}
        allPaths={draw || []}
        animated={true}
        eraserMode={eraserMode}
        highlighter={highlighter}
      />
    );
  }
};

const SectionH3: FC = () => {
  const { ref, parts } = useSectionH3Context();

  return (
    <section
      data-name="section::h3"
      ref={ref}
      className="relative flex flex-col gap-6"
    >
      {parts.map((part, index) => (
        <GuideSectionH4 key={index} data={part} />
      ))}

      <Draw />
    </section>
  );
};

export const GuideSectionH3: FC<{
  data: Line[];
  sketchProps?: {
    onDrawChange?: (paths: DrawElement[] | null) => void;
    mode?: "draw" | "edit";
    changeLine?: (draw: string, id: string) => void;
    startFrame?: number;
    outFrame?: number;
    duration?: number;
    eraserMode?: boolean;
    highlighter?: boolean;
  };
  parentStyleText?: number;
}> = (props) => {
  return (
    <SectionH3Provider {...props}>
      <SectionH3 />
    </SectionH3Provider>
  );
};
