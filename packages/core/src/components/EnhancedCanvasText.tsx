"use client";

import React, { useEffect, useRef } from "react";

interface EnhancedCanvasTextProps {
  text: string;
  fontSize?: number;
  fontFamily?: string;
  fontWeight?: string | number;
  color?: string; // 单一颜色，优先级高于渐变色
  gradientColors?: string[];
  strokeColor?: string;
  strokeWidth?: number;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 增强版Canvas文字组件
 * 专门优化描边效果和文字清晰度
 */
export const EnhancedCanvasText: React.FC<EnhancedCanvasTextProps> = ({
  text,
  fontSize = 32,
  fontFamily = "ResourceHanRounded",
  fontWeight = 900,
  color,
  gradientColors = ["#FF5416", "#FF50A4"],
  strokeColor = "#FFFFFF",
  strokeWidth = 3,
  className,
  style,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // 获取设备像素比，提高清晰度
    const dpr = window.devicePixelRatio || 1;
    const scaleFactor = Math.max(dpr, 3); // 提高到3倍分辨率，改善细节

    // 设置字体
    const scaledFontSize = fontSize * scaleFactor;
    ctx.font = `${fontWeight} ${scaledFontSize}px ${fontFamily}`;

    // 测量文字尺寸
    const metrics = ctx.measureText(text);
    const textWidth = metrics.width / scaleFactor;
    const textHeight = fontSize * 1.2; // 增加高度以容纳字体的上下部分

    // 设置canvas尺寸（考虑描边宽度）
    const padding = strokeWidth * 4; // 减少padding，避免过度空间
    const canvasWidth = textWidth + padding * 2;
    const canvasHeight = textHeight + padding * 2;

    // 设置实际canvas尺寸（高分辨率）
    canvas.width = canvasWidth * scaleFactor;
    canvas.height = canvasHeight * scaleFactor;

    // 设置显示尺寸
    canvas.style.width = canvasWidth + "px";
    canvas.style.height = canvasHeight + "px";

    // 缩放上下文
    ctx.scale(scaleFactor, scaleFactor);

    // 重新设置字体，优化字体渲染
    ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`;
    ctx.textBaseline = "middle"; // 改为middle，更好的垂直对齐
    ctx.textAlign = "left";

    // 计算文字位置
    const x = padding;
    const y = canvasHeight / 2; // 垂直居中

    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // 设置高质量文字渲染
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = "high";

    // 单层描边，避免重叠导致的模糊
    if (strokeWidth > 0) {
      ctx.lineJoin = "miter"; // 改为直角连接
      ctx.lineCap = "butt"; // 改为直线端点
      ctx.miterLimit = 2; // 设置合适的尖角限制
      ctx.strokeStyle = strokeColor;
      ctx.lineWidth = strokeWidth * 1.8; // 保持适中的描边宽度
      ctx.globalAlpha = 1;
      ctx.strokeText(text, x, y);
    }

    // 重置透明度
    ctx.globalAlpha = 1;

    // 设置填充样式 - 优先使用单一颜色，否则使用渐变
    if (color) {
      // 使用单一颜色
      ctx.fillStyle = color;
    } else if (gradientColors.length > 1) {
      // 创建渐变 - 从左到右（需要至少2个颜色）
      const gradient = ctx.createLinearGradient(0, 0, textWidth, 0);
      gradientColors.forEach((gradientColor, index) => {
        const position =
          gradientColors.length === 1 ? 0 : index / (gradientColors.length - 1);
        gradient.addColorStop(position, gradientColor);
      });
      ctx.fillStyle = gradient;
    } else if (gradientColors.length === 1 && gradientColors[0]) {
      // 只有一个渐变色时，直接使用该颜色
      ctx.fillStyle = gradientColors[0];
    } else {
      // 没有颜色时使用默认颜色
      ctx.fillStyle = "#000000";
    }

    // 添加轻微阴影增强立体感，但不影响文字清晰度
    ctx.shadowColor = "rgba(0, 0, 0, 0.15)";
    ctx.shadowBlur = 1;
    ctx.shadowOffsetX = 0.5;
    ctx.shadowOffsetY = 0.5;

    // 单次填充，保持文字清晰
    ctx.fillText(text, x, y);

    // 清除阴影
    ctx.shadowColor = "transparent";
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
  }, [
    text,
    fontSize,
    fontFamily,
    fontWeight,
    color,
    gradientColors,
    strokeColor,
    strokeWidth,
  ]);

  return (
    <canvas
      ref={canvasRef}
      className={className}
      style={{
        display: "block",
        ...style,
      }}
    />
  );
};
