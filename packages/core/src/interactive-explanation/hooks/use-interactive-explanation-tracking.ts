/**
 * 互动讲题埋点 Hook
 * 参考 trackEventWithExercise 的使用方式
 */

import { trackEvent } from '@repo/core/utils/stu/device';
import { useCallback } from 'react';

// ===== 类型定义 =====

/** 环节类型 */
export type StepType = "question_analysis" | "key_point_analysis" | "step_explanation" | "summary";

/** 埋点函数类型 */
export interface TrackEventFunction {
  (eventName: string, params: Record<string, unknown>): void;
}

/** Hook 参数 */
export interface UseInteractiveExplanationTrackingParams {
  /** 课程ID */
  lessonId: string | number;
  /** 题目ID */
  questionId: string | number;
  /** 埋点函数 */
  trackEvent: TrackEventFunction;
}

// ===== 埋点事件名称常量 =====
export const INTERACTIVE_EXPLANATION_EVENTS = {
  ENTER_INTERACTIVE_EXPLANATION: "enter_interactive_explanation",
  EXIT_INTERACTIVE_EXPLANATION: "exit_interactive_explanation",
  STEP_VIEW: "step_view",
  STEP_JUMP: "step_jump",
  STEP_EXIT: "step_exit",
} as const;

export type InteractiveExplanationEventName = typeof INTERACTIVE_EXPLANATION_EVENTS[keyof typeof INTERACTIVE_EXPLANATION_EVENTS];

// ===== 不同事件的参数类型定义 =====

/** 进入/退出互动讲题参数（无额外参数） */
export type EnterExitParams = Record<string, never>;

/** 环节浏览参数 */
export type StepViewParams = {
  step_type: StepType;
  step_index: number;
  stay_time: number;
};

/** 环节跳转参数 */
export type StepJumpParams = {
  from_step_type: StepType;
  to_step_type: StepType;
  from_step_index: number;
  to_step_index: number;
};

/** 环节跳出参数 */
export type StepExitParams = {
  step_type: StepType;
  step_index: number;
};

// ===== 事件参数映射 =====
export type EventParamsMap = {
  [INTERACTIVE_EXPLANATION_EVENTS.ENTER_INTERACTIVE_EXPLANATION]: EnterExitParams;
  [INTERACTIVE_EXPLANATION_EVENTS.EXIT_INTERACTIVE_EXPLANATION]: EnterExitParams;
  [INTERACTIVE_EXPLANATION_EVENTS.STEP_VIEW]: StepViewParams;
  [INTERACTIVE_EXPLANATION_EVENTS.STEP_JUMP]: StepJumpParams;
  [INTERACTIVE_EXPLANATION_EVENTS.STEP_EXIT]: StepExitParams;
};

// ===== 主要 Hook =====

/**
 * 互动讲题埋点 Hook
 * 参考 trackEventWithExercise 的使用方式
 * 
 * @param params Hook 参数
 * @returns 埋点方法
 */
export const useInteractiveExplanationTracking = ({
  lessonId,
  questionId,
}: UseInteractiveExplanationTrackingParams) => {

  // 统一的埋点方法，类似 trackEventWithExercise，根据不同 eventName 处理不同参数类型
  const trackInteractiveExplanationEvent = useCallback(
    <T extends InteractiveExplanationEventName>(
      eventName: T,
      additionalParams: EventParamsMap[T] = {} as EventParamsMap[T]
    ) => {
      const baseParams = {
        lesson_id: String(lessonId),
        question_id: String(questionId),
        ...additionalParams,
      };

      trackEvent(eventName, baseParams);
      console.log(`[InteractiveExplanationTracking] 📊 ${eventName}`, baseParams);
    },
    [lessonId, questionId, trackEvent]
  );

  return trackInteractiveExplanationEvent;
};

export default useInteractiveExplanationTracking;