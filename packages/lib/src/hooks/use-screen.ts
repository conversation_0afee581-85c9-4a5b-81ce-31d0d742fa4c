"use client";
import { useEffect, useState } from "react";
import { getScreenSize } from "../utils/device";

function useScreen() {
  const deviceScreen = getScreenSize();

  const [screen, setScreen] = useState<{ width: number; height: number }>(
    deviceScreen || { width: 1143, height: 629 }
  );

  useEffect(() => {
    const handleResize = () => {
      if (deviceScreen) {
        setScreen(deviceScreen);
      } else {
        setScreen({ width: window.innerWidth, height: window.innerHeight });
      }
    };
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [deviceScreen]);

  return screen;
}

export default useScreen;
