#!/bin/bash

# 定义颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 输出带颜色的信息函数
info() {
    echo -e "\n${BLUE}[信息]${NC} $1"
}

success() {
    echo -e "\n${GRE<PERSON>}[成功]${NC} $1"
}

error() {
    echo -e "\n${RED}[错误]${NC} $1"
}

warning() {
    echo -e "\n${YELLOW}[警告]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        error "命令 '$1' 未找到"
        return 1
    fi
    return 0
}

# SSH连接控制目录
SSH_CONTROL_DIR="/tmp/ssh_mux_$(whoami)"
SSH_CONTROL_PATH="$SSH_CONTROL_DIR/%h_%p_%r"

# 初始化SSH连接复用
init_ssh_mux() {
    mkdir -p "$SSH_CONTROL_DIR"
    chmod 700 "$SSH_CONTROL_DIR"
    
    # 添加连接复用选项
    SSH_MUX_OPTIONS="-o ControlMaster=auto -o ControlPath=$SSH_CONTROL_PATH -o ControlPersist=10m"
    SSH_OPTIONS="$SSH_OPTIONS $SSH_MUX_OPTIONS"
    
    info "初始化SSH连接复用..."
}

# 关闭SSH连接复用
close_ssh_mux() {
    info "关闭SSH连接..."
    if [ -d "$SSH_CONTROL_DIR" ]; then
        if sshpass -p "$PASSWORD" ssh -O exit $SSH_OPTIONS "$REMOTE_HOST" 2>/dev/null; then
            success "SSH连接已关闭"
        fi
    fi
}

# 执行远程命令
remote_exec() {
    local cmd="$1"
    
    if ! sshpass -p "$PASSWORD" ssh $SSH_OPTIONS "$REMOTE_HOST" "$cmd"; then
        error "远程命令执行失败: $cmd"
        return 1
    fi
    return 0
}

# 执行多个远程命令（在一个SSH会话中）
remote_exec_multi() {
    local commands="$1"
    
    if ! sshpass -p "$PASSWORD" ssh $SSH_OPTIONS "$REMOTE_HOST" "$commands"; then
        error "远程命令执行失败"
        return 1
    fi
    return 0
}

# 检查远程连接
check_remote_connection() {
    info "检查远程连接..."
    if ! sshpass -p "$PASSWORD" ssh $SSH_OPTIONS -q "$REMOTE_HOST" "exit" 2>/dev/null; then
        error "无法连接到远程服务器，请检查："
        echo "1. 服务器地址是否正确"
        echo "2. SSH 密码是否正确"
        echo "3. 服务器是否在线"
        echo "4. SSH 服务是否正常运行"
        return 1
    fi
    success "远程服务器连接正常"
    return 0
}

# 检查并安装 sshpass
install_sshpass() {
    if ! check_command sshpass; then
        warning "未检测到 sshpass，正在安装..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            if ! check_command brew; then
                warning "需要先安装 Homebrew..."
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            fi
            brew install sshpass
        elif check_command apt-get; then
            sudo apt-get update
            sudo apt-get install -y sshpass
        elif check_command yum; then
            sudo yum install -y sshpass
        else
            error "无法自动安装 sshpass，请手动安装后重试"
            exit 1
        fi
    fi
}

# 定义远程服务器信息
REMOTE_HOST="<EMAIL>"
REMOTE_PATH="/home/<USER>/app/schoolroom"
PASSWORD="Yhs8jCfcys"

# SSH 和 SCP 的通用选项
SSH_OPTIONS="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=10 -o LogLevel=ERROR"

# 获取完整参数
FULL_ARG="$1"  # 直接获取第一个参数 "-F=tch"

# 主函数
main() {
    echo -e "\n📦 开始部署项目...\n"
    
    # 1. 检查必要的命令
    install_sshpass
    check_command zip || { error "需要 zip"; exit 1; }
    check_command scp || { error "需要 scp"; exit 1; }
    check_command rsync || { error "需要 rsync"; exit 1; }
    
    # 初始化SSH连接复用
    init_ssh_mux
    
    # 2. 打包文件
    info "打包文件..."
    local temp_dir=$(mktemp -d)
    local zip_file="deploy_$(date +%Y%m%d_%H%M%S).zip"
    trap 'rm -rf "$temp_dir"; rm -f "$zip_file"' EXIT
    
    # 复制文件到临时目录，排除不需要的文件
    rsync -a --quiet \
          --exclude='node_modules' \
          --exclude='.pnpm' \
          --exclude='.git' \
          --exclude='.husky' \
          --exclude='dist' \
          --exclude='.next' \
          --exclude='.turbo' \
          --exclude='coverage' \
          --exclude='.DS_Store' \
          --exclude='*.log' \
          --exclude='*.cookie' \
          . "$temp_dir/"
    
    # 在临时目录中创建zip文件
    if ! (cd "$temp_dir" && zip -rq "$OLDPWD/$zip_file" .); then
        error "打包文件失败"
        rm -f "$zip_file"
        exit 1
    fi
    success "打包文件完成"

    # 3. 检查远程连接
    check_remote_connection || exit 1
    
    # 4. 确保远端目录存在
    info "确保远端目录存在..."
    remote_exec "mkdir -p $REMOTE_PATH" || exit 1
    
    # 5. 上传zip文件
    info "上传文件到远程服务器..."
    if ! sshpass -p "$PASSWORD" scp $SSH_OPTIONS "$zip_file" "$REMOTE_HOST:$REMOTE_PATH/"; then
        error "上传文件失败"
        rm -f "$zip_file"
        exit 1
    fi
    
    # 6. 使用rsync方式部署文件（解压到临时目录，然后同步并删除多余文件）
    info "部署文件（保留dist、.next和node_modules目录）..."
    
    # 直接使用SSH命令执行，保留原始输出
    sshpass -p "$PASSWORD" ssh $SSH_OPTIONS "$REMOTE_HOST" "
        # 创建临时目录
        TMP_DIR=\$(mktemp -d)
        
        # 解压到临时目录
        unzip -q $REMOTE_PATH/$zip_file -d \$TMP_DIR
        
        # 使用rsync同步文件，保留特定目录，删除多余文件
        rsync -a --delete --force \\
              --exclude='dist' \\
              --exclude='.next' \\
              --exclude='node_modules' \\
              --exclude='node_modules/**' \\
              \$TMP_DIR/ $REMOTE_PATH/
        
        # 清理临时目录和zip文件
        rm -rf \$TMP_DIR
        rm -f $REMOTE_PATH/$zip_file
        
        echo '部署文件完成'
    " || {
        error "部署文件失败"
        exit 1
    }
    
    # 7. 在远程服务器上构建项目（直接使用SSH命令保留原始输出）
    info "在远程服务器上构建项目..."
    sshpass -p "$PASSWORD" ssh $SSH_OPTIONS "$REMOTE_HOST" "bash -l -c 'cd $REMOTE_PATH && pnpm i && pnpm deploy:dev $FULL_ARG'" || {
        error "远程项目构建失败"
        exit 1
    }
    
    # 清理本地zip文件
    rm -f "$zip_file"
    success "文件上传和构建完成"
    
    success "远程项目构建完成"
    
    # 关闭SSH连接复用
    close_ssh_mux
    
    echo -e "\n✨ $FULL_ARG 部署完成！\n"
}

# 执行主函数
main